const { sql, getPool } = require('../db');
const { coa31Columns } = require('../routes/utils/constant');

const productController = {
    createProduct: async (req, res) => {
        try {
            const fields = Object.keys(req.body);
            const values = fields.map(field => `@${field}`).join(', ');
            const columns = fields.join(', ');

            const query = `
                INSERT INTO Coa31 (${columns})
                VALUES (${values});
            `;

            const pool = await getPool();
            const request = new sql.Request(pool);

            fields.forEach(field => {
                const value = req.body[field];
                const column = coa31Columns.find(col => col.name === field);
                if (column) {
                    if (column.type === sql.VarChar && column.length) {
                        request.input(field, sql.VarChar(column.length), value || null);
                    } else {
                        request.input(field, column.type, value || null);
                    }
                }
            });

            await request.query(query);

            res.status(201).json({
                message: 'Product created successfully',
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },
    getNextProductId: async (req, res) => {
        try {
            const query = `
                SELECT MAX(CAST(id AS INT)) AS maxId
                FROM Coa31;
            `;

            const pool = await getPool();
            const request = new sql.Request(pool);
            const result = await request.query(query);

            const maxId = result.recordset[0].maxId || 0;
            const nextId = (maxId + 1).toString().padStart(10, '0');

            res.status(200).json({
                message: 'Next product ID generated successfully',
                nextId: nextId
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },
    getProductById: async (req, res) => {
        try {
            const { id } = req.params;

            const query = `
                SELECT 
                    C.*, 
                    Categories.Title AS CategoryName,
                    Manufacturers.Title AS ManufacturerName,
                    PSalesType.Title AS PTypeName,
                    Units.ID AS UnitName,
                    Coa32P.Title AS PurchaseAccountName,
                    Coa32S.Title AS SaleAccountName,
                    Coa32O.Title AS OpeningAccountName,
                    Users.Title AS PreparedByName,
                    Coa32Party.Title AS PartyName,
                    CostCenters.Title AS CostCenterName,
                    Designs.Title AS DesignName,
                    SizeGroups.ID AS SizeGroupName,
                    Godown.Title AS LocationName,
                    DefGovtCoa31.Title AS GovtName,
                    StandardBallTypes.Title AS BallTypeName,
                    PackUnit.ID AS PackUnitName,
                    Coa32FA6.Title AS FinancialAccountName,
                    Currencies.ID AS CurrencyName,
                    CartonTypes.ID AS CartonTypeName,
                    Shapes.ID AS ShapeName
                FROM Coa31 C
                LEFT JOIN Categories ON C.Category_ID = Categories.Id
                LEFT JOIN Manufacturers ON C.Manufacturer_ID = Manufacturers.Id
                LEFT JOIN PSalesType ON C.PType = PSalesType.id
                LEFT JOIN Units ON C.Unit = Units.ID
                LEFT JOIN Coa32 AS Coa32P ON C.Purchase_ID = Coa32P.id
                LEFT JOIN Coa32 AS Coa32S ON C.Sale_ID = Coa32S.id
                LEFT JOIN Coa32 AS Coa32O ON C.OpnAcc_ID = Coa32O.id
                LEFT JOIN Users ON C.prp_id = Users.id
                LEFT JOIN Coa32 AS Coa32Party ON C.Party_ID = Coa32Party.id
                LEFT JOIN CostCenters ON C.CostCenter_ID = CostCenters.ID
                LEFT JOIN Designs ON C.Design_ID = Designs.ID
                LEFT JOIN SizeGroups ON C.SizeGroup_ID = SizeGroups.ID
                LEFT JOIN Godown ON C.Location = Godown.Id
                LEFT JOIN DefGovtCoa31 ON C.Govt_ID = DefGovtCoa31.ID
                LEFT JOIN StandardBallTypes ON C.FG_BallType = StandardBallTypes.ID
                LEFT JOIN Units AS PackUnit ON C.PackUnit = PackUnit.ID
                LEFT JOIN Coa32 AS Coa32FA6 ON C.FinAcc06_ID = Coa32FA6.id
                LEFT JOIN Currencies ON C.Currency_ID = Currencies.ID
                LEFT JOIN CartonTypes ON C.FG_CartonType = CartonTypes.ID
                LEFT JOIN Units AS Shapes ON C.Shape = Shapes.ID
                WHERE C.id = @id;
            `;

            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('id', sql.VarChar(32), id);

            const result = await request.query(query);

            if (result.recordset.length === 0) {
                return res.status(404).json({ message: 'Product not found' });
            }

            res.status(200).json({
                message: 'Product retrieved successfully',
                product: result.recordset[0]
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },
    deleteProduct: async (req, res) => {
        try {
            const { id } = req.params;

            const query = `
                DELETE FROM Coa31
                WHERE id = @id;
            `;

            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('id', sql.VarChar(32), id);

            const result = await request.query(query);

            if (result.rowsAffected[0] === 0) {
                return res.status(404).json({ message: 'Product not found' });
            }

            res.status(200).json({
                message: 'Product deleted successfully'
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    }
};

module.exports = productController;