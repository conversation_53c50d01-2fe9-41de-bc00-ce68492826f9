const express = require("express");
const router = express.Router();
const nodemailer = require("../services/email");
const { QUOTATION_EMAIL_CONTENT } = require("./utils/Email");
const { getPool, sql } = require("../db");
const zlib = require('zlib');
const multer = require('multer');
const dayjs = require("dayjs");
const upload = multer();

router.post("/quotation-email", async (req, res) => {
    try {
        const { to, subject, clientName, quotation, voucher_no } = req.body;
        if (!to || !subject || !clientName || !quotation || !voucher_no) {
            return res.status(400).json({ message: "to, subject, clientName, voucher_no and quotation are required" });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        request.input('voucher_no', sql.Var<PERSON>har(120), voucher_no);

        const query = `
            SELECT FileName, FileData
            FROM Client_Lead_PDFs
            WHERE Voucher_No = @voucher_no
        `;

        request.query(query, async (err, result) => {
            if (err) {
                return res.status(500).send(err);
            }

            if (result.recordset.length === 0) {
                return res.status(404).send('PDF file not found.');
            }

            const pdf = result.recordset[0];
            const decompressedPdf = zlib.gunzipSync(pdf.FileData);
            const attachment = {
                filename: pdf.FileName,
                content: decompressedPdf,
                contentType: 'application/pdf'
            };
            await nodemailer.sendMail(to, subject, QUOTATION_EMAIL_CONTENT(quotation, clientName), [attachment]);
            res.status(200).json({ message: "Email sent successfully" });
        });
    } catch (error) {
        console.error("Error sending email:", error);
        res.status(500).json({ message: "Failed to send email", error: error.message });
    }
});

router.post("/quotation-email-with-file", upload.single('pdf'), async (req, res) => {
    try {
        const { to, subject, clientName, quotation } = req.body;
        if (!to || !subject || !clientName || !quotation || !req.file) {
            return res.status(400).json({ message: "to, subject, clientName, quotation and pdf file are required" });
        }

        const attachment = {
            filename: req.file.originalname,
            content: req.file.buffer,
            contentType: 'application/pdf'
        };

        await nodemailer.sendMail(to, subject, QUOTATION_EMAIL_CONTENT(quotation, clientName), [attachment]);
        res.status(200).json({ message: "Email sent successfully" });
    } catch (error) {
        console.error("Error sending email:", error);
        res.status(500).json({ message: "Failed to send email", error: error.message });
    }
});

module.exports = router;
