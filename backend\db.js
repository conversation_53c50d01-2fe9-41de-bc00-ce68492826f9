const sql = require("mssql");
require("dotenv").config();

const dbConfig = {
    user: process.env.USER,
    password: process.env.PASSWORD,
    server: process.env.SERVER,
    database: process.env.DATABASE,
    options: {
        encrypt: false,
        enableArithAbort: true,
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000,
    },
};

let pool;
let connectionPromise;

const connectToDb = async () => {
    try {
        pool = await sql.connect(dbConfig);
        console.log('Database connected successfully');
        pool.on('error', async (err) => {
            console.error('Database connection lost, attempting to reconnect...', err);
            await reconnect();
        });
        connectionPromise = Promise.resolve(pool);
    } catch (err) {
        console.error('Database connection error: ', err);
        connectionPromise = Promise.reject(err);
        setTimeout(connectToDb, 5000);
    }
};

const reconnect = async () => {
    try {
        pool = await sql.connect(dbConfig);
        console.log('Database reconnected successfully');
        connectionPromise = Promise.resolve(pool);
    } catch (err) {
        console.error('Reconnection attempt failed, retrying...', err);
        connectionPromise = Promise.reject(err);
        setTimeout(reconnect, 5000);
    }
};

const getPool = async () => {
    if (!connectionPromise) {
        throw new Error('Database connection not established');
    }
    return connectionPromise;
};

connectToDb();

module.exports = {
    sql,
    getPool
};
