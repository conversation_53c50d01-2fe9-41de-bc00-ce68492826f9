import { 
  Modal, 
  ModalOverlay, 
  ModalContent, 
  Modal<PERSON>eader, 
  ModalCloseButton, 
  ModalBody, 
  useColorModeValue, 
  Box, 
  Table, 
  Thead, 
  Tbody, 
  Tr, 
  Th, 
  Td,
  Input,
  InputGroup,
  InputLeftElement,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FiSearch } from 'react-icons/fi';
import { keyframes } from "@emotion/react";
import "@components/Custom/Modal/Modal.css";

// Define animations
const slideIn = keyframes`
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
`;

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

export default function CustomModal({ onClose, tableData, tableHeaders, handleRowClick }) {
    const [searchTerm, setSearchTerm] = useState("");

    const filteredData = tableData.filter(row =>
        Object.values(row).some(value => {
            if (value) {
                return value.toString().toLowerCase().includes(searchTerm.toLowerCase())
            }
            return false;
        })
    );

    return (
        <Modal 
            isOpen={true} 
            onClose={onClose} 
            isCentered
            motionPreset="scale"
        >
            <ModalOverlay 
                bg="blackAlpha.300"
                backdropFilter="blur(10px)"
                sx={{
                    animation: `${fadeIn} 0.2s ease-out`
                }}
            />
            <ModalContent
                maxW={{ base: "90%", sm: "90%", md: "700px", lg: "800px", xl: "900px" }}
                w="100%"
                h="80%"
                sx={{
                    animation: `${slideIn} 0.3s ease-out`,
                    bg: "white",
                    boxShadow: "xl",
                }}
            >
                <ModalHeader
                    bgGradient="linear(to-r, #3a866a, #2d6651)"
                    color="white"
                    borderTopRadius="md"
                    px={6}
                    py={4}
                >
                    <InputGroup>
                        <InputLeftElement pointerEvents="none">
                            <FiSearch color="white" />
                        </InputLeftElement>
                        <Input
                            type="text"
                            placeholder={"Search " + tableHeaders.join(', ') + "..."}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            border="1px solid"
                            borderColor="whiteAlpha.300"
                            _hover={{ borderColor: "whiteAlpha.400" }}
                            _focus={{ 
                                borderColor: "whiteAlpha.500",
                                boxShadow: "0 0 0 1px rgba(255,255,255,0.5)"
                            }}
                            color="white"
                            _placeholder={{ color: "whiteAlpha.700" }}
                            transition="all 0.2s"
                        />
                    </InputGroup>
                </ModalHeader>
                <ModalCloseButton 
                    color="white" 
                    _hover={{
                        bg: "whiteAlpha.300",
                        transform: "rotate(90deg)"
                    }}
                    transition="all 0.2s"
                />
                <ModalBody 
                    p={0}
                    sx={{
                        "&::-webkit-scrollbar": {
                            width: "6px",
                        },
                        "&::-webkit-scrollbar-track": {
                            background: "#f1f1f1",
                            borderRadius: "4px",
                        },
                        "&::-webkit-scrollbar-thumb": {
                            background: "#3a866a",
                            borderRadius: "4px",
                            "&:hover": {
                                background: "#2d6651",
                            },
                        },
                    }}
                >
                    <Box height="100%" overflow="auto" p={4}>
                        <Table variant="simple">
                            <Thead>
                                {tableHeaders?.length === 0 && (
                                    <Tr>
                                        <Th 
                                            textAlign="center" 
                                            color="gray.500"
                                            fontStyle="italic"
                                        >
                                            No headers found
                                        </Th>
                                    </Tr>
                                )}
                                {tableHeaders?.length !== 0 && (
                                    <Tr 
                                        bgGradient="linear(to-r, #3a866a, #2d6651)"
                                    >
                                        {tableHeaders?.map((header, index) => (
                                            <Th 
                                                key={index} 
                                                color="white"
                                                borderRight={index !== tableHeaders.length - 1 ? "1px solid" : "none"}
                                                borderColor="whiteAlpha.300"
                                                py={4}
                                            >
                                                {header}
                                            </Th>
                                        ))}
                                    </Tr>
                                )}
                            </Thead>
                            <Tbody>
                                {filteredData?.length === 0 && (
                                    <Tr>
                                        <Td 
                                            colSpan={tableHeaders.length} 
                                            textAlign="center"
                                            color="gray.500"
                                            py={8}
                                        >
                                            No items found
                                        </Td>
                                    </Tr>
                                )}
                                {filteredData?.length !== 0 && filteredData?.map((data, index) => (
                                    <Tr 
                                        key={index} 
                                        onClick={() => handleRowClick(data)} 
                                        className="modalRow"
                                        _hover={{
                                            bg: "gray.50",
                                            cursor: "pointer",
                                        }}
                                        transition="background 0.2s"
                                    >
                                        {Object.values(data)?.map((value, index) => (
                                            <Td 
                                                key={index}
                                                borderBottom="1px solid"
                                                borderColor="gray.100"
                                                py={3}
                                            >
                                                {value}
                                            </Td>
                                        ))}
                                    </Tr>
                                ))}
                            </Tbody>
                        </Table>
                    </Box>
                </ModalBody>
            </ModalContent>
        </Modal>
    );
}