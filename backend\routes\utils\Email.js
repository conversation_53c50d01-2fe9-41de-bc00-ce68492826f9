const dayjs = require("dayjs");

const logo = "https://i.ibb.co/Z1Hyh7PQ/header.png";
const privacy = "https://nexsoltech.biz/";
const terms = "https://nexsoltech.biz/";

const QUOTATION_EMAIL_CONTENT = (quotationDetails, clientName) => {
    const date = dayjs().format('DD-MMM-YYYY HH:MM:ss');
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body { font-family: Arial, sans-serif; background-color: #C4C4C400; margin: 0; padding: 0; }
            table { border-spacing: 0; width: 100%; margin: 0 auto; }
            .date { color: black; font-size: 16px; font-weight: bold; text-align: right }
            .content { background: #FFFFFF; border-radius: 8px; padding: 20px; width: 80%; margin: 0 auto; z-index: 2; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important; }
            .content h1 { color: #333; font-size: 24px; margin-bottom: 10px; text-align: center; }
            .content p { color: #666666; font-size: 16px; margin-bottom: 20px; text-align: center; }
            .quotation-box { font-size: 18px; color: #333; padding: 10px 20px; text-align: center; }
            .footer { font-size: 12px; color: #999; text-align: center; padding: 20px; }
            .footer p { margin: 5px 0; }
            @media (max-width: 768px) {
                .content { width: 90%; }
                .content h1 { font-size: 20px; }
                .content p { font-size: 14px; }
                .quotation-box { font-size: 16px; }
                .footer { font-size: 10px; }
                .date { font-size: 14px; }
            }
        </style>
    </head>
    <body>
        <table style="width: 100%;" cellpadding="0" cellspacing="0">
            <tr><td><img style="width:100% ; height:auto" src=${logo} alt="Background" class="background"></td></tr>
            <tr>
                <td>
                    <table class="content" style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">
                        <tr><td class="date">${date}</td></tr>
                        <tr>
                            <td>
                                <table>
                                    <tr>
                                        <td>
                                            <h1>Dear ${clientName},</h1>
                                            <p>We are pleased to provide you with the following quotation. Please review the details below:</p>
                                        </td>
                                    </tr>
                                    <tr><td class="quotation-box">${quotationDetails}</td></tr>
                                    <tr>
                                        <td style="text-align: center; font-size: 12px; color: #666666;">
                                            <p><strong>DISCLAIMER:</strong> This email and any files transmitted with it are intended only for the use of the recipient and may contain information which is privileged and/or confidential under applicable law. If you are not the intended recipient or such recipient's employee or agent, you are hereby notified that any unauthorized use, dissemination, distribution, copy, or disclosure of this communication is strictly prohibited.</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr><td><table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 10px; width: 80%;"></table></td></tr>
            <tr>
                <td>
                    <table class="footer">
                        <tr>
                            <td>
                                <p>If you have any questions, feel free to message us at <strong><EMAIL></strong>. All rights reserved. Update email preferences or unsubscribe.</p>
                                <p>Lahore, Pakistan</p>
                                <p>
                                    <a href=${terms} style="color: #666666;">Terms of use</a> |
                                    <a href=${privacy} style="color: #666666;">Privacy Policy</a>
                                </p>
                                &copy; 2025 NexSol Tech. All Rights Reserved.
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    `;
};

module.exports = {
    QUOTATION_EMAIL_CONTENT
};