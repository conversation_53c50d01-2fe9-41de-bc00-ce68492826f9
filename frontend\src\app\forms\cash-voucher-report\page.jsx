"use client";
import React, { useState, useEffect } from 'react';
import "@src/app/dashboard/dashboard.css";
import {
    Button,
    useToast
} from '@chakra-ui/react';
import axiosInstance from '@src/app/axios';
import ServerPaginatedTable from '@src/components/Custom/ServerPaginatedTable';
import VoucherDetailsModal from './VoucherDetailsModal';
import Loader from '@src/components/Loader/Loader';

const CashVoucherReport = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 20,
        totalCount: 0,
        totalPages: 0
    });
    const [selectedVoucher, setSelectedVoucher] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [voucherDetails, setVoucherDetails] = useState(null);
    const [paymentHistory, setPaymentHistory] = useState([]);
    const [modalLoading, setModalLoading] = useState(false);
    const toast = useToast();

    const fetchData = async (page = 1, pageSize = 20) => {
        try {
            setLoading(true);
            const response = await axiosInstance.get(`cashVoucher/report?page=${page}&pageSize=${pageSize}`);
            console.log(response.data);

            if (response.data && response.data.data) {
                setData(response.data.data);
                setPagination({
                    page: response.data.page,
                    pageSize: response.data.pageSize,
                    totalCount: response.data.totalCount,
                    totalPages: response.data.totalPages
                });
            } else {
                setData([]);
                setPagination({
                    page: 1,
                    pageSize: 20,
                    totalCount: 0,
                    totalPages: 0
                });
            }
        } catch (error) {
            console.error("Error fetching data: ", error);
            toast({
                title: "Error",
                description: "Failed to fetch cash voucher report data",
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchVoucherDetails = async (voucherNo) => {
        try {
            setModalLoading(true);
            const response = await axiosInstance.get(`cashVoucher/voucher-details?voucherNo=${voucherNo}`);

            if (response.data) {
                setVoucherDetails(response.data.voucher_details);
                setPaymentHistory(response.data.payment_history || []);
            }
        } catch (error) {
            console.error("Error fetching voucher details: ", error);
            toast({
                title: "Error",
                description: "Failed to fetch voucher details",
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setModalLoading(false);
        }
    };

    const handleOpenModal = async (voucher) => {
        setSelectedVoucher(voucher);
        setIsModalOpen(true);
        await fetchVoucherDetails(voucher.voucher_no);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedVoucher(null);
        setVoucherDetails(null);
        setPaymentHistory([]);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'green';
            case 'pending': return 'red';
            case 'partial': return 'yellow';
            default: return 'gray';
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount || 0);
    };

    const columns = [
        { header: 'Voucher No', field: 'voucher_no' },
        { header: 'Quotation No (adj)', field: 'quotation_no' },
        { header: 'Client ID', field: 'client_id' },
        { header: 'Client Name', field: 'client_name' },
        {
            header: 'Gross Amount',
            field: 'gross_amount',
            render: (item) => formatCurrency(item.gross_amount)
        },
        {
            header: 'Paid Amount',
            field: 'paid_amount',
            render: (item) => formatCurrency(item.paid_amount)
        },
        {
            header: 'Remaining Amount',
            field: 'remaining_amount',
            render: (item) => formatCurrency(item.remaining_amount)
        },
        { header: 'Status', field: 'status', type: 'badge' },
        {
            header: 'Actions',
            field: 'actions',
            render: (item) => (
                <Button
                    colorScheme="blue"
                    size="sm"
                    onClick={(e) => {
                        e.stopPropagation();
                        handleOpenModal(item);
                    }}
                >
                    View Details
                </Button>
            )
        }
    ];

    const getRowCursor = () => 'pointer';

    const handleRowClick = (item) => {
        handleOpenModal(item);
    };

    const handlePageChange = (newPage) => {
        fetchData(newPage, pagination.pageSize);
    };

    const handlePageSizeChange = (newPageSize) => {
        fetchData(1, newPageSize);
    };

    return (
        <>
            {loading ? (
                <Loader />
            ) : (
                <>
                    <div className="wrapper">
                        <div>
                            <div>
                                <div className="page-inner">
                                    <div className="row">
                                        <div className="bgWhite">
                                            <h1
                                                style={{
                                                    margin: "0",
                                                    textAlign: "center",
                                                    color: "#2B6CB0",
                                                    fontSize: "24px",
                                                    fontWeight: "bold",
                                                    padding: "10px",
                                                }}
                                            >
                                                Cash Voucher Report
                                            </h1>
                                        </div>
                                    </div>
                                    <div className="row">
                                        <ServerPaginatedTable
                                            data={data}
                                            columns={columns}
                                            pagination={pagination}
                                            onPageChange={handlePageChange}
                                            onPageSizeChange={handlePageSizeChange}
                                            onRowClick={handleRowClick}
                                            getRowCursor={getRowCursor}
                                            getBadgeColor={getStatusColor}
                                            loading={loading}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Voucher Details Modal */}
                    <VoucherDetailsModal
                        isOpen={isModalOpen}
                        onClose={handleCloseModal}
                        selectedVoucher={selectedVoucher}
                        voucherDetails={voucherDetails}
                        paymentHistory={paymentHistory}
                        modalLoading={modalLoading}
                    />
                </>
            )}
        </>
    );
}

export default CashVoucherReport;
