"use client";
import React, { useState, useEffect } from 'react';
import "@src/app/dashboard/dashboard.css";
import {
    Box,
    Button,
    useToast,
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalCloseButton,
    ModalBody,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    Badge,
    Text,
    VStack,
    HStack,
    Divider,
    Spinner
} from '@chakra-ui/react';
import axiosInstance from '@src/app/axios';
import ServerPaginatedTable from '@src/components/Custom/ServerPaginatedTable';
import Loader from '@src/components/Loader/Loader';
import dayjs from 'dayjs';

const CashVoucherReport = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 20,
        totalCount: 0,
        totalPages: 0
    });
    const [selectedVoucher, setSelectedVoucher] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [voucherDetails, setVoucherDetails] = useState(null);
    const [paymentHistory, setPaymentHistory] = useState([]);
    const [modalLoading, setModalLoading] = useState(false);
    const toast = useToast();

    const fetchData = async (page = 1, pageSize = 20) => {
        try {
            setLoading(true);
            const response = await axiosInstance.get(`cashVoucher/report?page=${page}&pageSize=${pageSize}`);
            console.log(response.data);

            if (response.data && response.data.data) {
                setData(response.data.data);
                setPagination({
                    page: response.data.page,
                    pageSize: response.data.pageSize,
                    totalCount: response.data.totalCount,
                    totalPages: response.data.totalPages
                });
            } else {
                setData([]);
                setPagination({
                    page: 1,
                    pageSize: 20,
                    totalCount: 0,
                    totalPages: 0
                });
            }
        } catch (error) {
            console.error("Error fetching data: ", error);
            toast({
                title: "Error",
                description: "Failed to fetch cash voucher report data",
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchVoucherDetails = async (voucherNo) => {
        try {
            setModalLoading(true);
            const response = await axiosInstance.get(`cashVoucher/voucher-details?voucherNo=${voucherNo}`);

            if (response.data) {
                setVoucherDetails(response.data.voucher_details);
                setPaymentHistory(response.data.payment_history || []);
            }
        } catch (error) {
            console.error("Error fetching voucher details: ", error);
            toast({
                title: "Error",
                description: "Failed to fetch voucher details",
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setModalLoading(false);
        }
    };

    const handleOpenModal = async (voucher) => {
        setSelectedVoucher(voucher);
        setIsModalOpen(true);
        await fetchVoucherDetails(voucher.voucher_no);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedVoucher(null);
        setVoucherDetails(null);
        setPaymentHistory([]);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'green';
            case 'pending': return 'red';
            case 'partial': return 'yellow';
            default: return 'gray';
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount || 0);
    };

    const columns = [
        { header: 'Voucher No', field: 'voucher_no' },
        { header: 'Quotation No (adj)', field: 'quotation_no' },
        { header: 'Client ID', field: 'client_id' },
        { header: 'Client Name', field: 'client_name' },
        {
            header: 'Gross Amount',
            field: 'gross_amount',
            render: (item) => formatCurrency(item.gross_amount)
        },
        {
            header: 'Paid Amount',
            field: 'paid_amount',
            render: (item) => formatCurrency(item.paid_amount)
        },
        {
            header: 'Remaining Amount',
            field: 'remaining_amount',
            render: (item) => formatCurrency(item.remaining_amount)
        },
        { header: 'Status', field: 'status', type: 'badge' },
        {
            header: 'Actions',
            field: 'actions',
            render: (item) => (
                <Button
                    colorScheme="blue"
                    size="sm"
                    onClick={(e) => {
                        e.stopPropagation();
                        handleOpenModal(item);
                    }}
                >
                    View Details
                </Button>
            )
        }
    ];

    const getRowCursor = () => 'pointer';

    const handleRowClick = (item) => {
        handleOpenModal(item);
    };

    const handlePageChange = (newPage) => {
        fetchData(newPage, pagination.pageSize);
    };

    const handlePageSizeChange = (newPageSize) => {
        fetchData(1, newPageSize);
    };

    return (
        <>
            {loading ? (
                <Loader />
            ) : (
                <>
                    <div className="wrapper">
                        <div>
                            <div>
                                <div className="page-inner">
                                    <div className="row">
                                        <div className="bgWhite">
                                            <h1
                                                style={{
                                                    margin: "0",
                                                    textAlign: "center",
                                                    color: "#2B6CB0",
                                                    fontSize: "24px",
                                                    fontWeight: "bold",
                                                    padding: "10px",
                                                }}
                                            >
                                                Cash Voucher Report
                                            </h1>
                                        </div>
                                    </div>
                                    <div className="row">
                                        <ServerPaginatedTable
                                            data={data}
                                            columns={columns}
                                            pagination={pagination}
                                            onPageChange={handlePageChange}
                                            onPageSizeChange={handlePageSizeChange}
                                            onRowClick={handleRowClick}
                                            getRowCursor={getRowCursor}
                                            getBadgeColor={getStatusColor}
                                            loading={loading}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Voucher Details Modal */}
                    <Modal
                        isOpen={isModalOpen}
                        onClose={handleCloseModal}
                        size="6xl"
                        scrollBehavior="inside"
                    >
                        <ModalOverlay />
                        <ModalContent maxH="90vh">
                            <ModalHeader
                                bg="blue.600"
                                color="white"
                                borderTopRadius="md"
                            >
                                <Text fontSize="xl" fontWeight="bold">
                                    Voucher Details - {selectedVoucher?.voucher_no}
                                </Text>
                            </ModalHeader>
                            <ModalCloseButton color="white" />
                            <ModalBody p={6}>
                                {modalLoading ? (
                                    <Box display="flex" justifyContent="center" alignItems="center" minH="200px">
                                        <Spinner size="xl" color="blue.500" />
                                    </Box>
                                ) : voucherDetails ? (
                                    <VStack spacing={6} align="stretch">
                                        {/* Voucher Summary */}
                                        <Box
                                            p={4}
                                            bg="gray.50"
                                            borderRadius="md"
                                            border="1px solid"
                                            borderColor="gray.200"
                                        >
                                            <Text fontSize="lg" fontWeight="bold" mb={3} color="blue.600">
                                                Voucher Summary
                                            </Text>
                                            <HStack spacing={8} wrap="wrap">
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Voucher No</Text>
                                                    <Text fontWeight="semibold">{voucherDetails.voucher_no}</Text>
                                                </VStack>
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Client ID</Text>
                                                    <Text fontWeight="semibold">{voucherDetails.client_id}</Text>
                                                </VStack>
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Client Name</Text>
                                                    <Text fontWeight="semibold">{voucherDetails.client_name}</Text>
                                                </VStack>
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Status</Text>
                                                    <Badge
                                                        colorScheme={getStatusColor(voucherDetails.status)}
                                                        fontSize="sm"
                                                        px={2}
                                                        py={1}
                                                    >
                                                        {voucherDetails.status.toUpperCase()}
                                                    </Badge>
                                                </VStack>
                                            </HStack>

                                            <Divider my={4} />

                                            <HStack spacing={8} wrap="wrap">
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Gross Amount</Text>
                                                    <Text fontWeight="bold" fontSize="lg" color="blue.600">
                                                        {formatCurrency(voucherDetails.gross_amount)}
                                                    </Text>
                                                </VStack>
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Paid Amount</Text>
                                                    <Text fontWeight="bold" fontSize="lg" color="green.600">
                                                        {formatCurrency(voucherDetails.paid_amount)}
                                                    </Text>
                                                </VStack>
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Remaining Amount</Text>
                                                    <Text fontWeight="bold" fontSize="lg" color="red.600">
                                                        {formatCurrency(voucherDetails.remaining_amount)}
                                                    </Text>
                                                </VStack>
                                                <VStack align="start" spacing={1}>
                                                    <Text fontSize="sm" color="gray.600">Created Date</Text>
                                                    <Text fontWeight="semibold">
                                                        {voucherDetails.created_at ?
                                                            dayjs(voucherDetails.created_at).format('DD MMM, YYYY hh:mm A') :
                                                            'N/A'
                                                        }
                                                    </Text>
                                                </VStack>
                                            </HStack>
                                        </Box>

                                        {/* Payment History */}
                                        <Box>
                                            <Text fontSize="lg" fontWeight="bold" mb={3} color="blue.600">
                                                Payment History ({paymentHistory.length} payments)
                                            </Text>

                                            {paymentHistory.length > 0 ? (
                                                <TableContainer
                                                    border="1px solid"
                                                    borderColor="gray.200"
                                                    borderRadius="md"
                                                >
                                                    <Table variant="simple" size="sm">
                                                        <Thead bg="gray.100">
                                                            <Tr>
                                                                <Th>Payment Voucher No</Th>
                                                                <Th>Payment Type</Th>
                                                                <Th isNumeric>Amount</Th>
                                                                <Th>Date</Th>
                                                                <Th>Description</Th>
                                                            </Tr>
                                                        </Thead>
                                                        <Tbody>
                                                            {paymentHistory.map((payment, index) => (
                                                                <Tr key={index} _hover={{ bg: "gray.50" }}>
                                                                    <Td fontWeight="semibold">
                                                                        {payment.payment_voucher_no}
                                                                    </Td>
                                                                    <Td>
                                                                        <Badge
                                                                            colorScheme={payment.payment_type === 'CR' ? 'green' : 'blue'}
                                                                            variant="subtle"
                                                                        >
                                                                            {payment.payment_type === 'CR' ? 'Cash Receipt' : payment.payment_type}
                                                                        </Badge>
                                                                    </Td>
                                                                    <Td isNumeric fontWeight="semibold" color="green.600">
                                                                        {formatCurrency(payment.payment_amount)}
                                                                    </Td>
                                                                    <Td>
                                                                        {payment.payment_date ?
                                                                            dayjs(payment.payment_date).format('DD MMM, YYYY hh:mm A') :
                                                                            'N/A'
                                                                        }
                                                                    </Td>
                                                                    <Td>
                                                                        {payment.payment_description || 'N/A'}
                                                                    </Td>
                                                                </Tr>
                                                            ))}
                                                        </Tbody>
                                                    </Table>
                                                </TableContainer>
                                            ) : (
                                                <Box
                                                    p={8}
                                                    textAlign="center"
                                                    bg="gray.50"
                                                    borderRadius="md"
                                                    border="1px solid"
                                                    borderColor="gray.200"
                                                >
                                                    <Text color="gray.500" fontSize="lg">
                                                        No payments found for this voucher
                                                    </Text>
                                                    <Text color="gray.400" fontSize="sm" mt={1}>
                                                        This voucher is still pending payment
                                                    </Text>
                                                </Box>
                                            )}
                                        </Box>
                                    </VStack>
                                ) : (
                                    <Box textAlign="center" py={8}>
                                        <Text color="gray.500">No details available</Text>
                                    </Box>
                                )}
                            </ModalBody>
                        </ModalContent>
                    </Modal>
                </>
            )}
        </>
    );
}

export default CashVoucherReport;
