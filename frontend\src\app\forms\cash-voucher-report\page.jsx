"use client";
import React, { useState, useEffect } from 'react';
import "@src/app/dashboard/dashboard.css";
import { Box, Button, useToast } from '@chakra-ui/react';
import axiosInstance from '@src/app/axios';
import { useRouter } from 'next/navigation';
import ReportTable from '@src/components/Custom/ReportTable';
import Loader from '@src/components/Loader/Loader';

const CashVoucherReport = () => {
    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedTask, setSelectedTask] = useState(null);
    const router = useRouter();

    const fetchTasks = async () => {
        try {
            const { data } = await axiosInstance.get("cashVoucher/report");
            console.log(data);
            if (Array.isArray(data) && data.length > 0) {
                setTasks(data);
            } else {
                setTasks([]);
            }
        } catch (error) {
            console.error("Error: ", error);
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchTasks();
    }, []);

    const getStatusColor = (status) => {
        switch (status) {
            case 'rejected': return 'red';
            case 'pending': return 'yellow';
            case 'approved': return 'green';
            default: return 'gray';
        }
    };

    const columns = [
        { header: 'Voucher No', field: 'Voucher_No' },
        { header: 'Adj. Voucher No', field: 'adj_VoucherNo' },
        { header: 'Client ID', field: 'acc_id' },
        { header: 'Client Name', field: 'acc_name' },
        { header: 'Gross Amount', field: 'GrossAmount' },
        { header: 'Paid Amount', field: 'TotalAmt' },
        { header: 'Remaining Amount', field: 'RemainingAmount' },
        { header: 'Status', field: 'status', type: 'badge' },
        // {
        //     header: 'Actions',
        //     field: 'actions',
        //     render: (task) => (
        //         <Button
        //             colorScheme="blue"
        //             size="sm"
        //             disabled={task.status !== 'approved' || task.deliveryPersonID}
        //             onClick={() => handleOpenDeliveryModal(task)}
        //         >
        //             Assign Jobs
        //         </Button>
        //     )
        // }
    ];

    const getRowCursor = (task) => task.status === 'pending' ? 'pointer' : 'auto';

    const handleRowClick = (task) => {
        if (task.status === 'pending') {
            router.push(`/forms/audit-report/follow-up?purpose_no=${task.ID}`);
        }
    };

    return (
        <>
            {loading ? (
                <Loader />
            ) : (
                <>
                    <div className="wrapper">
                        <div>
                            <div>
                                <div className="page-inner">
                                    <div className="row">
                                        <div className="bgWhite">
                                            <h1
                                                style={{
                                                    margin: "0",
                                                    textAlign: "center",
                                                    color: "#2B6CB0",
                                                    fontSize: "24px",
                                                    fontWeight: "bold",
                                                    padding: "10px",
                                                }}
                                            >
                                                Audit Reports
                                            </h1>
                                        </div>
                                    </div>
                                    <div className="row">
                                        <ReportTable
                                            data={tasks}
                                            columns={columns}
                                            onRowClick={handleRowClick}
                                            getRowCursor={getRowCursor}
                                            getBadgeColor={getStatusColor}
                                            dateField="assessmentTime"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </>
    );
}

export default CashVoucherReport;
