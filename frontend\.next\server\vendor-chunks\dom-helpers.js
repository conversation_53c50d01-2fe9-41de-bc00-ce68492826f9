"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dom-helpers";
exports.ids = ["vendor-chunks/dom-helpers"];
exports.modules = {

/***/ "(ssr)/./node_modules/dom-helpers/esm/addClass.js":
/*!**************************************************!*\
  !*** ./node_modules/dom-helpers/esm/addClass.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ addClass)\n/* harmony export */ });\n/* harmony import */ var _hasClass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hasClass */ \"(ssr)/./node_modules/dom-helpers/esm/hasClass.js\");\n\n/**\n * Adds a CSS class to a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\nfunction addClass(element, className) {\n  if (element.classList) element.classList.add(className);else if (!(0,_hasClass__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element, className)) if (typeof element.className === 'string') element.className = element.className + \" \" + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + \" \" + className);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2FkZENsYXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZTtBQUNmLDBEQUEwRCxVQUFVLHFEQUFRLHlIQUF5SDtBQUNyTSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vYWRkQ2xhc3MuanM/NmNkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaGFzQ2xhc3MgZnJvbSAnLi9oYXNDbGFzcyc7XG4vKipcbiAqIEFkZHMgYSBDU1MgY2xhc3MgdG8gYSBnaXZlbiBlbGVtZW50LlxuICogXG4gKiBAcGFyYW0gZWxlbWVudCB0aGUgZWxlbWVudFxuICogQHBhcmFtIGNsYXNzTmFtZSB0aGUgQ1NTIGNsYXNzIG5hbWVcbiAqL1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBhZGRDbGFzcyhlbGVtZW50LCBjbGFzc05hbWUpIHtcbiAgaWYgKGVsZW1lbnQuY2xhc3NMaXN0KSBlbGVtZW50LmNsYXNzTGlzdC5hZGQoY2xhc3NOYW1lKTtlbHNlIGlmICghaGFzQ2xhc3MoZWxlbWVudCwgY2xhc3NOYW1lKSkgaWYgKHR5cGVvZiBlbGVtZW50LmNsYXNzTmFtZSA9PT0gJ3N0cmluZycpIGVsZW1lbnQuY2xhc3NOYW1lID0gZWxlbWVudC5jbGFzc05hbWUgKyBcIiBcIiArIGNsYXNzTmFtZTtlbHNlIGVsZW1lbnQuc2V0QXR0cmlidXRlKCdjbGFzcycsIChlbGVtZW50LmNsYXNzTmFtZSAmJiBlbGVtZW50LmNsYXNzTmFtZS5iYXNlVmFsIHx8ICcnKSArIFwiIFwiICsgY2xhc3NOYW1lKTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/addClass.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/addEventListener.js":
/*!**********************************************************!*\
  !*** ./node_modules/dom-helpers/esm/addEventListener.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   onceSupported: () => (/* binding */ onceSupported),\n/* harmony export */   optionsSupported: () => (/* binding */ optionsSupported)\n/* harmony export */ });\n/* harmony import */ var _canUseDOM__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./canUseDOM */ \"(ssr)/./node_modules/dom-helpers/esm/canUseDOM.js\");\n/* eslint-disable no-return-assign */\n\nvar optionsSupported = false;\nvar onceSupported = false;\n\ntry {\n  var options = {\n    get passive() {\n      return optionsSupported = true;\n    },\n\n    get once() {\n      // eslint-disable-next-line no-multi-assign\n      return onceSupported = optionsSupported = true;\n    }\n\n  };\n\n  if (_canUseDOM__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, true);\n  }\n} catch (e) {\n  /* */\n}\n\n/**\n * An `addEventListener` ponyfill, supports the `once` option\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction addEventListener(node, eventName, handler, options) {\n  if (options && typeof options !== 'boolean' && !onceSupported) {\n    var once = options.once,\n        capture = options.capture;\n    var wrappedHandler = handler;\n\n    if (!onceSupported && once) {\n      wrappedHandler = handler.__once || function onceHandler(event) {\n        this.removeEventListener(eventName, onceHandler, capture);\n        handler.call(this, event);\n      };\n\n      handler.__once = wrappedHandler;\n    }\n\n    node.addEventListener(eventName, wrappedHandler, optionsSupported ? options : capture);\n  }\n\n  node.addEventListener(eventName, handler, options);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (addEventListener);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/addEventListener.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/animationFrame.js":
/*!********************************************************!*\
  !*** ./node_modules/dom-helpers/esm/animationFrame.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancel: () => (/* binding */ cancel),\n/* harmony export */   request: () => (/* binding */ request)\n/* harmony export */ });\n/* harmony import */ var _canUseDOM__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./canUseDOM */ \"(ssr)/./node_modules/dom-helpers/esm/canUseDOM.js\");\n\n\n/* https://github.com/component/raf */\nvar prev = new Date().getTime();\n\nfunction fallback(fn) {\n  var curr = new Date().getTime();\n  var ms = Math.max(0, 16 - (curr - prev));\n  var handle = setTimeout(fn, ms);\n  prev = curr;\n  return handle;\n}\n\nvar vendors = ['', 'webkit', 'moz', 'o', 'ms'];\nvar cancelMethod = 'clearTimeout';\nvar rafImpl = fallback; // eslint-disable-next-line import/no-mutable-exports\n\nvar getKey = function getKey(vendor, k) {\n  return vendor + (!vendor ? k : k[0].toUpperCase() + k.substr(1)) + \"AnimationFrame\";\n};\n\nif (_canUseDOM__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  vendors.some(function (vendor) {\n    var rafMethod = getKey(vendor, 'request');\n\n    if (rafMethod in window) {\n      cancelMethod = getKey(vendor, 'cancel'); // @ts-ignore\n\n      rafImpl = function rafImpl(cb) {\n        return window[rafMethod](cb);\n      };\n    }\n\n    return !!rafImpl;\n  });\n}\n\nvar cancel = function cancel(id) {\n  // @ts-ignore\n  if (typeof window[cancelMethod] === 'function') window[cancelMethod](id);\n};\nvar request = rafImpl;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/animationFrame.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/canUseDOM.js":
/*!***************************************************!*\
  !*** ./node_modules/dom-helpers/esm/canUseDOM.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (!!(typeof window !== 'undefined' && window.document && window.document.createElement));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2NhblVzZURPTS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUscUZBQXFGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2RvbS1oZWxwZXJzL2VzbS9jYW5Vc2VET00uanM/MzYwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAhISh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuZG9jdW1lbnQgJiYgd2luZG93LmRvY3VtZW50LmNyZWF0ZUVsZW1lbnQpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/canUseDOM.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/closest.js":
/*!*************************************************!*\
  !*** ./node_modules/dom-helpers/esm/closest.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ closest)\n/* harmony export */ });\n/* harmony import */ var _matches__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./matches */ \"(ssr)/./node_modules/dom-helpers/esm/matches.js\");\n\n/**\n * Returns the closest parent element that matches a given selector.\n * \n * @param node the reference element\n * @param selector the selector to match\n * @param stopAt stop traversing when this element is found\n */\n\nfunction closest(node, selector, stopAt) {\n  if (node.closest && !stopAt) node.closest(selector);\n  var nextNode = node;\n\n  do {\n    if ((0,_matches__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(nextNode, selector)) return nextNode;\n    nextNode = nextNode.parentElement;\n  } while (nextNode && nextNode !== stopAt && nextNode.nodeType === document.ELEMENT_NODE);\n\n  return null;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2Nsb3Nlc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZjtBQUNBOztBQUVBO0FBQ0EsUUFBUSxvREFBTztBQUNmO0FBQ0EsSUFBSTs7QUFFSjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2RvbS1oZWxwZXJzL2VzbS9jbG9zZXN0LmpzPzU0N2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1hdGNoZXMgZnJvbSAnLi9tYXRjaGVzJztcbi8qKlxuICogUmV0dXJucyB0aGUgY2xvc2VzdCBwYXJlbnQgZWxlbWVudCB0aGF0IG1hdGNoZXMgYSBnaXZlbiBzZWxlY3Rvci5cbiAqIFxuICogQHBhcmFtIG5vZGUgdGhlIHJlZmVyZW5jZSBlbGVtZW50XG4gKiBAcGFyYW0gc2VsZWN0b3IgdGhlIHNlbGVjdG9yIHRvIG1hdGNoXG4gKiBAcGFyYW0gc3RvcEF0IHN0b3AgdHJhdmVyc2luZyB3aGVuIHRoaXMgZWxlbWVudCBpcyBmb3VuZFxuICovXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNsb3Nlc3Qobm9kZSwgc2VsZWN0b3IsIHN0b3BBdCkge1xuICBpZiAobm9kZS5jbG9zZXN0ICYmICFzdG9wQXQpIG5vZGUuY2xvc2VzdChzZWxlY3Rvcik7XG4gIHZhciBuZXh0Tm9kZSA9IG5vZGU7XG5cbiAgZG8ge1xuICAgIGlmIChtYXRjaGVzKG5leHROb2RlLCBzZWxlY3RvcikpIHJldHVybiBuZXh0Tm9kZTtcbiAgICBuZXh0Tm9kZSA9IG5leHROb2RlLnBhcmVudEVsZW1lbnQ7XG4gIH0gd2hpbGUgKG5leHROb2RlICYmIG5leHROb2RlICE9PSBzdG9wQXQgJiYgbmV4dE5vZGUubm9kZVR5cGUgPT09IGRvY3VtZW50LkVMRU1FTlRfTk9ERSk7XG5cbiAgcmV0dXJuIG51bGw7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/closest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/contains.js":
/*!**************************************************!*\
  !*** ./node_modules/dom-helpers/esm/contains.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ contains)\n/* harmony export */ });\n/* eslint-disable no-bitwise, no-cond-assign */\n\n/**\n * Checks if an element contains another given element.\n * \n * @param context the context element\n * @param node the element to check\n */\nfunction contains(context, node) {\n  // HTML DOM and SVG DOM may have different support levels,\n  // so we need to check on context instead of a document root element.\n  if (context.contains) return context.contains(node);\n  if (context.compareDocumentPosition) return context === node || !!(context.compareDocumentPosition(node) & 16);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2NvbnRhaW5zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2NvbnRhaW5zLmpzPzdlYTUiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWRpc2FibGUgbm8tYml0d2lzZSwgbm8tY29uZC1hc3NpZ24gKi9cblxuLyoqXG4gKiBDaGVja3MgaWYgYW4gZWxlbWVudCBjb250YWlucyBhbm90aGVyIGdpdmVuIGVsZW1lbnQuXG4gKiBcbiAqIEBwYXJhbSBjb250ZXh0IHRoZSBjb250ZXh0IGVsZW1lbnRcbiAqIEBwYXJhbSBub2RlIHRoZSBlbGVtZW50IHRvIGNoZWNrXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNvbnRhaW5zKGNvbnRleHQsIG5vZGUpIHtcbiAgLy8gSFRNTCBET00gYW5kIFNWRyBET00gbWF5IGhhdmUgZGlmZmVyZW50IHN1cHBvcnQgbGV2ZWxzLFxuICAvLyBzbyB3ZSBuZWVkIHRvIGNoZWNrIG9uIGNvbnRleHQgaW5zdGVhZCBvZiBhIGRvY3VtZW50IHJvb3QgZWxlbWVudC5cbiAgaWYgKGNvbnRleHQuY29udGFpbnMpIHJldHVybiBjb250ZXh0LmNvbnRhaW5zKG5vZGUpO1xuICBpZiAoY29udGV4dC5jb21wYXJlRG9jdW1lbnRQb3NpdGlvbikgcmV0dXJuIGNvbnRleHQgPT09IG5vZGUgfHwgISEoY29udGV4dC5jb21wYXJlRG9jdW1lbnRQb3NpdGlvbihub2RlKSAmIDE2KTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/css.js":
/*!*********************************************!*\
  !*** ./node_modules/dom-helpers/esm/css.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _getComputedStyle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getComputedStyle */ \"(ssr)/./node_modules/dom-helpers/esm/getComputedStyle.js\");\n/* harmony import */ var _hyphenateStyle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hyphenateStyle */ \"(ssr)/./node_modules/dom-helpers/esm/hyphenateStyle.js\");\n/* harmony import */ var _isTransform__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isTransform */ \"(ssr)/./node_modules/dom-helpers/esm/isTransform.js\");\n\n\n\n\nfunction style(node, property) {\n  var css = '';\n  var transforms = '';\n\n  if (typeof property === 'string') {\n    return node.style.getPropertyValue((0,_hyphenateStyle__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(property)) || (0,_getComputedStyle__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).getPropertyValue((0,_hyphenateStyle__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(property));\n  }\n\n  Object.keys(property).forEach(function (key) {\n    var value = property[key];\n\n    if (!value && value !== 0) {\n      node.style.removeProperty((0,_hyphenateStyle__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key));\n    } else if ((0,_isTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n      transforms += key + \"(\" + value + \") \";\n    } else {\n      css += (0,_hyphenateStyle__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key) + \": \" + value + \";\";\n    }\n  });\n\n  if (transforms) {\n    css += \"transform: \" + transforms + \";\";\n  }\n\n  node.style.cssText += \";\" + css;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (style);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2Nzcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtEO0FBQ1Q7QUFDRDs7QUFFeEM7QUFDQTtBQUNBOztBQUVBO0FBQ0EsdUNBQXVDLDJEQUFTLGVBQWUsNkRBQWdCLHdCQUF3QiwyREFBUztBQUNoSDs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsZ0NBQWdDLDJEQUFTO0FBQ3pDLE1BQU0sU0FBUyx3REFBVztBQUMxQjtBQUNBLE1BQU07QUFDTixhQUFhLDJEQUFTLHlCQUF5QjtBQUMvQztBQUNBLEdBQUc7O0FBRUg7QUFDQSwwQ0FBMEM7QUFDMUM7O0FBRUEsMEJBQTBCO0FBQzFCOztBQUVBLGlFQUFlLEtBQUsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2Nzcy5qcz83NGM3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRDb21wdXRlZFN0eWxlIGZyb20gJy4vZ2V0Q29tcHV0ZWRTdHlsZSc7XG5pbXBvcnQgaHlwaGVuYXRlIGZyb20gJy4vaHlwaGVuYXRlU3R5bGUnO1xuaW1wb3J0IGlzVHJhbnNmb3JtIGZyb20gJy4vaXNUcmFuc2Zvcm0nO1xuXG5mdW5jdGlvbiBzdHlsZShub2RlLCBwcm9wZXJ0eSkge1xuICB2YXIgY3NzID0gJyc7XG4gIHZhciB0cmFuc2Zvcm1zID0gJyc7XG5cbiAgaWYgKHR5cGVvZiBwcm9wZXJ0eSA9PT0gJ3N0cmluZycpIHtcbiAgICByZXR1cm4gbm9kZS5zdHlsZS5nZXRQcm9wZXJ0eVZhbHVlKGh5cGhlbmF0ZShwcm9wZXJ0eSkpIHx8IGdldENvbXB1dGVkU3R5bGUobm9kZSkuZ2V0UHJvcGVydHlWYWx1ZShoeXBoZW5hdGUocHJvcGVydHkpKTtcbiAgfVxuXG4gIE9iamVjdC5rZXlzKHByb3BlcnR5KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICB2YXIgdmFsdWUgPSBwcm9wZXJ0eVtrZXldO1xuXG4gICAgaWYgKCF2YWx1ZSAmJiB2YWx1ZSAhPT0gMCkge1xuICAgICAgbm9kZS5zdHlsZS5yZW1vdmVQcm9wZXJ0eShoeXBoZW5hdGUoa2V5KSk7XG4gICAgfSBlbHNlIGlmIChpc1RyYW5zZm9ybShrZXkpKSB7XG4gICAgICB0cmFuc2Zvcm1zICs9IGtleSArIFwiKFwiICsgdmFsdWUgKyBcIikgXCI7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNzcyArPSBoeXBoZW5hdGUoa2V5KSArIFwiOiBcIiArIHZhbHVlICsgXCI7XCI7XG4gICAgfVxuICB9KTtcblxuICBpZiAodHJhbnNmb3Jtcykge1xuICAgIGNzcyArPSBcInRyYW5zZm9ybTogXCIgKyB0cmFuc2Zvcm1zICsgXCI7XCI7XG4gIH1cblxuICBub2RlLnN0eWxlLmNzc1RleHQgKz0gXCI7XCIgKyBjc3M7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHN0eWxlOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/css.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/getComputedStyle.js":
/*!**********************************************************!*\
  !*** ./node_modules/dom-helpers/esm/getComputedStyle.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getComputedStyle)\n/* harmony export */ });\n/* harmony import */ var _ownerWindow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ownerWindow */ \"(ssr)/./node_modules/dom-helpers/esm/ownerWindow.js\");\n\n/**\n * Returns one or all computed style properties of an element.\n * \n * @param node the element\n * @param psuedoElement the style property\n */\n\nfunction getComputedStyle(node, psuedoElement) {\n  return (0,_ownerWindow__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).getComputedStyle(node, psuedoElement);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2dldENvbXB1dGVkU3R5bGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVlO0FBQ2YsU0FBUyx3REFBVztBQUNwQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vZ2V0Q29tcHV0ZWRTdHlsZS5qcz83ZTZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBvd25lcldpbmRvdyBmcm9tICcuL293bmVyV2luZG93Jztcbi8qKlxuICogUmV0dXJucyBvbmUgb3IgYWxsIGNvbXB1dGVkIHN0eWxlIHByb3BlcnRpZXMgb2YgYW4gZWxlbWVudC5cbiAqIFxuICogQHBhcmFtIG5vZGUgdGhlIGVsZW1lbnRcbiAqIEBwYXJhbSBwc3VlZG9FbGVtZW50IHRoZSBzdHlsZSBwcm9wZXJ0eVxuICovXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldENvbXB1dGVkU3R5bGUobm9kZSwgcHN1ZWRvRWxlbWVudCkge1xuICByZXR1cm4gb3duZXJXaW5kb3cobm9kZSkuZ2V0Q29tcHV0ZWRTdHlsZShub2RlLCBwc3VlZG9FbGVtZW50KTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/getComputedStyle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/getScrollAccessor.js":
/*!***********************************************************!*\
  !*** ./node_modules/dom-helpers/esm/getScrollAccessor.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getscrollAccessor)\n/* harmony export */ });\n/* harmony import */ var _isWindow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isWindow */ \"(ssr)/./node_modules/dom-helpers/esm/isWindow.js\");\n\nfunction getscrollAccessor(offset) {\n  var prop = offset === 'pageXOffset' ? 'scrollLeft' : 'scrollTop';\n\n  function scrollAccessor(node, val) {\n    var win = (0,_isWindow__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node);\n\n    if (val === undefined) {\n      return win ? win[offset] : node[prop];\n    }\n\n    if (win) {\n      win.scrollTo(win[offset], val);\n    } else {\n      node[prop] = val;\n    }\n  }\n\n  return scrollAccessor;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2dldFNjcm9sbEFjY2Vzc29yLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQ25CO0FBQ2Y7O0FBRUE7QUFDQSxjQUFjLHFEQUFROztBQUV0QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vZ2V0U2Nyb2xsQWNjZXNzb3IuanM/ZjhjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaXNXaW5kb3cgZnJvbSAnLi9pc1dpbmRvdyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRzY3JvbGxBY2Nlc3NvcihvZmZzZXQpIHtcbiAgdmFyIHByb3AgPSBvZmZzZXQgPT09ICdwYWdlWE9mZnNldCcgPyAnc2Nyb2xsTGVmdCcgOiAnc2Nyb2xsVG9wJztcblxuICBmdW5jdGlvbiBzY3JvbGxBY2Nlc3Nvcihub2RlLCB2YWwpIHtcbiAgICB2YXIgd2luID0gaXNXaW5kb3cobm9kZSk7XG5cbiAgICBpZiAodmFsID09PSB1bmRlZmluZWQpIHtcbiAgICAgIHJldHVybiB3aW4gPyB3aW5bb2Zmc2V0XSA6IG5vZGVbcHJvcF07XG4gICAgfVxuXG4gICAgaWYgKHdpbikge1xuICAgICAgd2luLnNjcm9sbFRvKHdpbltvZmZzZXRdLCB2YWwpO1xuICAgIH0gZWxzZSB7XG4gICAgICBub2RlW3Byb3BdID0gdmFsO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBzY3JvbGxBY2Nlc3Nvcjtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/getScrollAccessor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/hasClass.js":
/*!**************************************************!*\
  !*** ./node_modules/dom-helpers/esm/hasClass.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ hasClass)\n/* harmony export */ });\n/**\n * Checks if a given element has a CSS class.\n * \n * @param element the element\n * @param className the CSS class name\n */\nfunction hasClass(element, className) {\n  if (element.classList) return !!className && element.classList.contains(className);\n  return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2hhc0NsYXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vaGFzQ2xhc3MuanM/N2U1NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENoZWNrcyBpZiBhIGdpdmVuIGVsZW1lbnQgaGFzIGEgQ1NTIGNsYXNzLlxuICogXG4gKiBAcGFyYW0gZWxlbWVudCB0aGUgZWxlbWVudFxuICogQHBhcmFtIGNsYXNzTmFtZSB0aGUgQ1NTIGNsYXNzIG5hbWVcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaGFzQ2xhc3MoZWxlbWVudCwgY2xhc3NOYW1lKSB7XG4gIGlmIChlbGVtZW50LmNsYXNzTGlzdCkgcmV0dXJuICEhY2xhc3NOYW1lICYmIGVsZW1lbnQuY2xhc3NMaXN0LmNvbnRhaW5zKGNsYXNzTmFtZSk7XG4gIHJldHVybiAoXCIgXCIgKyAoZWxlbWVudC5jbGFzc05hbWUuYmFzZVZhbCB8fCBlbGVtZW50LmNsYXNzTmFtZSkgKyBcIiBcIikuaW5kZXhPZihcIiBcIiArIGNsYXNzTmFtZSArIFwiIFwiKSAhPT0gLTE7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/hasClass.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/height.js":
/*!************************************************!*\
  !*** ./node_modules/dom-helpers/esm/height.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ height)\n/* harmony export */ });\n/* harmony import */ var _isWindow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isWindow */ \"(ssr)/./node_modules/dom-helpers/esm/isWindow.js\");\n/* harmony import */ var _offset__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./offset */ \"(ssr)/./node_modules/dom-helpers/esm/offset.js\");\n\n\n/**\n * Returns the height of a given element.\n * \n * @param node the element\n * @param client whether to use `clientHeight` if possible\n */\n\nfunction height(node, client) {\n  var win = (0,_isWindow__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node);\n  return win ? win.innerHeight : client ? node.clientHeight : (0,_offset__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node).height;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2hlaWdodC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDTDtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZixZQUFZLHFEQUFTO0FBQ3JCLDhEQUE4RCxtREFBTTtBQUNwRSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vaGVpZ2h0LmpzP2YxZjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGdldFdpbmRvdyBmcm9tICcuL2lzV2luZG93JztcbmltcG9ydCBvZmZzZXQgZnJvbSAnLi9vZmZzZXQnO1xuLyoqXG4gKiBSZXR1cm5zIHRoZSBoZWlnaHQgb2YgYSBnaXZlbiBlbGVtZW50LlxuICogXG4gKiBAcGFyYW0gbm9kZSB0aGUgZWxlbWVudFxuICogQHBhcmFtIGNsaWVudCB3aGV0aGVyIHRvIHVzZSBgY2xpZW50SGVpZ2h0YCBpZiBwb3NzaWJsZVxuICovXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGhlaWdodChub2RlLCBjbGllbnQpIHtcbiAgdmFyIHdpbiA9IGdldFdpbmRvdyhub2RlKTtcbiAgcmV0dXJuIHdpbiA/IHdpbi5pbm5lckhlaWdodCA6IGNsaWVudCA/IG5vZGUuY2xpZW50SGVpZ2h0IDogb2Zmc2V0KG5vZGUpLmhlaWdodDtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/height.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/hyphenate.js":
/*!***************************************************!*\
  !*** ./node_modules/dom-helpers/esm/hyphenate.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ hyphenate)\n/* harmony export */ });\nvar rUpper = /([A-Z])/g;\nfunction hyphenate(string) {\n  return string.replace(rUpper, '-$1').toLowerCase();\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2h5cGhlbmF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2h5cGhlbmF0ZS5qcz8yMWNlIl0sInNvdXJjZXNDb250ZW50IjpbInZhciByVXBwZXIgPSAvKFtBLVpdKS9nO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaHlwaGVuYXRlKHN0cmluZykge1xuICByZXR1cm4gc3RyaW5nLnJlcGxhY2UoclVwcGVyLCAnLSQxJykudG9Mb3dlckNhc2UoKTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/hyphenate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/hyphenateStyle.js":
/*!********************************************************!*\
  !*** ./node_modules/dom-helpers/esm/hyphenateStyle.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ hyphenateStyleName)\n/* harmony export */ });\n/* harmony import */ var _hyphenate__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hyphenate */ \"(ssr)/./node_modules/dom-helpers/esm/hyphenate.js\");\n/**\n * Copyright 2013-2014, Facebook, Inc.\n * All rights reserved.\n * https://github.com/facebook/react/blob/2aeb8a2a6beb00617a4217f7f8284924fa2ad819/src/vendor/core/hyphenateStyleName.js\n */\n\nvar msPattern = /^ms-/;\nfunction hyphenateStyleName(string) {\n  return (0,_hyphenate__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(string).replace(msPattern, '-ms-');\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2h5cGhlbmF0ZVN0eWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNvQztBQUNwQztBQUNlO0FBQ2YsU0FBUyxzREFBUztBQUNsQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vaHlwaGVuYXRlU3R5bGUuanM/YzUwZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAyMDEzLTIwMTQsIEZhY2Vib29rLCBJbmMuXG4gKiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2Jsb2IvMmFlYjhhMmE2YmViMDA2MTdhNDIxN2Y3ZjgyODQ5MjRmYTJhZDgxOS9zcmMvdmVuZG9yL2NvcmUvaHlwaGVuYXRlU3R5bGVOYW1lLmpzXG4gKi9cbmltcG9ydCBoeXBoZW5hdGUgZnJvbSAnLi9oeXBoZW5hdGUnO1xudmFyIG1zUGF0dGVybiA9IC9ebXMtLztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGh5cGhlbmF0ZVN0eWxlTmFtZShzdHJpbmcpIHtcbiAgcmV0dXJuIGh5cGhlbmF0ZShzdHJpbmcpLnJlcGxhY2UobXNQYXR0ZXJuLCAnLW1zLScpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/hyphenateStyle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/isDocument.js":
/*!****************************************************!*\
  !*** ./node_modules/dom-helpers/esm/isDocument.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isDocument)\n/* harmony export */ });\nfunction isDocument(element) {\n  return 'nodeType' in element && element.nodeType === document.DOCUMENT_NODE;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2lzRG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vaXNEb2N1bWVudC5qcz83Zjk5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlzRG9jdW1lbnQoZWxlbWVudCkge1xuICByZXR1cm4gJ25vZGVUeXBlJyBpbiBlbGVtZW50ICYmIGVsZW1lbnQubm9kZVR5cGUgPT09IGRvY3VtZW50LkRPQ1VNRU5UX05PREU7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/isDocument.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/isTransform.js":
/*!*****************************************************!*\
  !*** ./node_modules/dom-helpers/esm/isTransform.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isTransform)\n/* harmony export */ });\nvar supportedTransforms = /^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;\nfunction isTransform(value) {\n  return !!(value && supportedTransforms.test(value));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2lzVHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vaXNUcmFuc2Zvcm0uanM/ZDJjMiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgc3VwcG9ydGVkVHJhbnNmb3JtcyA9IC9eKCh0cmFuc2xhdGV8cm90YXRlfHNjYWxlKShYfFl8WnwzZCk/fG1hdHJpeCgzZCk/fHBlcnNwZWN0aXZlfHNrZXcoWHxZKT8pJC9pO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaXNUcmFuc2Zvcm0odmFsdWUpIHtcbiAgcmV0dXJuICEhKHZhbHVlICYmIHN1cHBvcnRlZFRyYW5zZm9ybXMudGVzdCh2YWx1ZSkpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/isTransform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/isWindow.js":
/*!**************************************************!*\
  !*** ./node_modules/dom-helpers/esm/isWindow.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isWindow)\n/* harmony export */ });\n/* harmony import */ var _isDocument__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isDocument */ \"(ssr)/./node_modules/dom-helpers/esm/isDocument.js\");\n\nfunction isWindow(node) {\n  if ('window' in node && node.window === node) return node;\n  if ((0,_isDocument__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node)) return node.defaultView || false;\n  return false;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2lzV2luZG93LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQ3ZCO0FBQ2Y7QUFDQSxNQUFNLHVEQUFVO0FBQ2hCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2lzV2luZG93LmpzP2I1MTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGlzRG9jdW1lbnQgZnJvbSAnLi9pc0RvY3VtZW50JztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlzV2luZG93KG5vZGUpIHtcbiAgaWYgKCd3aW5kb3cnIGluIG5vZGUgJiYgbm9kZS53aW5kb3cgPT09IG5vZGUpIHJldHVybiBub2RlO1xuICBpZiAoaXNEb2N1bWVudChub2RlKSkgcmV0dXJuIG5vZGUuZGVmYXVsdFZpZXcgfHwgZmFsc2U7XG4gIHJldHVybiBmYWxzZTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/isWindow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/listen.js":
/*!************************************************!*\
  !*** ./node_modules/dom-helpers/esm/listen.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _addEventListener__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./addEventListener */ \"(ssr)/./node_modules/dom-helpers/esm/addEventListener.js\");\n/* harmony import */ var _removeEventListener__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./removeEventListener */ \"(ssr)/./node_modules/dom-helpers/esm/removeEventListener.js\");\n\n\n\nfunction listen(node, eventName, handler, options) {\n  (0,_addEventListener__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node, eventName, handler, options);\n  return function () {\n    (0,_removeEventListener__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, eventName, handler, options);\n  };\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (listen);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL2xpc3Rlbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDTTs7QUFFeEQ7QUFDQSxFQUFFLDZEQUFnQjtBQUNsQjtBQUNBLElBQUksZ0VBQW1CO0FBQ3ZCO0FBQ0E7O0FBRUEsaUVBQWUsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vbGlzdGVuLmpzP2JhN2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFkZEV2ZW50TGlzdGVuZXIgZnJvbSAnLi9hZGRFdmVudExpc3RlbmVyJztcbmltcG9ydCByZW1vdmVFdmVudExpc3RlbmVyIGZyb20gJy4vcmVtb3ZlRXZlbnRMaXN0ZW5lcic7XG5cbmZ1bmN0aW9uIGxpc3Rlbihub2RlLCBldmVudE5hbWUsIGhhbmRsZXIsIG9wdGlvbnMpIHtcbiAgYWRkRXZlbnRMaXN0ZW5lcihub2RlLCBldmVudE5hbWUsIGhhbmRsZXIsIG9wdGlvbnMpO1xuICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgIHJlbW92ZUV2ZW50TGlzdGVuZXIobm9kZSwgZXZlbnROYW1lLCBoYW5kbGVyLCBvcHRpb25zKTtcbiAgfTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgbGlzdGVuOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/listen.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/matches.js":
/*!*************************************************!*\
  !*** ./node_modules/dom-helpers/esm/matches.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ matches)\n/* harmony export */ });\nvar matchesImpl;\n/**\n * Checks if a given element matches a selector.\n * \n * @param node the element\n * @param selector the selector\n */\n\nfunction matches(node, selector) {\n  if (!matchesImpl) {\n    var body = document.body;\n    var nativeMatch = body.matches || body.matchesSelector || body.webkitMatchesSelector || body.mozMatchesSelector || body.msMatchesSelector;\n\n    matchesImpl = function matchesImpl(n, s) {\n      return nativeMatch.call(n, s);\n    };\n  }\n\n  return matchesImpl(node, selector);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL21hdGNoZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVlO0FBQ2Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL21hdGNoZXMuanM/MDg2YSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbWF0Y2hlc0ltcGw7XG4vKipcbiAqIENoZWNrcyBpZiBhIGdpdmVuIGVsZW1lbnQgbWF0Y2hlcyBhIHNlbGVjdG9yLlxuICogXG4gKiBAcGFyYW0gbm9kZSB0aGUgZWxlbWVudFxuICogQHBhcmFtIHNlbGVjdG9yIHRoZSBzZWxlY3RvclxuICovXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1hdGNoZXMobm9kZSwgc2VsZWN0b3IpIHtcbiAgaWYgKCFtYXRjaGVzSW1wbCkge1xuICAgIHZhciBib2R5ID0gZG9jdW1lbnQuYm9keTtcbiAgICB2YXIgbmF0aXZlTWF0Y2ggPSBib2R5Lm1hdGNoZXMgfHwgYm9keS5tYXRjaGVzU2VsZWN0b3IgfHwgYm9keS53ZWJraXRNYXRjaGVzU2VsZWN0b3IgfHwgYm9keS5tb3pNYXRjaGVzU2VsZWN0b3IgfHwgYm9keS5tc01hdGNoZXNTZWxlY3RvcjtcblxuICAgIG1hdGNoZXNJbXBsID0gZnVuY3Rpb24gbWF0Y2hlc0ltcGwobiwgcykge1xuICAgICAgcmV0dXJuIG5hdGl2ZU1hdGNoLmNhbGwobiwgcyk7XG4gICAgfTtcbiAgfVxuXG4gIHJldHVybiBtYXRjaGVzSW1wbChub2RlLCBzZWxlY3Rvcik7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/matches.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/offset.js":
/*!************************************************!*\
  !*** ./node_modules/dom-helpers/esm/offset.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ offset)\n/* harmony export */ });\n/* harmony import */ var _contains__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contains */ \"(ssr)/./node_modules/dom-helpers/esm/contains.js\");\n/* harmony import */ var _ownerDocument__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ownerDocument */ \"(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\");\n/* harmony import */ var _scrollLeft__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./scrollLeft */ \"(ssr)/./node_modules/dom-helpers/esm/scrollLeft.js\");\n/* harmony import */ var _scrollTop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./scrollTop */ \"(ssr)/./node_modules/dom-helpers/esm/scrollTop.js\");\n\n\n\n\n/**\n * Returns the offset of a given element, including top and left positions, width and height.\n * \n * @param node the element\n */\n\nfunction offset(node) {\n  var doc = (0,_ownerDocument__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node);\n  var box = {\n    top: 0,\n    left: 0,\n    height: 0,\n    width: 0\n  };\n  var docElem = doc && doc.documentElement; // Make sure it's not a disconnected DOM node\n\n  if (!docElem || !(0,_contains__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(docElem, node)) return box;\n  if (node.getBoundingClientRect !== undefined) box = node.getBoundingClientRect();\n  box = {\n    top: box.top + (0,_scrollTop__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(docElem) - (docElem.clientTop || 0),\n    left: box.left + (0,_scrollLeft__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(docElem) - (docElem.clientLeft || 0),\n    width: box.width,\n    height: box.height\n  };\n  return box;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL29mZnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrQztBQUNVO0FBQ047QUFDRjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVlO0FBQ2YsWUFBWSwwREFBYTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEM7O0FBRTVDLG1CQUFtQixxREFBUTtBQUMzQjtBQUNBO0FBQ0EsbUJBQW1CLHNEQUFTO0FBQzVCLHFCQUFxQix1REFBVTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2RvbS1oZWxwZXJzL2VzbS9vZmZzZXQuanM/NzQxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY29udGFpbnMgZnJvbSAnLi9jb250YWlucyc7XG5pbXBvcnQgb3duZXJEb2N1bWVudCBmcm9tICcuL293bmVyRG9jdW1lbnQnO1xuaW1wb3J0IHNjcm9sbExlZnQgZnJvbSAnLi9zY3JvbGxMZWZ0JztcbmltcG9ydCBzY3JvbGxUb3AgZnJvbSAnLi9zY3JvbGxUb3AnO1xuLyoqXG4gKiBSZXR1cm5zIHRoZSBvZmZzZXQgb2YgYSBnaXZlbiBlbGVtZW50LCBpbmNsdWRpbmcgdG9wIGFuZCBsZWZ0IHBvc2l0aW9ucywgd2lkdGggYW5kIGhlaWdodC5cbiAqIFxuICogQHBhcmFtIG5vZGUgdGhlIGVsZW1lbnRcbiAqL1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBvZmZzZXQobm9kZSkge1xuICB2YXIgZG9jID0gb3duZXJEb2N1bWVudChub2RlKTtcbiAgdmFyIGJveCA9IHtcbiAgICB0b3A6IDAsXG4gICAgbGVmdDogMCxcbiAgICBoZWlnaHQ6IDAsXG4gICAgd2lkdGg6IDBcbiAgfTtcbiAgdmFyIGRvY0VsZW0gPSBkb2MgJiYgZG9jLmRvY3VtZW50RWxlbWVudDsgLy8gTWFrZSBzdXJlIGl0J3Mgbm90IGEgZGlzY29ubmVjdGVkIERPTSBub2RlXG5cbiAgaWYgKCFkb2NFbGVtIHx8ICFjb250YWlucyhkb2NFbGVtLCBub2RlKSkgcmV0dXJuIGJveDtcbiAgaWYgKG5vZGUuZ2V0Qm91bmRpbmdDbGllbnRSZWN0ICE9PSB1bmRlZmluZWQpIGJveCA9IG5vZGUuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XG4gIGJveCA9IHtcbiAgICB0b3A6IGJveC50b3AgKyBzY3JvbGxUb3AoZG9jRWxlbSkgLSAoZG9jRWxlbS5jbGllbnRUb3AgfHwgMCksXG4gICAgbGVmdDogYm94LmxlZnQgKyBzY3JvbGxMZWZ0KGRvY0VsZW0pIC0gKGRvY0VsZW0uY2xpZW50TGVmdCB8fCAwKSxcbiAgICB3aWR0aDogYm94LndpZHRoLFxuICAgIGhlaWdodDogYm94LmhlaWdodFxuICB9O1xuICByZXR1cm4gYm94O1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/offset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/offsetParent.js":
/*!******************************************************!*\
  !*** ./node_modules/dom-helpers/esm/offsetParent.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ offsetParent)\n/* harmony export */ });\n/* harmony import */ var _css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./css */ \"(ssr)/./node_modules/dom-helpers/esm/css.js\");\n/* harmony import */ var _ownerDocument__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ownerDocument */ \"(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\");\n\n\n\nvar isHTMLElement = function isHTMLElement(e) {\n  return !!e && 'offsetParent' in e;\n};\n\nfunction offsetParent(node) {\n  var doc = (0,_ownerDocument__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node);\n  var parent = node && node.offsetParent;\n\n  while (isHTMLElement(parent) && parent.nodeName !== 'HTML' && (0,_css__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(parent, 'position') === 'static') {\n    parent = parent.offsetParent;\n  }\n\n  return parent || doc.documentElement;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL29mZnNldFBhcmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0I7QUFDb0I7O0FBRTVDO0FBQ0E7QUFDQTs7QUFFZTtBQUNmLFlBQVksMERBQWE7QUFDekI7O0FBRUEsZ0VBQWdFLGdEQUFHO0FBQ25FO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vb2Zmc2V0UGFyZW50LmpzPzRlMGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNzcyBmcm9tICcuL2Nzcyc7XG5pbXBvcnQgb3duZXJEb2N1bWVudCBmcm9tICcuL293bmVyRG9jdW1lbnQnO1xuXG52YXIgaXNIVE1MRWxlbWVudCA9IGZ1bmN0aW9uIGlzSFRNTEVsZW1lbnQoZSkge1xuICByZXR1cm4gISFlICYmICdvZmZzZXRQYXJlbnQnIGluIGU7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBvZmZzZXRQYXJlbnQobm9kZSkge1xuICB2YXIgZG9jID0gb3duZXJEb2N1bWVudChub2RlKTtcbiAgdmFyIHBhcmVudCA9IG5vZGUgJiYgbm9kZS5vZmZzZXRQYXJlbnQ7XG5cbiAgd2hpbGUgKGlzSFRNTEVsZW1lbnQocGFyZW50KSAmJiBwYXJlbnQubm9kZU5hbWUgIT09ICdIVE1MJyAmJiBjc3MocGFyZW50LCAncG9zaXRpb24nKSA9PT0gJ3N0YXRpYycpIHtcbiAgICBwYXJlbnQgPSBwYXJlbnQub2Zmc2V0UGFyZW50O1xuICB9XG5cbiAgcmV0dXJuIHBhcmVudCB8fCBkb2MuZG9jdW1lbnRFbGVtZW50O1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/offsetParent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js":
/*!*******************************************************!*\
  !*** ./node_modules/dom-helpers/esm/ownerDocument.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ownerDocument)\n/* harmony export */ });\n/**\n * Returns the owner document of a given element.\n * \n * @param node the element\n */\nfunction ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL293bmVyRG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL293bmVyRG9jdW1lbnQuanM/YmZlMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJldHVybnMgdGhlIG93bmVyIGRvY3VtZW50IG9mIGEgZ2l2ZW4gZWxlbWVudC5cbiAqIFxuICogQHBhcmFtIG5vZGUgdGhlIGVsZW1lbnRcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gb3duZXJEb2N1bWVudChub2RlKSB7XG4gIHJldHVybiBub2RlICYmIG5vZGUub3duZXJEb2N1bWVudCB8fCBkb2N1bWVudDtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/ownerWindow.js":
/*!*****************************************************!*\
  !*** ./node_modules/dom-helpers/esm/ownerWindow.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ownerWindow)\n/* harmony export */ });\n/* harmony import */ var _ownerDocument__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ownerDocument */ \"(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\");\n\n/**\n * Returns the owner window of a given element.\n * \n * @param node the element\n */\n\nfunction ownerWindow(node) {\n  var doc = (0,_ownerDocument__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node);\n  return doc && doc.defaultView || window;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL293bmVyV2luZG93LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZixZQUFZLDBEQUFhO0FBQ3pCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL293bmVyV2luZG93LmpzPzgyYTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG93bmVyRG9jdW1lbnQgZnJvbSAnLi9vd25lckRvY3VtZW50Jztcbi8qKlxuICogUmV0dXJucyB0aGUgb3duZXIgd2luZG93IG9mIGEgZ2l2ZW4gZWxlbWVudC5cbiAqIFxuICogQHBhcmFtIG5vZGUgdGhlIGVsZW1lbnRcbiAqL1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBvd25lcldpbmRvdyhub2RlKSB7XG4gIHZhciBkb2MgPSBvd25lckRvY3VtZW50KG5vZGUpO1xuICByZXR1cm4gZG9jICYmIGRvYy5kZWZhdWx0VmlldyB8fCB3aW5kb3c7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/ownerWindow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/position.js":
/*!**************************************************!*\
  !*** ./node_modules/dom-helpers/esm/position.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ position)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./css */ \"(ssr)/./node_modules/dom-helpers/esm/css.js\");\n/* harmony import */ var _offset__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset */ \"(ssr)/./node_modules/dom-helpers/esm/offset.js\");\n/* harmony import */ var _offsetParent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./offsetParent */ \"(ssr)/./node_modules/dom-helpers/esm/offsetParent.js\");\n/* harmony import */ var _scrollLeft__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scrollLeft */ \"(ssr)/./node_modules/dom-helpers/esm/scrollLeft.js\");\n/* harmony import */ var _scrollTop__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./scrollTop */ \"(ssr)/./node_modules/dom-helpers/esm/scrollTop.js\");\n\n\n\n\n\n\n\nvar nodeName = function nodeName(node) {\n  return node.nodeName && node.nodeName.toLowerCase();\n};\n/**\n * Returns the relative position of a given element.\n * \n * @param node the element\n * @param offsetParent the offset parent\n */\n\n\nfunction position(node, offsetParent) {\n  var parentOffset = {\n    top: 0,\n    left: 0\n  };\n  var offset; // Fixed elements are offset from window (parentOffset = {top:0, left: 0},\n  // because it is its only offset parent\n\n  if ((0,_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, 'position') === 'fixed') {\n    offset = node.getBoundingClientRect();\n  } else {\n    var parent = offsetParent || (0,_offsetParent__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(node);\n    offset = (0,_offset__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node);\n    if (nodeName(parent) !== 'html') parentOffset = (0,_offset__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(parent);\n    var borderTop = String((0,_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(parent, 'borderTopWidth') || 0);\n    parentOffset.top += parseInt(borderTop, 10) - (0,_scrollTop__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(parent) || 0;\n    var borderLeft = String((0,_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(parent, 'borderLeftWidth') || 0);\n    parentOffset.left += parseInt(borderLeft, 10) - (0,_scrollLeft__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(parent) || 0;\n  }\n\n  var marginTop = String((0,_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, 'marginTop') || 0);\n  var marginLeft = String((0,_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, 'marginLeft') || 0); // Subtract parent offsets and node margins\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, offset, {\n    top: offset.top - parentOffset.top - (parseInt(marginTop, 10) || 0),\n    left: offset.left - parentOffset.left - (parseInt(marginLeft, 10) || 0)\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/position.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/querySelectorAll.js":
/*!**********************************************************!*\
  !*** ./node_modules/dom-helpers/esm/querySelectorAll.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ qsa)\n/* harmony export */ });\nvar toArray = Function.prototype.bind.call(Function.prototype.call, [].slice);\n/**\n * Runs `querySelectorAll` on a given element.\n * \n * @param element the element\n * @param selector the selector\n */\n\nfunction qsa(element, selector) {\n  return toArray(element.querySelectorAll(selector));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3F1ZXJ5U2VsZWN0b3JBbGwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9kb20taGVscGVycy9lc20vcXVlcnlTZWxlY3RvckFsbC5qcz82Yjc5Il0sInNvdXJjZXNDb250ZW50IjpbInZhciB0b0FycmF5ID0gRnVuY3Rpb24ucHJvdG90eXBlLmJpbmQuY2FsbChGdW5jdGlvbi5wcm90b3R5cGUuY2FsbCwgW10uc2xpY2UpO1xuLyoqXG4gKiBSdW5zIGBxdWVyeVNlbGVjdG9yQWxsYCBvbiBhIGdpdmVuIGVsZW1lbnQuXG4gKiBcbiAqIEBwYXJhbSBlbGVtZW50IHRoZSBlbGVtZW50XG4gKiBAcGFyYW0gc2VsZWN0b3IgdGhlIHNlbGVjdG9yXG4gKi9cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcXNhKGVsZW1lbnQsIHNlbGVjdG9yKSB7XG4gIHJldHVybiB0b0FycmF5KGVsZW1lbnQucXVlcnlTZWxlY3RvckFsbChzZWxlY3RvcikpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/querySelectorAll.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/removeClass.js":
/*!*****************************************************!*\
  !*** ./node_modules/dom-helpers/esm/removeClass.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ removeClass)\n/* harmony export */ });\nfunction replaceClassName(origClass, classToRemove) {\n  return origClass.replace(new RegExp(\"(^|\\\\s)\" + classToRemove + \"(?:\\\\s|$)\", 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\n}\n/**\n * Removes a CSS class from a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\n\nfunction removeClass(element, className) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else if (typeof element.className === 'string') {\n    element.className = replaceClassName(element.className, className);\n  } else {\n    element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3JlbW92ZUNsYXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdlO0FBQ2Y7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3JlbW92ZUNsYXNzLmpzPzg3MWEiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVwbGFjZUNsYXNzTmFtZShvcmlnQ2xhc3MsIGNsYXNzVG9SZW1vdmUpIHtcbiAgcmV0dXJuIG9yaWdDbGFzcy5yZXBsYWNlKG5ldyBSZWdFeHAoXCIoXnxcXFxccylcIiArIGNsYXNzVG9SZW1vdmUgKyBcIig/OlxcXFxzfCQpXCIsICdnJyksICckMScpLnJlcGxhY2UoL1xccysvZywgJyAnKS5yZXBsYWNlKC9eXFxzKnxcXHMqJC9nLCAnJyk7XG59XG4vKipcbiAqIFJlbW92ZXMgYSBDU1MgY2xhc3MgZnJvbSBhIGdpdmVuIGVsZW1lbnQuXG4gKiBcbiAqIEBwYXJhbSBlbGVtZW50IHRoZSBlbGVtZW50XG4gKiBAcGFyYW0gY2xhc3NOYW1lIHRoZSBDU1MgY2xhc3MgbmFtZVxuICovXG5cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcmVtb3ZlQ2xhc3MoZWxlbWVudCwgY2xhc3NOYW1lKSB7XG4gIGlmIChlbGVtZW50LmNsYXNzTGlzdCkge1xuICAgIGVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZShjbGFzc05hbWUpO1xuICB9IGVsc2UgaWYgKHR5cGVvZiBlbGVtZW50LmNsYXNzTmFtZSA9PT0gJ3N0cmluZycpIHtcbiAgICBlbGVtZW50LmNsYXNzTmFtZSA9IHJlcGxhY2VDbGFzc05hbWUoZWxlbWVudC5jbGFzc05hbWUsIGNsYXNzTmFtZSk7XG4gIH0gZWxzZSB7XG4gICAgZWxlbWVudC5zZXRBdHRyaWJ1dGUoJ2NsYXNzJywgcmVwbGFjZUNsYXNzTmFtZShlbGVtZW50LmNsYXNzTmFtZSAmJiBlbGVtZW50LmNsYXNzTmFtZS5iYXNlVmFsIHx8ICcnLCBjbGFzc05hbWUpKTtcbiAgfVxufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/removeClass.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/removeEventListener.js":
/*!*************************************************************!*\
  !*** ./node_modules/dom-helpers/esm/removeEventListener.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * A `removeEventListener` ponyfill\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction removeEventListener(node, eventName, handler, options) {\n  var capture = options && typeof options !== 'boolean' ? options.capture : options;\n  node.removeEventListener(eventName, handler, capture);\n\n  if (handler.__once) {\n    node.removeEventListener(eventName, handler.__once, capture);\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (removeEventListener);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3JlbW92ZUV2ZW50TGlzdGVuZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsbUJBQW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2RvbS1oZWxwZXJzL2VzbS9yZW1vdmVFdmVudExpc3RlbmVyLmpzP2Q0NWQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBIGByZW1vdmVFdmVudExpc3RlbmVyYCBwb255ZmlsbFxuICogXG4gKiBAcGFyYW0gbm9kZSB0aGUgZWxlbWVudFxuICogQHBhcmFtIGV2ZW50TmFtZSB0aGUgZXZlbnQgbmFtZVxuICogQHBhcmFtIGhhbmRsZSB0aGUgaGFuZGxlclxuICogQHBhcmFtIG9wdGlvbnMgZXZlbnQgb3B0aW9uc1xuICovXG5mdW5jdGlvbiByZW1vdmVFdmVudExpc3RlbmVyKG5vZGUsIGV2ZW50TmFtZSwgaGFuZGxlciwgb3B0aW9ucykge1xuICB2YXIgY2FwdHVyZSA9IG9wdGlvbnMgJiYgdHlwZW9mIG9wdGlvbnMgIT09ICdib29sZWFuJyA/IG9wdGlvbnMuY2FwdHVyZSA6IG9wdGlvbnM7XG4gIG5vZGUucmVtb3ZlRXZlbnRMaXN0ZW5lcihldmVudE5hbWUsIGhhbmRsZXIsIGNhcHR1cmUpO1xuXG4gIGlmIChoYW5kbGVyLl9fb25jZSkge1xuICAgIG5vZGUucmVtb3ZlRXZlbnRMaXN0ZW5lcihldmVudE5hbWUsIGhhbmRsZXIuX19vbmNlLCBjYXB0dXJlKTtcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCByZW1vdmVFdmVudExpc3RlbmVyOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/removeEventListener.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/scrollLeft.js":
/*!****************************************************!*\
  !*** ./node_modules/dom-helpers/esm/scrollLeft.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _getScrollAccessor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getScrollAccessor */ \"(ssr)/./node_modules/dom-helpers/esm/getScrollAccessor.js\");\n\n/**\n * Gets or sets the scroll left position of a given element.\n * \n * @param node the element\n * @param val the position to set\n */\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_getScrollAccessor__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('pageXOffset'));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3Njcm9sbExlZnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0Q7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGlFQUFlLDhEQUFpQixlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2RvbS1oZWxwZXJzL2VzbS9zY3JvbGxMZWZ0LmpzP2ZjNTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGdldFNjcm9sbEFjY2Vzc29yIGZyb20gJy4vZ2V0U2Nyb2xsQWNjZXNzb3InO1xuLyoqXG4gKiBHZXRzIG9yIHNldHMgdGhlIHNjcm9sbCBsZWZ0IHBvc2l0aW9uIG9mIGEgZ2l2ZW4gZWxlbWVudC5cbiAqIFxuICogQHBhcmFtIG5vZGUgdGhlIGVsZW1lbnRcbiAqIEBwYXJhbSB2YWwgdGhlIHBvc2l0aW9uIHRvIHNldFxuICovXG5cbmV4cG9ydCBkZWZhdWx0IGdldFNjcm9sbEFjY2Vzc29yKCdwYWdlWE9mZnNldCcpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/scrollLeft.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/scrollTop.js":
/*!***************************************************!*\
  !*** ./node_modules/dom-helpers/esm/scrollTop.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _getScrollAccessor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getScrollAccessor */ \"(ssr)/./node_modules/dom-helpers/esm/getScrollAccessor.js\");\n\n/**\n * Gets or sets the scroll top position of a given element.\n * \n * @param node the element\n * @param val the position to set\n */\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_getScrollAccessor__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('pageYOffset'));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3Njcm9sbFRvcC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsOERBQWlCLGVBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3Njcm9sbFRvcC5qcz9mOThjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRTY3JvbGxBY2Nlc3NvciBmcm9tICcuL2dldFNjcm9sbEFjY2Vzc29yJztcbi8qKlxuICogR2V0cyBvciBzZXRzIHRoZSBzY3JvbGwgdG9wIHBvc2l0aW9uIG9mIGEgZ2l2ZW4gZWxlbWVudC5cbiAqIFxuICogQHBhcmFtIG5vZGUgdGhlIGVsZW1lbnRcbiAqIEBwYXJhbSB2YWwgdGhlIHBvc2l0aW9uIHRvIHNldFxuICovXG5cbmV4cG9ydCBkZWZhdWx0IGdldFNjcm9sbEFjY2Vzc29yKCdwYWdlWU9mZnNldCcpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/scrollTop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/scrollbarSize.js":
/*!*******************************************************!*\
  !*** ./node_modules/dom-helpers/esm/scrollbarSize.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ scrollbarSize)\n/* harmony export */ });\n/* harmony import */ var _canUseDOM__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./canUseDOM */ \"(ssr)/./node_modules/dom-helpers/esm/canUseDOM.js\");\n\nvar size;\nfunction scrollbarSize(recalc) {\n  if (!size && size !== 0 || recalc) {\n    if (_canUseDOM__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n      var scrollDiv = document.createElement('div');\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.top = '-9999px';\n      scrollDiv.style.width = '50px';\n      scrollDiv.style.height = '50px';\n      scrollDiv.style.overflow = 'scroll';\n      document.body.appendChild(scrollDiv);\n      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n    }\n  }\n\n  return size;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3Njcm9sbGJhclNpemUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFDcEM7QUFDZTtBQUNmO0FBQ0EsUUFBUSxrREFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3Njcm9sbGJhclNpemUuanM/N2ZiOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY2FuVXNlRE9NIGZyb20gJy4vY2FuVXNlRE9NJztcbnZhciBzaXplO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc2Nyb2xsYmFyU2l6ZShyZWNhbGMpIHtcbiAgaWYgKCFzaXplICYmIHNpemUgIT09IDAgfHwgcmVjYWxjKSB7XG4gICAgaWYgKGNhblVzZURPTSkge1xuICAgICAgdmFyIHNjcm9sbERpdiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpO1xuICAgICAgc2Nyb2xsRGl2LnN0eWxlLnBvc2l0aW9uID0gJ2Fic29sdXRlJztcbiAgICAgIHNjcm9sbERpdi5zdHlsZS50b3AgPSAnLTk5OTlweCc7XG4gICAgICBzY3JvbGxEaXYuc3R5bGUud2lkdGggPSAnNTBweCc7XG4gICAgICBzY3JvbGxEaXYuc3R5bGUuaGVpZ2h0ID0gJzUwcHgnO1xuICAgICAgc2Nyb2xsRGl2LnN0eWxlLm92ZXJmbG93ID0gJ3Njcm9sbCc7XG4gICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHNjcm9sbERpdik7XG4gICAgICBzaXplID0gc2Nyb2xsRGl2Lm9mZnNldFdpZHRoIC0gc2Nyb2xsRGl2LmNsaWVudFdpZHRoO1xuICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChzY3JvbGxEaXYpO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBzaXplO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/scrollbarSize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-helpers/esm/width.js":
/*!***********************************************!*\
  !*** ./node_modules/dom-helpers/esm/width.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getWidth)\n/* harmony export */ });\n/* harmony import */ var _isWindow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isWindow */ \"(ssr)/./node_modules/dom-helpers/esm/isWindow.js\");\n/* harmony import */ var _offset__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./offset */ \"(ssr)/./node_modules/dom-helpers/esm/offset.js\");\n\n\n/**\n * Returns the width of a given element.\n * \n * @param node the element\n * @param client whether to use `clientWidth` if possible\n */\n\nfunction getWidth(node, client) {\n  var win = (0,_isWindow__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node);\n  return win ? win.innerWidth : client ? node.clientWidth : (0,_offset__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node).width;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tLWhlbHBlcnMvZXNtL3dpZHRoLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtQztBQUNMO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZTtBQUNmLFlBQVkscURBQVM7QUFDckIsNERBQTRELG1EQUFNO0FBQ2xFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2RvbS1oZWxwZXJzL2VzbS93aWR0aC5qcz83YTNhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRXaW5kb3cgZnJvbSAnLi9pc1dpbmRvdyc7XG5pbXBvcnQgb2Zmc2V0IGZyb20gJy4vb2Zmc2V0Jztcbi8qKlxuICogUmV0dXJucyB0aGUgd2lkdGggb2YgYSBnaXZlbiBlbGVtZW50LlxuICogXG4gKiBAcGFyYW0gbm9kZSB0aGUgZWxlbWVudFxuICogQHBhcmFtIGNsaWVudCB3aGV0aGVyIHRvIHVzZSBgY2xpZW50V2lkdGhgIGlmIHBvc3NpYmxlXG4gKi9cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2V0V2lkdGgobm9kZSwgY2xpZW50KSB7XG4gIHZhciB3aW4gPSBnZXRXaW5kb3cobm9kZSk7XG4gIHJldHVybiB3aW4gPyB3aW4uaW5uZXJXaWR0aCA6IGNsaWVudCA/IG5vZGUuY2xpZW50V2lkdGggOiBvZmZzZXQobm9kZSkud2lkdGg7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-helpers/esm/width.js\n");

/***/ })

};
;