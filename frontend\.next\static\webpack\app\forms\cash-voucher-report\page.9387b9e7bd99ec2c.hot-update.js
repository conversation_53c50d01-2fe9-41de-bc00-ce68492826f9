"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @src/components/Custom/ServerPaginatedTable */ \"(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx\");\n/* harmony import */ var _src_components_Custom_VoucherDetailsModal_index__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @src/components/Custom/VoucherDetailsModal/index */ \"(app-pages-browser)/./src/components/Custom/VoucherDetailsModal/index.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        totalCount: 0,\n        totalPages: 0\n    });\n    const [selectedVoucher, setSelectedVoucher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voucherDetails, setVoucherDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentHistory, setPaymentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalLoading, setModalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const fetchData = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n            console.log(response.data);\n            if (response.data && response.data.data) {\n                setData(response.data.data);\n                setPagination({\n                    page: response.data.page,\n                    pageSize: response.data.pageSize,\n                    totalCount: response.data.totalCount,\n                    totalPages: response.data.totalPages\n                });\n            } else {\n                setData([]);\n                setPagination({\n                    page: 1,\n                    pageSize: 20,\n                    totalCount: 0,\n                    totalPages: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch cash voucher report data\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchData();\n    }, []);\n    const fetchVoucherDetails = async (voucherNo)=>{\n        try {\n            setModalLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/voucher-details?voucherNo=\".concat(voucherNo));\n            if (response.data) {\n                setVoucherDetails(response.data.voucher_details);\n                setPaymentHistory(response.data.payment_history || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching voucher details: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch voucher details\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setModalLoading(false);\n        }\n    };\n    const handleOpenModal = async (voucher)=>{\n        setSelectedVoucher(voucher);\n        setIsModalOpen(true);\n        await fetchVoucherDetails(voucher.voucher_no);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedVoucher(null);\n        setVoucherDetails(null);\n        setPaymentHistory([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"voucher_no\"\n        },\n        {\n            header: \"Quotation No (adj)\",\n            field: \"quotation_no\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"client_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"client_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"gross_amount\",\n            render: (item)=>formatCurrency(item.gross_amount)\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"paid_amount\",\n            render: (item)=>formatCurrency(item.paid_amount)\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"remaining_amount\",\n            render: (item)=>formatCurrency(item.remaining_amount)\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        },\n        {\n            header: \"Actions\",\n            field: \"actions\",\n            render: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                    colorScheme: \"blue\",\n                    size: \"sm\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleOpenModal(item);\n                    },\n                    children: \"View Details\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 148,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    const getRowCursor = ()=>\"pointer\";\n    const handleRowClick = (item)=>{\n        handleOpenModal(item);\n    };\n    const handlePageChange = (newPage)=>{\n        fetchData(newPage, pagination.pageSize);\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        fetchData(1, newPageSize);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 179,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Cash Voucher Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            data: data,\n                                            columns: columns,\n                                            pagination: pagination,\n                                            onPageChange: handlePageChange,\n                                            onPageSizeChange: handlePageSizeChange,\n                                            onRowClick: handleRowClick,\n                                            getRowCursor: getRowCursor,\n                                            getBadgeColor: getStatusColor,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                lineNumber: 185,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 183,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 182,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_VoucherDetailsModal_index__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: isModalOpen,\n                    onClose: handleCloseModal,\n                    selectedVoucher: selectedVoucher,\n                    voucherDetails: voucherDetails,\n                    paymentHistory: paymentHistory,\n                    modalLoading: modalLoading\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"to0F4F3T6vvp+5aeff+YHPpyvK4=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Custom/VoucherDetailsModal/index.jsx":
/*!*************************************************************!*\
  !*** ./src/components/Custom/VoucherDetailsModal/index.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/icon/icon.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/spinner/spinner.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/card/card.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/card/card-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/grid/grid-item.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table-container.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/react */ \"(app-pages-browser)/./node_modules/@emotion/react/dist/emotion-react.browser.development.esm.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiCreditCard,FiDollarSign,FiFileText,FiTrendingUp,FiUser!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  from { transform: scale(0.95); opacity: 0; }\\n  to { transform: scale(1); opacity: 1; }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  from { opacity: 0; }\\n  to { opacity: 1; }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n\n\n\n// Define animations\nconst slideIn = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_4__.keyframes)(_templateObject());\nconst fadeIn = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_4__.keyframes)(_templateObject1());\nconst VoucherDetailsModal = (param)=>{\n    let { isOpen, onClose, selectedVoucher, voucherDetails, paymentHistory, modalLoading } = param;\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const getPaymentTypeColor = (type)=>{\n        switch(type){\n            case \"CR\":\n                return \"green\";\n            case \"CP\":\n                return \"blue\";\n            default:\n                return \"gray\";\n        }\n    };\n    const getPaymentTypeLabel = (type)=>{\n        switch(type){\n            case \"CR\":\n                return \"Cash Receipt\";\n            case \"CP\":\n                return \"Cash Payment\";\n            default:\n                return type || \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"6xl\",\n        scrollBehavior: \"inside\",\n        motionPreset: \"scale\",\n        isCentered: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalOverlay, {\n                bg: \"blackAlpha.300\",\n                backdropFilter: \"blur(10px)\",\n                sx: {\n                    animation: \"\".concat(fadeIn, \" 0.2s ease-out\")\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.ModalContent, {\n                maxH: \"90vh\",\n                my: 4,\n                sx: {\n                    animation: \"\".concat(slideIn, \" 0.3s ease-out\"),\n                    bg: \"white\",\n                    boxShadow: \"xl\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.ModalHeader, {\n                        bgGradient: \"linear(to-r, #2B6CB0, #1A365D)\",\n                        color: \"white\",\n                        borderTopRadius: \"md\",\n                        px: 6,\n                        py: 4,\n                        position: \"sticky\",\n                        top: 0,\n                        zIndex: 1,\n                        flexShrink: 0,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Flex, {\n                            align: \"center\",\n                            gap: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                    as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiFileText,\n                                    boxSize: 6\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                    align: \"start\",\n                                    spacing: 0,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            fontSize: \"xl\",\n                                            fontWeight: \"bold\",\n                                            children: \"Voucher Details\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            fontSize: \"sm\",\n                                            opacity: 0.9,\n                                            children: (selectedVoucher === null || selectedVoucher === void 0 ? void 0 : selectedVoucher.voucher_no) || \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                            lineNumber: 124,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                        lineNumber: 113,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.ModalCloseButton, {\n                        color: \"white\",\n                        _hover: {\n                            bg: \"whiteAlpha.300\",\n                            transform: \"rotate(90deg)\"\n                        },\n                        transition: \"all 0.2s\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                        lineNumber: 136,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalBody, {\n                        px: 6,\n                        py: 6,\n                        flex: \"1\",\n                        overflowY: \"auto\",\n                        sx: {\n                            \"&::-webkit-scrollbar\": {\n                                width: \"6px\"\n                            },\n                            \"&::-webkit-scrollbar-track\": {\n                                background: \"#f1f1f1\",\n                                borderRadius: \"4px\"\n                            },\n                            \"&::-webkit-scrollbar-thumb\": {\n                                background: \"#2B6CB0\",\n                                borderRadius: \"4px\",\n                                \"&:hover\": {\n                                    background: \"#1A365D\"\n                                }\n                            }\n                        },\n                        children: modalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Flex, {\n                            justify: \"center\",\n                            align: \"center\",\n                            minH: \"300px\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                spacing: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Spinner, {\n                                        size: \"xl\",\n                                        color: \"blue.500\",\n                                        thickness: \"4px\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                        color: \"gray.600\",\n                                        fontSize: \"lg\",\n                                        children: \"Loading voucher details...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                lineNumber: 169,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                            lineNumber: 168,\n                            columnNumber: 25\n                        }, undefined) : voucherDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                            spacing: 8,\n                            align: \"stretch\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                                    variant: \"elevated\",\n                                    shadow: \"lg\",\n                                    borderTop: \"4px solid\",\n                                    borderTopColor: \"blue.500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.CardBody, {\n                                        p: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                            spacing: 6,\n                                            align: \"stretch\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Flex, {\n                                                    justify: \"space-between\",\n                                                    align: \"center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fontSize: \"xl\",\n                                                            fontWeight: \"bold\",\n                                                            color: \"blue.600\",\n                                                            children: \"Voucher Summary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Badge, {\n                                                            colorScheme: getStatusColor(voucherDetails.status),\n                                                            fontSize: \"md\",\n                                                            px: 4,\n                                                            py: 2,\n                                                            borderRadius: \"full\",\n                                                            textTransform: \"uppercase\",\n                                                            fontWeight: \"bold\",\n                                                            children: voucherDetails.status\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Grid, {\n                                                    templateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                                                    gap: 6,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.GridItem, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.HStack, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                                as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiFileText,\n                                                                                color: \"blue.500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 206,\n                                                                                columnNumber: 57\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: \"gray.600\",\n                                                                                fontWeight: \"600\",\n                                                                                children: \"Voucher No\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 57\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        children: voucherDetails.voucher_no\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.GridItem, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.HStack, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                                as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiFileText,\n                                                                                color: \"purple.500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 216,\n                                                                                columnNumber: 57\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: \"gray.600\",\n                                                                                fontWeight: \"600\",\n                                                                                children: \"Quotation No\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 217,\n                                                                                columnNumber: 57\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        children: voucherDetails.quotation_no || \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.GridItem, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.HStack, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                                as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiUser,\n                                                                                color: \"green.500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 57\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: \"gray.600\",\n                                                                                fontWeight: \"600\",\n                                                                                children: \"Client\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 57\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        children: voucherDetails.client_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.500\",\n                                                                        children: [\n                                                                            \"ID: \",\n                                                                            voucherDetails.client_id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.GridItem, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.HStack, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                                as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCalendar,\n                                                                                color: \"orange.500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 57\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: \"gray.600\",\n                                                                                fontWeight: \"600\",\n                                                                                children: \"Created Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 238,\n                                                                                columnNumber: 57\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        children: voucherDetails.created_at ? dayjs__WEBPACK_IMPORTED_MODULE_3___default()(voucherDetails.created_at).format(\"DD MMM, YYYY\") : \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Divider, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Grid, {\n                                                    templateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                                                    gap: 6,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.GridItem, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                                                                bg: \"blue.50\",\n                                                                borderLeft: \"4px solid\",\n                                                                borderLeftColor: \"blue.500\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.CardBody, {\n                                                                    p: 4,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                        align: \"start\",\n                                                                        spacing: 2,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.HStack, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                                        as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiDollarSign,\n                                                                                        color: \"blue.500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                        lineNumber: 259,\n                                                                                        columnNumber: 65\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        color: \"blue.600\",\n                                                                                        fontWeight: \"600\",\n                                                                                        children: \"Gross Amount\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 65\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 61\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                fontWeight: \"bold\",\n                                                                                fontSize: \"2xl\",\n                                                                                color: \"blue.700\",\n                                                                                children: formatCurrency(voucherDetails.gross_amount)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 61\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 53\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.GridItem, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                                                                bg: \"green.50\",\n                                                                borderLeft: \"4px solid\",\n                                                                borderLeftColor: \"green.500\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.CardBody, {\n                                                                    p: 4,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                        align: \"start\",\n                                                                        spacing: 2,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.HStack, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                                        as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiTrendingUp,\n                                                                                        color: \"green.500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                        lineNumber: 275,\n                                                                                        columnNumber: 65\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        color: \"green.600\",\n                                                                                        fontWeight: \"600\",\n                                                                                        children: \"Paid Amount\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                        lineNumber: 276,\n                                                                                        columnNumber: 65\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 274,\n                                                                                columnNumber: 61\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                fontWeight: \"bold\",\n                                                                                fontSize: \"2xl\",\n                                                                                color: \"green.700\",\n                                                                                children: formatCurrency(voucherDetails.paid_amount)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 278,\n                                                                                columnNumber: 61\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 53\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.GridItem, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                                                                bg: \"red.50\",\n                                                                borderLeft: \"4px solid\",\n                                                                borderLeftColor: \"red.500\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.CardBody, {\n                                                                    p: 4,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                        align: \"start\",\n                                                                        spacing: 2,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.HStack, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                                        as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCreditCard,\n                                                                                        color: \"red.500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                        lineNumber: 291,\n                                                                                        columnNumber: 65\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        color: \"red.600\",\n                                                                                        fontWeight: \"600\",\n                                                                                        children: \"Remaining Amount\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                        lineNumber: 292,\n                                                                                        columnNumber: 65\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 61\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                                fontWeight: \"bold\",\n                                                                                fontSize: \"2xl\",\n                                                                                color: \"red.700\",\n                                                                                children: formatCurrency(voucherDetails.remaining_amount)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 61\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 53\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                                    variant: \"elevated\",\n                                    shadow: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.CardBody, {\n                                        p: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                            spacing: 4,\n                                            align: \"stretch\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Flex, {\n                                                    justify: \"space-between\",\n                                                    align: \"center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fontSize: \"xl\",\n                                                            fontWeight: \"bold\",\n                                                            color: \"blue.600\",\n                                                            children: \"Payment History\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Badge, {\n                                                            colorScheme: \"blue\",\n                                                            variant: \"subtle\",\n                                                            fontSize: \"sm\",\n                                                            px: 3,\n                                                            py: 1,\n                                                            borderRadius: \"full\",\n                                                            children: [\n                                                                paymentHistory.length,\n                                                                \" payment\",\n                                                                paymentHistory.length !== 1 ? \"s\" : \"\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                paymentHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.TableContainer, {\n                                                    border: \"1px solid\",\n                                                    borderColor: \"gray.200\",\n                                                    borderRadius: \"lg\",\n                                                    bg: \"white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.Table, {\n                                                        variant: \"simple\",\n                                                        size: \"md\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Thead, {\n                                                                bg: \"gray.50\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Tr, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Th, {\n                                                                            color: \"gray.700\",\n                                                                            fontWeight: \"bold\",\n                                                                            children: \"Payment Voucher\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 61\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Th, {\n                                                                            color: \"gray.700\",\n                                                                            fontWeight: \"bold\",\n                                                                            children: \"Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 61\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Th, {\n                                                                            color: \"gray.700\",\n                                                                            fontWeight: \"bold\",\n                                                                            isNumeric: true,\n                                                                            children: \"Amount\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 61\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Th, {\n                                                                            color: \"gray.700\",\n                                                                            fontWeight: \"bold\",\n                                                                            children: \"Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 61\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Th, {\n                                                                            color: \"gray.700\",\n                                                                            fontWeight: \"bold\",\n                                                                            children: \"Description\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 61\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.Tbody, {\n                                                                children: paymentHistory.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Tr, {\n                                                                        _hover: {\n                                                                            bg: \"blue.50\"\n                                                                        },\n                                                                        transition: \"all 0.2s\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.Td, {\n                                                                                fontWeight: \"semibold\",\n                                                                                color: \"blue.700\",\n                                                                                children: payment.payment_voucher_no\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.Td, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Badge, {\n                                                                                    colorScheme: getPaymentTypeColor(payment.payment_type),\n                                                                                    variant: \"subtle\",\n                                                                                    px: 2,\n                                                                                    py: 1,\n                                                                                    borderRadius: \"md\",\n                                                                                    children: getPaymentTypeLabel(payment.payment_type)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                    lineNumber: 350,\n                                                                                    columnNumber: 69\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 349,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.Td, {\n                                                                                isNumeric: true,\n                                                                                fontWeight: \"bold\",\n                                                                                color: \"green.600\",\n                                                                                fontSize: \"lg\",\n                                                                                children: formatCurrency(payment.payment_amount)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.Td, {\n                                                                                color: \"gray.600\",\n                                                                                children: payment.payment_date ? dayjs__WEBPACK_IMPORTED_MODULE_3___default()(payment.payment_date).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.Td, {\n                                                                                color: \"gray.600\",\n                                                                                children: payment.payment_description || \"No description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 65\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 61\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 45\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                                                    bg: \"gray.50\",\n                                                    border: \"2px dashed\",\n                                                    borderColor: \"gray.300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.CardBody, {\n                                                        p: 12,\n                                                        textAlign: \"center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                            spacing: 4,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                                                    as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCreditCard,\n                                                                    boxSize: 12,\n                                                                    color: \"gray.400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 57\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                                                    spacing: 2,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                            color: \"gray.600\",\n                                                                            fontSize: \"lg\",\n                                                                            fontWeight: \"semibold\",\n                                                                            children: \"No payments found\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 61\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                            color: \"gray.500\",\n                                                                            fontSize: \"sm\",\n                                                                            children: \"This voucher is still pending payment\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 61\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                            lineNumber: 175,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                            bg: \"gray.50\",\n                            border: \"2px dashed\",\n                            borderColor: \"gray.300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.CardBody, {\n                                p: 12,\n                                textAlign: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.VStack, {\n                                    spacing: 4,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Icon, {\n                                            as: _barrel_optimize_names_FiCalendar_FiCreditCard_FiDollarSign_FiFileText_FiTrendingUp_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiFileText,\n                                            boxSize: 12,\n                                            color: \"gray.400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            color: \"gray.600\",\n                                            fontSize: \"lg\",\n                                            children: \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                                lineNumber: 400,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                            lineNumber: 399,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                        lineNumber: 145,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\VoucherDetailsModal\\\\index.jsx\",\n        lineNumber: 87,\n        columnNumber: 9\n    }, undefined);\n};\n_c = VoucherDetailsModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VoucherDetailsModal);\nvar _c;\n$RefreshReg$(_c, \"VoucherDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Custom/VoucherDetailsModal/index.jsx\n"));

/***/ })

});