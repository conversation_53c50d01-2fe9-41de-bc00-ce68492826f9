import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Box,
  Badge,
  Divider,
  Icon,
} from "@chakra-ui/react";
import React from 'react';
import { FaUser, FaPhone, FaEnvelope, FaGlobe, FaMobile, FaMapMarkerAlt, FaDollarSign, FaTags } from 'react-icons/fa';

const ViewLeadDetailsModal = ({ isOpen, onClose, clientData }) => {
  if (!clientData) return null;

  const InfoRow = ({ icon, label, value }) => (
    <HStack spacing={3} align="start">
      <Icon as={icon} color="blue.500" mt={1} />
      <Box flex={1}>
        <Text fontSize="sm" fontWeight="semibold" color="gray.600">
          {label}
        </Text>
        <Text fontSize="md" color="gray.800">
          {value || 'N/A'}
        </Text>
      </Box>
    </HStack>
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader style={{ background: "#071533", color: "white" }}>
          Lead Details
        </ModalHeader>
        <ModalCloseButton style={{ color: "white" }} />
        <ModalBody py={6} maxHeight={"60vh"} overflowY="auto">
          <VStack spacing={4} align="stretch">
            {/* Client Name */}
            <InfoRow 
              icon={FaUser} 
              label="Client Name" 
              value={clientData.name} 
            />
            
            <Divider />
            
            {/* Contact Information */}
            <Text fontSize="lg" fontWeight="bold" color="gray.700">
              Contact Information
            </Text>
            
            <InfoRow 
              icon={FaPhone} 
              label="Phone" 
              value={clientData.phone} 
            />
            
            <InfoRow 
              icon={FaMobile} 
              label="Mobile" 
              value={clientData.mobile} 
            />
            
            <InfoRow 
              icon={FaEnvelope} 
              label="Email" 
              value={clientData.email} 
            />
            
            <Divider />
            
            {/* Business Information */}
            <Text fontSize="lg" fontWeight="bold" color="gray.700">
              Business Information
            </Text>
            
            <InfoRow 
              icon={FaGlobe} 
              label="Website" 
              value={clientData.website} 
            />
            
            <InfoRow 
              icon={FaGlobe} 
              label="Portal Link" 
              value={clientData.portallink} 
            />
            
            <InfoRow 
              icon={FaDollarSign} 
              label="Currency" 
              value={clientData.currency} 
            />
            
            <Divider />
            
            {/* Additional Information */}
            <Text fontSize="lg" fontWeight="bold" color="gray.700">
              Additional Information
            </Text>
            
            <InfoRow 
              icon={FaMapMarkerAlt} 
              label="Shipping Address" 
              value={clientData.shipping} 
            />
            
            {clientData.tags && (
              <HStack spacing={3} align="start">
                <Icon as={FaTags} color="blue.500" mt={1} />
                <Box flex={1}>
                  <Text fontSize="sm" fontWeight="semibold" color="gray.600">
                    Tags
                  </Text>
                  <Badge colorScheme="blue" variant="subtle">
                    {clientData.tags}
                  </Badge>
                </Box>
              </HStack>
            )}
            
            {/* Client ID */}
            <InfoRow 
              icon={FaUser} 
              label="Client ID" 
              value={clientData.id} 
            />
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ViewLeadDetailsModal;
