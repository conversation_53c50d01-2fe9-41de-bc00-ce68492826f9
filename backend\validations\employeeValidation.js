const { body, validationResult } = require('express-validator');

const validate = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

const employeeValidationRules = {
    create: [
        body('employeeId').notEmpty().isString().withMessage('Employee ID is required'),
        body('username').notEmpty().isString().isLength({ max: 8 }).withMessage('Username is required and cannot exceed 8 characters'),
        body('firstName').notEmpty().isString().withMessage('First name is required'),
        body('lastName').notEmpty().isString().withMessage('Last name is required'),
        body('password').notEmpty().isString().withMessage('Password is required'),
        body('gender').notEmpty().isInt({ min: 0, max: 2 }).withMessage('Gender must be 0, 1, or 2'),
        body('email').notEmpty().isEmail().withMessage('Valid email is required'),
        body('abn').notEmpty().isString().withMessage('ABN is required'),
        body('licenseNo').notEmpty().isString().withMessage('License is required'),
        body('phoneNo').notEmpty().isString().withMessage('Phone number is required'),
        body('roleId').notEmpty().isInt().withMessage('Role ID must be an integer'),
        validate
    ]
};

module.exports = employeeValidationRules;
