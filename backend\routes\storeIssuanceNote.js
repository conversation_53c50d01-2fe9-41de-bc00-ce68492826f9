const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { storeIssuanceNoteColumns, storeIssuanceNoteItemColumns } = require('./utils/constant')

router.use(authorization);

// ------------------- Store Issuance Note (SI) APIS ------------------- //

router.post('/getVoucherNo', async (req, res) => {
    const { Mnth } = req.body;
    const query = 'select MAX(vno) AS vno from SI where Mnth = @Mnth';

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('Mnth', sql.VarChar(4), Mnth);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json({
            vtp: 'IS',
            location: 'EAM',
            vno: result.recordset[0].vno ? result.recordset[0].vno + 1 : 1,
            Mnth,
            voucherNo: 'IS' + '/' + Mnth + '/EAM/' + (result.recordset[0].vno ? result.recordset[0].vno + 1 : 1)
        });
    });
});

router.post('/navigate', async (req, res) => {
    const { next, prev, first, last, voucher_no } = req.body;
    const pool = await getPool();
    const request = new sql.Request(pool);

    let query = '';
    const baseQuery = `
        SELECT
        TOP 1
            SI.*,
            E.Title AS Emp_Title
        FROM 
            SI
        LEFT JOIN 
            EmployeeDetails E ON E.ID = SI.Emp_ID
    `;

    if ((next && (prev || first || last)) || (prev && (first || last)) || (first && last) || (!next && !prev && !first && !last)) {
        return res.status(400).json({ message: "Invalid request. Use either 'next', 'prev', 'first', or 'last' exclusively, and provide 'voucher_no' if using 'next' or 'prev'." });
    }

    if (next) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'next'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = parts[2];
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                SI.vtp > @vtp 
                OR (SI.vtp = @vtp AND SI.Mnth > @Mnth)
                OR (SI.vtp = @vtp AND SI.Mnth = @Mnth AND SI.Location > @Location)
                OR (SI.vtp = @vtp AND SI.Mnth = @Mnth AND SI.Location = @Location AND SI.vno > @vno)
            ) AND SI.vtp = @vtp
            ORDER BY SI.vtp, SI.Mnth, SI.Location, SI.vno;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (prev) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'prev'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = parts[2];
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                SI.vtp < @vtp 
                OR (SI.vtp = @vtp AND SI.Mnth < @Mnth)
                OR (SI.vtp = @vtp AND SI.Mnth = @Mnth AND SI.Location < @Location)
                OR (SI.vtp = @vtp AND SI.Mnth = @Mnth AND SI.Location = @Location AND SI.vno < @vno)
            ) AND SI.vtp = @vtp
            ORDER BY SI.vtp DESC, SI.Mnth DESC, SI.Location DESC, SI.vno DESC;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (first) {
        query = `${baseQuery} ORDER BY SI.VTP, SI.Mnth, SI.Location, SI.vno;`;
    } else if (last) {
        query = `${baseQuery} ORDER BY SI.VTP DESC, SI.Mnth DESC, SI.Location DESC, SI.vno DESC;`;
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }

        if (result.recordset.length > 0) {
            const order = result.recordset[0];
            const orderVoucherNo = order.Voucher_No;

            const detailsQuery = `
            SELECT
                SI_det.*,
                C.Title AS item_title,
                C.Unit AS item_unit
            FROM 
                SI_det
            LEFT JOIN 
                Coa31 C ON C.id = SI_det.item_id
            WHERE voucher_no = @orderVoucherNo`;
            const detailsRequest = new sql.Request(pool);
            detailsRequest.input('orderVoucherNo', sql.VarChar, orderVoucherNo);

            detailsRequest.query(detailsQuery, (detailsErr, detailsResult) => {
                if (detailsErr) {
                    return res.status(500).send(detailsErr);
                }

                order["items"] = detailsResult.recordset;
                res.status(200).json(order);
            });
        } else {
            res.status(404).json({ message: 'No more records available in this direction.' });
        }
    });
});

router.get('/', async (req, res) => {
    const query = 'SELECT * FROM SI';

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/create', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        let columns = [];
        let values = [];

        storeIssuanceNoteColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columns.push(name);
                values.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const orderQuery = `INSERT INTO SI (${columns.join(', ')}) VALUES (${values.join(', ')})`;

        await request.query(orderQuery);

        const { items } = req.body;

        if (!items || items.length === 0) {
            throw new Error('No items provided.');
        }

        const itemColumnsArray = [];
        const itemValuesArray = [];
        const paramsArray = [];

        storeIssuanceNoteItemColumns.forEach(({ name }) => {
            if (!itemColumnsArray.includes(name)) {
                itemColumnsArray.push(name);
            }
        });

        items.forEach((item, index) => {
            const rowValues = [];
            itemColumnsArray.forEach((column) => {
                if (item[column] !== undefined) {
                    rowValues.push(`@${column}${index}`);
                    paramsArray.push({ name: `${column}${index}`, value: item[column] });
                } else {
                    rowValues.push('NULL');
                }
            });
            itemValuesArray.push(`(${rowValues.join(', ')})`);
        });

        const itemQuery = `
            INSERT INTO SI_det (${itemColumnsArray.join(', ')}) 
            VALUES ${itemValuesArray.join(', ')};
        `;

        paramsArray.forEach(({ name, value }) => {
            request.input(name, value);
        });

        await request.query(itemQuery);

        await transaction.commit();

        res.status(201).json({
            message: 'Voucher and items successfully created.',
            vtp: req.body['VTP'],
            mnth: req.body['Mnth'],
            location: req.body['Location'],
            vno: req.body['Vno']
        });
    } catch (err) {
        await transaction.rollback();

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else if (err.message === 'No items provided.') {
            res.status(400).send(err.message);
        } else {
            res.status(500).send(err.message);
        }
    }
});

router.put('/update', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        const voucherNo = req.query.voucherNo;

        await transaction.begin();
        const request = new sql.Request(transaction);

        let setClause = [];
        storeIssuanceNoteColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                setClause.push(`${name} = @${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        if (setClause.length === 0) {
            return res.status(400).send('No fields to update.');
        }

        const storeIssuanceNoteQuery = `UPDATE SI SET ${setClause.join(', ')} WHERE Voucher_No = @Voucher_No`;
        request.input('Voucher_No', sql.VarChar(50), voucherNo);

        const storeIssuanceNoteResult = await request.query(storeIssuanceNoteQuery);

        if (storeIssuanceNoteResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Voucher not found.');
        }

        const { items } = req.body;

        if (items && Array.isArray(items) && items.length > 0) {
            const existingItemsQuery = `
                SELECT DVoucher_No
                FROM SI_det
                WHERE Voucher_No = @Voucher_No
            `;
            const existingItemsRequest = new sql.Request(transaction);
            existingItemsRequest.input('Voucher_No', sql.VarChar(50), voucherNo);
            const existingItemsResult = await existingItemsRequest.query(existingItemsQuery);

            const existingDVoucherNos = existingItemsResult.recordset.map(row => row.DVoucher_No);
            const itemsToUpdate = [];
            const itemsToInsert = [];
            const newDVoucherNos = new Set();

            items.forEach(item => {
                const { VTP, Mnth, Location, Vno, Srno } = item;
                const DVoucher_No = `${VTP}/${Mnth}/${Location}/${Vno}/${Srno}`;
                newDVoucherNos.add(DVoucher_No);
                if (existingDVoucherNos.includes(DVoucher_No)) {
                    itemsToUpdate.push({ ...item, DVoucher_No });
                } else {
                    itemsToInsert.push({ ...item, DVoucher_No });
                }
            });

            const itemsToDelete = existingDVoucherNos.filter(dvNo => !newDVoucherNos.has(dvNo));
            for (const item of itemsToUpdate) {
                const updateItemColumns = [];
                storeIssuanceNoteItemColumns.forEach(({ name, editable }) => {
                    if (editable && name !== 'DVoucher_No' && item[name] !== undefined) {
                        updateItemColumns.push(`${name} = @${name}`);
                    }
                });

                if (updateItemColumns.length > 0) {
                    const updateItemQuery = `
                        UPDATE SI_det
                        SET ${updateItemColumns.join(', ')}
                        WHERE DVoucher_No = @DVoucher_No
                    `;

                    const updateRequest = new sql.Request(transaction);
                    updateRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                    storeIssuanceNoteItemColumns.forEach(({ name }) => {
                        if (item[name] !== undefined) {
                            updateRequest.input(name, item[name]);
                        }
                    });

                    await updateRequest.query(updateItemQuery);
                }
            }

            for (const DVoucher_No of itemsToDelete) {
                const deleteItemQuery = `
                    DELETE FROM SI_det
                    WHERE DVoucher_No = @DVoucher_No
                `;

                const deleteRequest = new sql.Request(transaction);
                deleteRequest.input('DVoucher_No', sql.VarChar(50), DVoucher_No);

                await deleteRequest.query(deleteItemQuery);
            }

            for (const item of itemsToInsert) {
                const insertItemColumns = [];
                const insertItemValues = [];

                storeIssuanceNoteItemColumns.forEach(({ name }) => {
                    if (name !== 'DVoucher_No' && item[name] !== undefined) {
                        insertItemColumns.push(name);
                        insertItemValues.push(`@${name}`);
                    }
                });

                const insertItemQuery = `
                    INSERT INTO SI_det (${[...insertItemColumns].join(', ')})
                    VALUES (${[...insertItemValues].join(', ')})
                `;

                const insertRequest = new sql.Request(transaction);
                insertRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                storeIssuanceNoteItemColumns.forEach(({ name }) => {
                    if (item[name] !== undefined) {
                        insertRequest.input(name, item[name]);
                    }
                });

                await insertRequest.query(insertItemQuery);
            }
        }

        await transaction.commit();
        res.status(200).send('Voucher and items updated successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.delete('/delete', async (req, res) => {
    const voucherNo = req.query.voucherNo;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const deleteItemsQuery = 'DELETE FROM SI_det WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(16), voucherNo);
        const deleteItemsResult = await request.query(deleteItemsQuery);
        const deleteOrderQuery = 'DELETE FROM SI WHERE Voucher_No = @Voucher_No';
        const deleteOrderResult = await request.query(deleteOrderQuery);
        if (deleteItemsResult.rowsAffected[0] === 0 && deleteOrderResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Voucher not found or already deleted.');
        }
        await transaction.commit();
        res.status(200).send('Voucher and associated items deleted successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

module.exports = router;
