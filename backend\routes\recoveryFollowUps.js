const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { recoveryFollowUpsColumns, recoveryFollowUpsItemColumns } = require('./utils/constant')

router.use(authorization);

// ------------------- Recovery Follow Ups (RecoveryFollowUps) APIS ------------------- //

router.post('/getVoucherNo', async (req, res) => {
    const { Mnth } = req.body;
    const query = 'select MAX(vno) AS vno from RecoveryFollowUps where Mnth = @Mnth';
    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('Mnth', sql.VarChar(4), Mnth);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json({
            vtp: 'RF',
            location: 'EAM',
            vno: result.recordset[0].vno ? result.recordset[0].vno + 1 : 1,
            Mnth,
            voucherNo: 'RF/' + Mnth + '/EAM/' + (result.recordset[0].vno ? result.recordset[0].vno + 1 : 1)
        });
    });
});

router.post('/navigate', async (req, res) => {
    const { next, prev, first, last, voucher_no } = req.body;
    const pool = await getPool();
    const request = new sql.Request(pool);
    let query = '';

    if ((next && (prev || first || last)) || (prev && (first || last)) || (first && last) || (!next && !prev && !first && !last)) {
        return res.status(400).json({ message: "Invalid request. Use either 'next', 'prev', 'first', or 'last' exclusively, and provide 'voucher_no' if using 'next' or 'prev'." });
    }

    const baseQuery = `
        SELECT TOP 1 
            RecoveryFollowUps.*,
            c.Title AS ClientName
        FROM 
            RecoveryFollowUps
        LEFT JOIN
            Coa32 c ON RecoveryFollowUps.Client_ID = c.id
    `;

    if (next) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'next'." });
        query = `${baseQuery}
            WHERE (RecoveryFollowUps.vtp > (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) 
                OR (RecoveryFollowUps.vtp = (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Mnth > (SELECT Mnth FROM RecoveryFollowUps WHERE voucher_no = @voucher_no))
                OR (RecoveryFollowUps.vtp = (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Mnth = (SELECT Mnth FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Location > (SELECT Location FROM RecoveryFollowUps WHERE voucher_no = @voucher_no))
                OR (RecoveryFollowUps.vtp = (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Mnth = (SELECT Mnth FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Location = (SELECT Location FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.vno > (SELECT vno FROM RecoveryFollowUps WHERE voucher_no = @voucher_no)))
            ORDER BY RecoveryFollowUps.vtp, RecoveryFollowUps.Mnth, RecoveryFollowUps.Location, RecoveryFollowUps.vno`;
    } else if (prev) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'prev'." });
        query = `${baseQuery}
            WHERE (RecoveryFollowUps.vtp < (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) 
                OR (RecoveryFollowUps.vtp = (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Mnth < (SELECT Mnth FROM RecoveryFollowUps WHERE voucher_no = @voucher_no))
                OR (RecoveryFollowUps.vtp = (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Mnth = (SELECT Mnth FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Location < (SELECT Location FROM RecoveryFollowUps WHERE voucher_no = @voucher_no))
                OR (RecoveryFollowUps.vtp = (SELECT vtp FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Mnth = (SELECT Mnth FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.Location = (SELECT Location FROM RecoveryFollowUps WHERE voucher_no = @voucher_no) AND RecoveryFollowUps.vno < (SELECT vno FROM RecoveryFollowUps WHERE voucher_no = @voucher_no)))
            ORDER BY RecoveryFollowUps.vtp DESC, RecoveryFollowUps.Mnth DESC, RecoveryFollowUps.Location DESC, RecoveryFollowUps.vno DESC`;
    } else if (first) {
        query = `${baseQuery} ORDER BY RecoveryFollowUps.vtp, RecoveryFollowUps.Mnth, RecoveryFollowUps.Location, RecoveryFollowUps.vno`;
    } else if (last) {
        query = `${baseQuery} ORDER BY RecoveryFollowUps.vtp DESC, RecoveryFollowUps.Mnth DESC, RecoveryFollowUps.Location DESC, RecoveryFollowUps.vno DESC`;
    }

    if (voucher_no) {
        request.input('voucher_no', sql.VarChar, voucher_no);
    }

    request.query(query, async (err, result) => {
        if (err) return res.status(500).send(err);

        if (result.recordset.length > 0) {
            const order = result.recordset[0];
            const detailsRequest = new sql.Request(pool);
            detailsRequest.input('voucher_no', sql.VarChar, order.Voucher_No);

            const detailsQuery = `
                SELECT 
                    det.*
                FROM 
                    RecoveryFollowUps_Det det
                WHERE 
                    det.voucher_no = @voucher_no
            `;

            detailsRequest.query(detailsQuery, (detailsErr, detailsResult) => {
                if (detailsErr) return res.status(500).send(detailsErr);
                order["items"] = detailsResult.recordset;
                res.status(200).json(order);
            });
        } else {
            res.status(404).json({ message: 'No more records available in this direction.' });
        }
    });
});

router.get('/', async (req, res) => {
    const query = 'SELECT * FROM RecoveryFollowUps';

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/getbyvoucher', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const { voucherNo, ref } = req.body;

        if (!voucherNo) {
            return res.status(400).send('Voucher_No is required.');
        }

        let query = 'SELECT * FROM RecoveryFollowUps WHERE Voucher_No = @Voucher_No';
        if (ref) {
            query = 'SELECT * FROM RecoveryFollowUps WHERE RefVoucherNo = @Voucher_No';
        }

        request.input('Voucher_No', sql.VarChar(16), voucherNo);

        const recoveryFollowUps = await request.query(query);

        if (recoveryFollowUps.recordset.length === 0) {
            return res.status(200).send({});
        }
        const responseObject = recoveryFollowUps.recordset[0];
        const detailQuery = 'SELECT * FROM RecoveryFollowUps_det WHERE Voucher_No = @Voucher_No'
        const detailRequest = new sql.Request(transaction);
        detailRequest.input('Voucher_No', sql.VarChar(16), responseObject?.Voucher_No);
        const recoveryFollowUpsDetail = await detailRequest.query(detailQuery);

        await transaction.commit();
        responseObject['items'] = recoveryFollowUpsDetail.recordset;
        res.status(200).json(responseObject);
    } catch (err) {
        await transaction.rollback();

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else if (err.message === 'No items provided.') {
            res.status(400).send(err.message);
        } else {
            res.status(500).send(err.message);
        }
    }
});

router.post('/create', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        let columns = [];
        let values = [];

        recoveryFollowUpsColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columns.push(name);
                values.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const orderQuery = `INSERT INTO RecoveryFollowUps (${columns.join(', ')}) VALUES (${values.join(', ')})`;

        await request.query(orderQuery);

        const { items } = req.body;

        if (!items || items.length === 0) {
            throw new Error('No items provided.');
        }

        const itemColumnsArray = [];
        const itemValuesArray = [];
        const paramsArray = [];

        recoveryFollowUpsItemColumns.forEach(({ name }) => {
            if (!itemColumnsArray.includes(name)) {
                itemColumnsArray.push(name);
            }
        });

        items.forEach((item, index) => {
            const rowValues = [];
            itemColumnsArray.forEach((column) => {
                if (item[column] !== undefined) {
                    rowValues.push(`@${column}${index}`);
                    paramsArray.push({ name: `${column}${index}`, value: item[column] });
                } else {
                    rowValues.push('NULL');
                }
            });
            itemValuesArray.push(`(${rowValues.join(', ')})`);
        });

        const itemQuery = `
            INSERT INTO RecoveryFollowUps_Det (${itemColumnsArray.join(', ')}) 
            VALUES ${itemValuesArray.join(', ')};
        `;

        paramsArray.forEach(({ name, value }) => {
            request.input(name, value);
        });

        await request.query(itemQuery);

        await transaction.commit();

        res.status(201).json({
            message: 'Recovery Follow Ups and items successfully created.',
            vtp: req.body['vtp'],
            mnth: req.body['Mnth'],
            location: req.body['Location'],
            vno: req.body['vno']
        });
    } catch (err) {
        await transaction.rollback();

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else if (err.message === 'No items provided.') {
            res.status(400).send(err.message);
        } else {
            res.status(500).send(err.message);
        }
    }
});



router.put('/update', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        const voucherNo = req.query.voucherNo;

        await transaction.begin();
        const request = new sql.Request(transaction);

        let setClause = [];

        recoveryFollowUpsColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                setClause.push(`${name} = @${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        if (setClause.length === 0) {
            return res.status(400).send('No fields to update.');
        }

        const followUpsQuery = `UPDATE RecoveryFollowUps SET ${setClause.join(', ')} WHERE Voucher_No = @Voucher_No`;
        request.input('Voucher_No', sql.VarChar(50), voucherNo);

        const followUpsResult = await request.query(followUpsQuery);

        if (followUpsResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Recovery Follow Ups not found.');
        }

        const { items } = req.body;

        if (items && Array.isArray(items) && items.length > 0) {
            const existingItemsQuery = `
                SELECT DVoucher_No
                FROM RecoveryFollowUps_Det
                WHERE Voucher_No = @Voucher_No
            `;
            const existingItemsRequest = new sql.Request(transaction);
            existingItemsRequest.input('Voucher_No', sql.VarChar(50), voucherNo);
            const existingItemsResult = await existingItemsRequest.query(existingItemsQuery);
            const existingDVoucherNos = existingItemsResult.recordset.map(row => row.DVoucher_No);

            const itemsToUpdate = [];
            const itemsToInsert = [];
            const newDVoucherNos = new Set();

            items.forEach((item, i) => {
                const { vtp, Mnth, Location, vno, srno } = item;
                if (!vtp || !Mnth || !Location || !vno || !srno) {
                    return res.status(400).send(`Invalid DVoucher_No format in item ${i + 1}. Expected format: vtp/Mnth/Location/vno/srno.`);
                }

                const DVoucher_No = `${vtp}/${Mnth}/${Location}/${vno}/${srno}`;
                newDVoucherNos.add(DVoucher_No);

                if (existingDVoucherNos.includes(DVoucher_No)) {
                    itemsToUpdate.push({ ...item, DVoucher_No });
                } else {
                    itemsToInsert.push({ ...item, DVoucher_No });
                }
            });

            const itemsToDelete = existingDVoucherNos.filter(dvNo => !newDVoucherNos.has(dvNo));

            for (const item of itemsToUpdate) {
                const updateItemColumns = [];
                const updateParams = [];

                recoveryFollowUpsItemColumns.forEach(({ name, editable }) => {
                    if (item[name] !== undefined && editable) {
                        updateItemColumns.push(`${name} = @${name}`);
                        updateParams.push({ name, value: item[name] });
                    }
                });

                if (updateItemColumns.length > 0) {
                    const updateItemQuery = `
                        UPDATE RecoveryFollowUps_Det
                        SET ${updateItemColumns.join(', ')}
                        WHERE DVoucher_No = @DVoucher_No
                    `;

                    const updateRequest = new sql.Request(transaction);
                    updateRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                    updateParams.forEach(({ name, value }) => {
                        updateRequest.input(name, value);
                    });

                    await updateRequest.query(updateItemQuery);
                }
            }

            for (const DVoucher_No of itemsToDelete) {
                const deleteItemQuery = `
                    DELETE FROM RecoveryFollowUps_Det
                    WHERE DVoucher_No = @DVoucher_No
                `;

                const deleteRequest = new sql.Request(transaction);
                deleteRequest.input('DVoucher_No', sql.VarChar(50), DVoucher_No);

                await deleteRequest.query(deleteItemQuery);
            }

            for (const item of itemsToInsert) {
                const insertItemColumns = [];
                const insertItemValues = [];
                const insertParams = [];

                recoveryFollowUpsItemColumns.forEach(({ name }) => {
                    if (item[name] !== undefined) {
                        insertItemColumns.push(name);
                        insertItemValues.push(`@${name}`);
                        insertParams.push({ name, value: item[name] });
                    }
                });

                const insertItemQuery = `
                    INSERT INTO RecoveryFollowUps_Det (${insertItemColumns.join(', ')})
                    VALUES (${insertItemValues.join(', ')})
                `;

                const insertRequest = new sql.Request(transaction);
                insertParams.forEach(({ name, value }) => {
                    insertRequest.input(name, value);
                });

                await insertRequest.query(insertItemQuery);
            }
        }

        await transaction.commit();
        res.status(200).send('Recovery Follow Ups and items updated successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});



router.delete('/delete', async (req, res) => {
    const voucherNo = req.query.voucherNo;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const deleteItemsQuery = 'DELETE FROM RecoveryFollowUps_Det WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(16), voucherNo);
        const deleteItemsResult = await request.query(deleteItemsQuery);
        const deleteOrderQuery = 'DELETE FROM RecoveryFollowUps WHERE Voucher_No = @Voucher_No';
        const deleteOrderResult = await request.query(deleteOrderQuery);
        if (deleteItemsResult.rowsAffected[0] === 0 && deleteOrderResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Recovery Follow Ups not found or already deleted.');
        }
        await transaction.commit();
        res.status(200).send('Recovery Follow Ups and associated items deleted successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

module.exports = router;
