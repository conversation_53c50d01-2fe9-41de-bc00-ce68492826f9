import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  ModalCloseButton,
  Button,
  VStack,
  Text,
  Badge,
  HStack,
  Box,
  Divider
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const NotificationDetailsModal = ({ isOpen, onClose, notification }) => {
  if (!notification) return null;

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 4: return 'red.500';
      case 3: return 'orange.500';
      case 2: return 'blue.500';
      case 1: return 'gray.500';
      default: return 'gray.500';
    }
  };

  const getTypeColor = (type) => {
    switch (type?.toLowerCase()) {
      case 'time_update': return 'blue';
      case 'form_submission': return 'green';
      case 'system': return 'purple';
      default: return 'gray';
    }
  };

  const formatDateTime = (dateString) => {
    return dayjs(dateString).format('MMMM D, YYYY at h:mm A');
  };

  const parseContext = (contextString) => {
    try {
      return JSON.parse(contextString);
    } catch {
      return {};
    }
  };

  const context = parseContext(notification.Context || '{}');

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg" isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={3}>
            <Text fontSize="lg" fontWeight="bold">
              Notification Details
            </Text>
            <Badge colorScheme={getPriorityColor(notification.Priority)}>
              {notification.Priority || 'Normal'} Priority
            </Badge>
            <Badge colorScheme={getTypeColor(notification.Type)}>
              {notification.Type?.replace('_', ' ').toUpperCase() || 'NOTIFICATION'}
            </Badge>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* Title */}
            <Box>
              <Text fontSize="sm" color="gray.500" mb={1}>Title</Text>
              <Text fontSize="md" fontWeight="semibold">
                {notification.Title}
              </Text>
            </Box>

            <Divider />

            {/* Message */}
            <Box>
              <Text fontSize="sm" color="gray.500" mb={1}>Message</Text>
              <Text fontSize="md">
                {notification.Message}
              </Text>
            </Box>

            <Divider />

            {/* Timestamps */}
            <HStack spacing={6}>
              <Box>
                <Text fontSize="sm" color="gray.500" mb={1}>Created</Text>
                <Text fontSize="sm">
                  {formatDateTime(notification.CreatedAt)}
                </Text>
                <Text fontSize="xs" color="gray.400">
                  ({dayjs(notification.CreatedAt).fromNow()})
                </Text>
              </Box>

              {notification.ReadAt && (
                <Box>
                  <Text fontSize="sm" color="gray.500" mb={1}>Read</Text>
                  <Text fontSize="sm">
                    {formatDateTime(notification.ReadAt)}
                  </Text>
                  <Text fontSize="xs" color="gray.400">
                    ({dayjs(notification.ReadAt).fromNow()})
                  </Text>
                </Box>
              )}
            </HStack>

            {/* Context Details (for time updates) */}
            {notification.Type === 'TIME_UPDATE' && context && (
              <>
                <Divider />
                <Box>
                  <Text fontSize="sm" color="gray.500" mb={2}>Update Details</Text>
                  <VStack spacing={2} align="stretch">
                    {context.employeeName && (
                      <HStack>
                        <Text fontSize="sm" fontWeight="medium" minW="100px">Employee:</Text>
                        <Text fontSize="sm">{context.employeeName}</Text>
                      </HStack>
                    )}
                    {context.quotationNo && (
                      <HStack>
                        <Text fontSize="sm" fontWeight="medium" minW="100px">Quotation:</Text>
                        <Text fontSize="sm">{context.quotationNo}</Text>
                      </HStack>
                    )}
                    {context.clientName && (
                      <HStack>
                        <Text fontSize="sm" fontWeight="medium" minW="100px">Client:</Text>
                        <Text fontSize="sm">{context.clientName}</Text>
                      </HStack>
                    )}
                    {context.newTime && (
                      <HStack>
                        <Text fontSize="sm" fontWeight="medium" minW="100px">New Time:</Text>
                        <Text fontSize="sm">{dayjs(context.newTime).format('MMMM D, YYYY at h:mm A')}</Text>
                      </HStack>
                    )}
                    {context.reason && (
                      <Box>
                        <Text fontSize="sm" fontWeight="medium" mb={1}>Reason:</Text>
                        <Text fontSize="sm" bg="gray.50" p={2} borderRadius="md">
                          {context.reason}
                        </Text>
                      </Box>
                    )}
                  </VStack>
                </Box>
              </>
            )}

            {/* Status */}
            <Divider />
            <HStack spacing={4}>
              <Badge colorScheme={notification.IsRead ? 'green' : 'yellow'}>
                {notification.IsRead ? 'Read' : 'Unread'}
              </Badge>
              {notification.IsArchived && (
                <Badge colorScheme="gray">
                  Archived
                </Badge>
              )}
            </HStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default NotificationDetailsModal;
