const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const notificationService = require('../services/notificationService');

router.use(authorization);

/**
 * @swagger
 * /api/notifications:
 *   get:
 *     summary: Get notifications for the current user
 *     tags: [Notifications]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of notifications to retrieve
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of notifications to skip
 *       - in: query
 *         name: includeRead
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include read notifications
 *       - in: query
 *         name: includeArchived
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include archived notifications
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by notification type
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/', async (req, res) => {
    try {
        const userID = req.user.id;
        const options = {
            limit: parseInt(req.query.limit) || 50,
            offset: parseInt(req.query.offset) || 0,
            includeRead: req.query.includeRead !== 'false',
            includeArchived: req.query.includeArchived === 'true',
            typeFilter: req.query.type || null
        };

        const notifications = await notificationService.getUserNotifications(userID, options);

        res.status(200).json({
            message: 'Notifications retrieved successfully',
            data: notifications,
            pagination: {
                limit: options.limit,
                offset: options.offset,
                hasMore: notifications.length === options.limit
            }
        });
    } catch (error) {
        console.error('Error fetching notifications:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

/**
 * @swagger
 * /api/notifications/unread-count:
 *   get:
 *     summary: Get unread notification count for the current user
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Unread count retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/unread-count', async (req, res) => {
    try {
        const userID = req.user.id;
        const count = await notificationService.getUnreadCount(userID);

        res.status(200).json({
            message: 'Unread count retrieved successfully',
            data: { unreadCount: count }
        });
    } catch (error) {
        console.error('Error fetching unread count:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

/**
 * @swagger
 * /api/notifications/{id}/read:
 *   put:
 *     summary: Mark a notification as read
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification marked as read
 *       404:
 *         description: Notification not found
 *       500:
 *         description: Server error
 */
router.put('/:id/read', async (req, res) => {
    try {
        const userID = req.user.id;
        const notificationID = parseInt(req.params.id);

        if (!notificationID) {
            return res.status(400).json({ message: 'Invalid notification ID' });
        }

        await notificationService.markAsRead(userID, notificationID);

        res.status(200).json({
            message: 'Notification marked as read successfully'
        });
    } catch (error) {
        console.error('Error marking notification as read:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

/**
 * @swagger
 * /api/notifications/{id}/archive:
 *   put:
 *     summary: Mark a notification as archived
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification archived successfully
 *       404:
 *         description: Notification not found
 *       500:
 *         description: Server error
 */
router.put('/:id/archive', async (req, res) => {
    try {
        const userID = req.user.id;
        const notificationID = parseInt(req.params.id);

        if (!notificationID) {
            return res.status(400).json({ message: 'Invalid notification ID' });
        }

        await notificationService.markAsArchived(userID, notificationID);

        res.status(200).json({
            message: 'Notification archived successfully'
        });
    } catch (error) {
        console.error('Error archiving notification:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

/**
 * @swagger
 * /api/notifications/mark-all-read:
 *   put:
 *     summary: Mark all notifications as read for the current user
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: All notifications marked as read
 *       500:
 *         description: Server error
 */
router.put('/mark-all-read', async (req, res) => {
    try {
        const userID = req.user.id;
        const pool = await getPool();
        const request = new sql.Request(pool);

        const query = `
            UPDATE NotificationRecipients
            SET IsRead = 1, ReadAt = GETDATE()
            WHERE (RecipientUserID = @userID OR RecipientRoleID = (SELECT RoleID FROM users WHERE id = @userID))
            AND IsRead = 0`;

        request.input('userID', sql.NVarChar(32), userID);
        const result = await request.query(query);

        res.status(200).json({
            message: 'All notifications marked as read successfully',
            data: { updatedCount: result.rowsAffected[0] }
        });
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

/**
 * @swagger
 * /api/notifications/types:
 *   get:
 *     summary: Get all notification types
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Notification types retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/types', async (req, res) => {
    try {
        const pool = await getPool();
        const request = new sql.Request(pool);

        const query = `
            SELECT ID, TypeName, DisplayName, Description, IconName, ColorScheme
            FROM NotificationTypes
            WHERE IsActive = 1
            ORDER BY DisplayName`;

        const result = await request.query(query);

        res.status(200).json({
            message: 'Notification types retrieved successfully',
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching notification types:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

/**
 * @swagger
 * /api/notifications/settings:
 *   get:
 *     summary: Get notification settings for the current user
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Notification settings retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/settings', async (req, res) => {
    try {
        const userID = req.user.id;
        const pool = await getPool();
        const request = new sql.Request(pool);

        const query = `
            SELECT nt.ID, nt.TypeName, nt.DisplayName, nt.Description,
                   ISNULL(ns.IsEnabled, 1) as IsEnabled,
                   ISNULL(ns.EmailNotification, 0) as EmailNotification,
                   ISNULL(ns.PushNotification, 1) as PushNotification
            FROM NotificationTypes nt
            LEFT JOIN NotificationSettings ns ON nt.ID = ns.TypeID AND ns.UserID = @userID
            WHERE nt.IsActive = 1
            ORDER BY nt.DisplayName`;

        request.input('userID', sql.NVarChar(32), userID);
        const result = await request.query(query);

        res.status(200).json({
            message: 'Notification settings retrieved successfully',
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching notification settings:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

/**
 * @swagger
 * /api/notifications/settings:
 *   put:
 *     summary: Update notification settings for the current user
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               settings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     typeID:
 *                       type: integer
 *                     isEnabled:
 *                       type: boolean
 *                     emailNotification:
 *                       type: boolean
 *                     pushNotification:
 *                       type: boolean
 *     responses:
 *       200:
 *         description: Notification settings updated successfully
 *       500:
 *         description: Server error
 */
router.put('/settings', async (req, res) => {
    try {
        const userID = req.user.id;
        const { settings } = req.body;

        if (!Array.isArray(settings)) {
            return res.status(400).json({ message: 'Settings must be an array' });
        }

        const pool = await getPool();
        const transaction = new sql.Transaction(pool);

        await transaction.begin();

        for (const setting of settings) {
            const request = new sql.Request(transaction);

            const query = `
                IF EXISTS (SELECT 1 FROM NotificationSettings WHERE UserID = @userID AND TypeID = @typeID)
                BEGIN
                    UPDATE NotificationSettings
                    SET IsEnabled = @isEnabled, EmailNotification = @emailNotification,
                        PushNotification = @pushNotification, UpdatedAt = GETDATE()
                    WHERE UserID = @userID AND TypeID = @typeID
                END
                ELSE
                BEGIN
                    INSERT INTO NotificationSettings (UserID, TypeID, IsEnabled, EmailNotification, PushNotification)
                    VALUES (@userID, @typeID, @isEnabled, @emailNotification, @pushNotification)
                END`;

            request.input('userID', sql.NVarChar(32), userID);
            request.input('typeID', sql.Int, setting.typeID);
            request.input('isEnabled', sql.Bit, setting.isEnabled);
            request.input('emailNotification', sql.Bit, setting.emailNotification);
            request.input('pushNotification', sql.Bit, setting.pushNotification);

            await request.query(query);
        }

        await transaction.commit();

        res.status(200).json({
            message: 'Notification settings updated successfully'
        });
    } catch (error) {
        await transaction.rollback();
        console.error('Error updating notification settings:', error);
        res.status(500).json({ message: 'Internal server error', error: error.message });
    }
});

module.exports = router;
