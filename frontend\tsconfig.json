{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "target": "esnext", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@src/*": ["./src/*"], "@assets/*": ["./public/*"], "@components/*": ["./src/components/*"], "@utils/*": ["./src/app/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/app/provider/UserContext.js", "middleware.js"], "exclude": ["node_modules"]}