"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/date-arithmetic";
exports.ids = ["vendor-chunks/date-arithmetic"];
exports.modules = {

/***/ "(ssr)/./node_modules/date-arithmetic/index.js":
/*!***********************************************!*\
  !*** ./node_modules/date-arithmetic/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   century: () => (/* binding */ century),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   day: () => (/* binding */ day),\n/* harmony export */   decade: () => (/* binding */ decade),\n/* harmony export */   diff: () => (/* binding */ diff),\n/* harmony export */   endOf: () => (/* binding */ endOf),\n/* harmony export */   eq: () => (/* binding */ eq),\n/* harmony export */   gt: () => (/* binding */ gt),\n/* harmony export */   gte: () => (/* binding */ gte),\n/* harmony export */   hours: () => (/* binding */ hours),\n/* harmony export */   inRange: () => (/* binding */ inRange),\n/* harmony export */   lt: () => (/* binding */ lt),\n/* harmony export */   lte: () => (/* binding */ lte),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   milliseconds: () => (/* binding */ milliseconds),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   minutes: () => (/* binding */ minutes),\n/* harmony export */   month: () => (/* binding */ month),\n/* harmony export */   neq: () => (/* binding */ neq),\n/* harmony export */   seconds: () => (/* binding */ seconds),\n/* harmony export */   startOf: () => (/* binding */ startOf),\n/* harmony export */   subtract: () => (/* binding */ subtract),\n/* harmony export */   weekday: () => (/* binding */ weekday),\n/* harmony export */   year: () => (/* binding */ year)\n/* harmony export */ });\nvar MILI    = 'milliseconds'\n  , SECONDS = 'seconds'\n  , MINUTES = 'minutes'\n  , HOURS   = 'hours'\n  , DAY     = 'day'\n  , WEEK    = 'week'\n  , MONTH   = 'month'\n  , YEAR    = 'year'\n  , DECADE  = 'decade'\n  , CENTURY = 'century';\n\nvar multiplierMilli = {\n  'milliseconds': 1,\n  'seconds': 1000,\n  'minutes': 60 * 1000,\n  'hours': 60 * 60 * 1000,\n  'day': 24 * 60 * 60 * 1000,\n  'week': 7 * 24 * 60 * 60 * 1000 \n}\n\nvar multiplierMonth = {\n  'month': 1,\n  'year': 12,\n  'decade': 10 * 12,\n  'century': 100 * 12\n}\n\nfunction daysOf(year) {\n  return [31, daysInFeb(year), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\n}\n\nfunction daysInFeb(year) {\n  return (\n      year % 4 === 0 \n      && year % 100 !== 0\n    ) || year % 400 === 0\n      ? 29\n      : 28\n}\n\nfunction add(d, num, unit) {\n  d = new Date(d)\n\n  switch (unit){\n    case MILI:\n    case SECONDS:\n    case MINUTES:\n    case HOURS:\n    case DAY:\n    case WEEK:\n      return addMillis(d, num * multiplierMilli[unit])\n    case MONTH:\n    case YEAR:\n    case DECADE:\n    case CENTURY:\n      return addMonths(d, num * multiplierMonth[unit])\n  }\n\n  throw new TypeError('Invalid units: \"' + unit + '\"')\n}\n\nfunction addMillis(d, num) {\n  var nextDate = new Date(+(d) + num)\n\n  return solveDST(d, nextDate)\n}\n\nfunction addMonths(d, num) {\n  var year = d.getFullYear()\n    , month = d.getMonth()\n    , day = d.getDate()\n    , totalMonths = year * 12 + month + num\n    , nextYear = Math.trunc(totalMonths / 12)\n    , nextMonth = totalMonths % 12\n    , nextDay = Math.min(day, daysOf(nextYear)[nextMonth])\n\n  var nextDate = new Date(d)\n  nextDate.setFullYear(nextYear)\n\n  // To avoid a bug when sets the Feb month\n  // with a date > 28 or date > 29 (leap year)\n  nextDate.setDate(1)\n\n  nextDate.setMonth(nextMonth)\n  nextDate.setDate(nextDay)\n\n  return nextDate\n}\n\nfunction solveDST(currentDate, nextDate) {\n  var currentOffset = currentDate.getTimezoneOffset()\n    , nextOffset = nextDate.getTimezoneOffset()\n\n  // if is DST, add the difference in minutes\n  // else the difference is zero\n  var diffMinutes = (nextOffset - currentOffset)\n\n  return new Date(+(nextDate) + diffMinutes * multiplierMilli['minutes'])\n}\n\nfunction subtract(d, num, unit) {\n  return add(d, -num, unit)\n}\n\nfunction startOf(d, unit, firstOfWeek) {\n  d = new Date(d)\n\n  switch (unit) {\n    case CENTURY:\n    case DECADE:\n    case YEAR:\n        d = month(d, 0);\n    case MONTH:\n        d = date(d, 1);\n    case WEEK:\n    case DAY:\n        d = hours(d, 0);\n    case HOURS:\n        d = minutes(d, 0);\n    case MINUTES:\n        d = seconds(d, 0);\n    case SECONDS:\n        d = milliseconds(d, 0);\n  }\n\n  if (unit === DECADE)\n    d = subtract(d, year(d) % 10, 'year')\n\n  if (unit === CENTURY)\n    d = subtract(d, year(d) % 100, 'year')\n\n  if (unit === WEEK)\n    d = weekday(d, 0, firstOfWeek);\n\n  return d\n}\n\nfunction endOf(d, unit, firstOfWeek){\n  d = new Date(d)\n  d = startOf(d, unit, firstOfWeek)\n  switch (unit) {\n    case CENTURY:\n    case DECADE:\n    case YEAR:\n    case MONTH:\n    case WEEK:\n      d = add(d, 1, unit)\n      d = subtract(d, 1, DAY)\n      d.setHours(23, 59, 59, 999)\n      break;\n    case DAY:\n      d.setHours(23, 59, 59, 999)\n      break;\n    case HOURS:\n    case MINUTES:\n    case SECONDS:\n      d = add(d, 1, unit)\n      d = subtract(d, 1, MILI)\n  }\n  return d\n}\n\nvar eq =  createComparer(function(a, b){ return a === b })\nvar neq = createComparer(function(a, b){ return a !== b })\nvar gt =  createComparer(function(a, b){ return a > b })\nvar gte = createComparer(function(a, b){ return a >= b })\nvar lt =  createComparer(function(a, b){ return a < b })\nvar lte = createComparer(function(a, b){ return a <= b })\n\nfunction min(){\n  return new Date(Math.min.apply(Math, arguments))\n}\n\nfunction max(){\n  return new Date(Math.max.apply(Math, arguments))\n}\n\nfunction inRange(day, min, max, unit){\n  unit = unit || 'day'\n\n  return (!min || gte(day, min, unit))\n      && (!max || lte(day, max, unit))\n}\n\nvar milliseconds = createAccessor('Milliseconds')\nvar seconds =      createAccessor('Seconds')\nvar minutes =      createAccessor('Minutes')\nvar hours =        createAccessor('Hours')\nvar day =          createAccessor('Day')\nvar date =         createAccessor('Date')\nvar month =        createAccessor('Month')\nvar year =         createAccessor('FullYear')\n\nfunction decade(d, val) {\n  return val === undefined\n    ? year(startOf(d, DECADE))\n    : add(d, val + 10, YEAR);\n}\n\nfunction century(d, val) {\n  return val === undefined\n    ? year(startOf(d, CENTURY))\n    : add(d, val + 100, YEAR);\n}\n\nfunction weekday(d, val, firstDay) {\n    var w = (day(d) + 7 - (firstDay || 0) ) % 7;\n\n    return val === undefined\n      ? w\n      : add(d, val - w, DAY);\n}\n\nfunction diff(date1, date2, unit, asFloat) {\n  var dividend, divisor, result;\n\n  switch (unit) {\n    case MILI:\n    case SECONDS:\n    case MINUTES:\n    case HOURS:\n    case DAY:\n    case WEEK:\n      dividend = date2.getTime() - date1.getTime(); break;\n    case MONTH:\n    case YEAR:\n    case DECADE:\n    case CENTURY:\n      dividend = (year(date2) - year(date1)) * 12 + month(date2) - month(date1); break;\n    default:\n      throw new TypeError('Invalid units: \"' + unit + '\"');\n  }\n\n  switch (unit) {\n    case MILI:\n        divisor = 1; break;\n    case SECONDS:\n        divisor = 1000; break;\n    case MINUTES:\n        divisor = 1000 * 60; break;\n    case HOURS:\n        divisor = 1000 * 60 * 60; break;\n    case DAY:\n        divisor = 1000 * 60 * 60 * 24; break;\n    case WEEK:\n        divisor = 1000 * 60 * 60 * 24 * 7; break;\n    case MONTH:\n        divisor = 1; break;\n    case YEAR:\n        divisor = 12; break;\n    case DECADE:\n        divisor = 120; break;\n    case CENTURY:\n        divisor = 1200; break;\n    default:\n      throw new TypeError('Invalid units: \"' + unit + '\"');\n  }\n\n  result = dividend / divisor;\n\n  return asFloat ? result : Math.round(result);\n}\n\nfunction createAccessor(method){\n  var hourLength = (function(method) {  \n    switch(method) {\n      case 'Milliseconds':\n        return 3600000;\n      case 'Seconds':\n        return 3600;\n      case 'Minutes':\n        return 60;\n      case 'Hours':\n        return 1;\n      default:\n        return null;\n    }\n  })(method);\n  \n  return function(d, val){\n    if (val === undefined)\n      return d['get' + method]()\n\n    var dateOut = new Date(d)\n    dateOut['set' + method](val)\n    \n    if(hourLength && dateOut['get'+method]() != val && (method === 'Hours' || val >=hourLength && (dateOut.getHours()-d.getHours()<Math.floor(val/hourLength))) ){\n      //Skip DST hour, if it occurs\n      dateOut['set'+method](val+hourLength);\n    }\n    \n    return dateOut\n  }\n}\n\nfunction createComparer(operator) {\n  return function (a, b, unit) {\n    return operator(+startOf(a, unit), +startOf(b, unit))\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-arithmetic/index.js\n");

/***/ })

};
;