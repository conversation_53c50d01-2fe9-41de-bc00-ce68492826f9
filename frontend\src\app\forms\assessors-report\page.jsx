"use client";
import React, { useState, useEffect } from 'react';
import "@src/app/dashboard/dashboard.css";
import { Box } from '@chakra-ui/react';
import axiosInstance from '@src/app/axios';
import { useRouter } from 'next/navigation';
import Loader from '@src/components/Loader/Loader';
import ReportTable from '@src/components/Custom/ReportTable';

const Dashboard = () => {
    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    const router = useRouter();

    const fetchTasks = async () => {
        try {
            const { data } = await axiosInstance.get("purpose/assessor/all");
            if (Array.isArray(data.data) && data.data.length > 0) {
                setTasks(data.data);
            } else {
                setTasks([]);
            }
        } catch (error) {
            console.error("Error: ", error);
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchTasks();
    }, []);

    const getStatusColor = (status) => {
        switch (status) {
            case 'rejected': return 'red';
            case 'pending': return 'yellow';
            case 'in-progress': return 'blue';
            case 'completed': return 'green';
            default: return 'gray';
        }
    };

    const columns = [
        { header: '#', field: 'ID' },
        { header: 'Assigned To', field: 'assessorTitle' },
        { header: 'Contact', field: 'contactPerson' },
        { header: 'Assessment Date', field: 'assessmentTime', type: 'date' },
        { header: 'Status', field: 'status', type: 'badge' }
    ];

    const getRowCursor = (task) => task.status === 'completed' ? 'pointer' : 'auto';

    const handleRowClick = (task) => {
        if (task.status === 'completed') {
            router.push(`/forms/assessors-report/follow-up?purpose_no=${task.ID}`);
        }
    };

    return (
        <>
            {loading ? (
                <Loader />
            ) : (
                <>
                    <div className="wrapper">
                        <div>
                            <div>
                                <div className="page-inner">
                                    <div className="row">
                                        <div className="bgWhite">
                                            <h1
                                                style={{
                                                    margin: "0",
                                                    textAlign: "center",
                                                    color: "#2B6CB0",
                                                    fontSize: "24px",
                                                    fontWeight: "bold",
                                                    padding: "10px",
                                                }}
                                            >
                                                Assessment Reports
                                            </h1>
                                        </div>
                                    </div>
                                    <div className="row">
                                        <ReportTable
                                            data={tasks}
                                            columns={columns}
                                            onRowClick={handleRowClick}
                                            getRowCursor={getRowCursor}
                                            getBadgeColor={getStatusColor}
                                            dateField="assessmentTime"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </>
    );
}

export default Dashboard;