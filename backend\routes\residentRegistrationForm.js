const express = require('express');
const multer = require('multer');
const upload = multer();
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { residentRegistrationFormColumns } = require('./utils/constant')

router.use(authorization);

// ------------------- Resident Registration Form (RegistrationForm) APIS ------------------- //

router.post('/getVoucherNo', async (req, res) => {
    const { Mnth, vtp } = req.body;
    const query = 'select MAX(vno) AS vno from RegistrationForm where Mnth = @Mnth AND vtp = @vtp';

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('Mnth', sql.Var<PERSON>har(4), Mnth);
    request.input('vtp', sql.<PERSON>ar<PERSON><PERSON>(4), vtp);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json({
            vtp: vtp,
            location: 'EAM',
            vno: result.recordset[0].vno ? result.recordset[0].vno + 1 : 1,
            Mnth,
            voucherNo: vtp + '/' + Mnth + '/EAM/' + (result.recordset[0].vno ? result.recordset[0].vno + 1 : 1)
        });
    });
});

router.post('/navigate', async (req, res) => {
    const { next, prev, first, last, voucher_no } = req.body;
    const pool = await getPool();
    const request = new sql.Request(pool);

    let query = '';
    const baseQuery = `
        SELECT
        TOP 1
            RegistrationForm.*,
            g.Title AS GodownName,
            c.Title AS ClassName,
            eg.Title AS EducationGroupName,
            CashDetails.Title AS Cash_Title,
            s.Title AS SessionName
        FROM 
            RegistrationForm
            LEFT JOIN Godown g ON RegistrationForm.Location = g.Id
            LEFT JOIN Classes c ON RegistrationForm.Class_ID = c.ID
            LEFT JOIN EducationGroups eg ON RegistrationForm.EdGroup_ID = eg.ID
            LEFT JOIN Sessions s ON RegistrationForm.Session = s.ID
            LEFT JOIN 
                (SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CA') AS CashDetails ON CashDetails.id = RegistrationForm.Cash_id
        
    `;

    if ((next && (prev || first || last)) || (prev && (first || last)) || (first && last) || (!next && !prev && !first && !last)) {
        return res.status(400).json({ message: "Invalid request. Use either 'next', 'prev', 'first', or 'last' exclusively, and provide 'voucher_no' if using 'next' or 'prev'." });
    }

    if (next) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'next'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = parts[2];
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                RegistrationForm.vtp > @vtp 
                OR (RegistrationForm.vtp = @vtp AND RegistrationForm.Mnth > @Mnth)
                OR (RegistrationForm.vtp = @vtp AND RegistrationForm.Mnth = @Mnth AND RegistrationForm.Location > @Location)
                OR (RegistrationForm.vtp = @vtp AND RegistrationForm.Mnth = @Mnth AND RegistrationForm.Location = @Location AND RegistrationForm.vno > @vno)
            ) AND RegistrationForm.vtp = @vtp
            ORDER BY RegistrationForm.vtp, RegistrationForm.Mnth, RegistrationForm.Location, RegistrationForm.vno;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (prev) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'prev'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = parts[2];
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                RegistrationForm.vtp < @vtp 
                OR (RegistrationForm.vtp = @vtp AND RegistrationForm.Mnth < @Mnth)
                OR (RegistrationForm.vtp = @vtp AND RegistrationForm.Mnth = @Mnth AND RegistrationForm.Location < @Location)
                OR (RegistrationForm.vtp = @vtp AND RegistrationForm.Mnth = @Mnth AND RegistrationForm.Location = @Location AND RegistrationForm.vno < @vno)
            ) AND RegistrationForm.vtp = @vtp
            ORDER BY RegistrationForm.vtp DESC, RegistrationForm.Mnth DESC, RegistrationForm.Location DESC, RegistrationForm.vno DESC;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (first) {
        query = `${baseQuery} ORDER BY RegistrationForm.VTP, RegistrationForm.Mnth, RegistrationForm.Location, RegistrationForm.vno;`;
    } else if (last) {
        query = `${baseQuery} ORDER BY RegistrationForm.VTP DESC, RegistrationForm.Mnth DESC, RegistrationForm.Location DESC, RegistrationForm.vno DESC;`;
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }

        if (result.recordset.length > 0) {
            res.status(200).json(result.recordset[0]);
        } else {
            res.status(404).json({ message: 'No more records available in this direction.' });
        }
    });
});

router.get('/', async (req, res) => {
    const query = 'SELECT * FROM RegistrationForm';

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/create', async (req, res) => {
    const pool = await getPool();
    const request = new sql.Request(pool);

    try {
        let columns = [];
        let values = [];

        residentRegistrationFormColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columns.push(name);
                values.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const registraionFormQuery = `INSERT INTO RegistrationForm (${columns.join(', ')}) VALUES (${values.join(', ')})`;

        await request.query(registraionFormQuery);

        res.status(201).json({
            message: 'Registraion Form successfully created.',
            vtp: req.body['vtp'],
            mnth: req.body['Mnth'],
            location: req.body['Location'],
            vno: req.body['vno']
        });
    } catch (err) {

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else {
            res.status(500).send(err.message);
        }
    }
});

router.put('/update', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        const voucherNo = req.query.voucherNo;

        if (!voucherNo) {
            return res.status(400).send('Voucher_No is required.');
        }

        await transaction.begin();
        const request = new sql.Request(transaction);

        let setClause = [];
        residentRegistrationFormColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                setClause.push(`${name} = @${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        if (setClause.length === 0) {
            return res.status(400).send('No fields to update.');
        }

        const goodNoteQuery = `UPDATE RegistrationForm SET ${setClause.join(', ')} WHERE Voucher_No = @Voucher_No`;
        request.input('Voucher_No', sql.VarChar(50), voucherNo);

        const goodNoteResult = await request.query(goodNoteQuery);

        if (goodNoteResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Registration form not found.');
        }

        await transaction.commit();
        res.status(200).send('Registration form updated successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.delete('/delete', async (req, res) => {
    const voucherNo = req.query.voucherNo;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const deleteFormQuery = 'DELETE FROM RegistrationForm WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(16), voucherNo);
        const deleteFormResult = await request.query(deleteFormQuery);
        if (deleteFormResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Registration form not found or already deleted.');
        }
        await transaction.commit();
        res.status(200).send('Registration form deleted successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

// ------------------- Image Handling APIs ------------------- //

router.post('/image', upload.single('image'), async (req, res) => {
    const { vtp, AorB, Mnth, Location, vno } = req.body;
    const image = req.file;

    if (!vtp || !Mnth || !Location || !vno || !image) {
        return res.status(400).send('Required fields are missing');
    }

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        const imageBuffer = image.buffer;

        const query = `
            INSERT INTO RegistrationForm_pic 
            (dated, vtp, AorB, Mnth, Location, vno, Pic, PicSize) 
            VALUES 
            (GETDATE(), @vtp, @AorB, @Mnth, @Location, @vno, @Pic, @PicSize)
        `;

        request.input('vtp', sql.VarChar(20), vtp);
        request.input('AorB', sql.TinyInt, AorB || 0);
        request.input('Mnth', sql.VarChar(8), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);
        request.input('Pic', sql.VarBinary(sql.MAX), imageBuffer);
        request.input('PicSize', sql.Int, imageBuffer.length);

        await request.query(query);
        await transaction.commit();
        res.status(201).json({ message: 'Image uploaded successfully' });
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('Error uploading image: ' + err.message);
    }
});

router.get('/image', async (req, res) => {
    const { voucherNo } = req.query;
    const pool = await getPool();
    const request = new sql.Request(pool);

    try {
        request.input('Voucher_No', sql.VarChar(50), voucherNo);
        const query = 'SELECT Pic FROM RegistrationForm_pic WHERE Voucher_No = @Voucher_No';

        const result = await request.query(query);

        if (result.recordset.length === 0) {
            return res.status(404).send('Image not found');
        }

        const imageBuffer = result.recordset[0].Pic;
        res.writeHead(200, {
            'Content-Type': 'image/jpeg',
            'Content-Length': imageBuffer.length
        });
        res.end(imageBuffer);
    } catch (err) {
        res.status(500).send('Error retrieving image: ' + err.message);
    }
});

router.put('/image', upload.single('image'), async (req, res) => {
    const { vtp, AorB, Mnth, Location, vno, voucherNo } = req.body;
    const image = req.file;

    if (!voucherNo) {
        return res.status(400).send('Voucher number is required');
    }

    if (!image || !vtp || !Mnth || !Location || !vno) {
        return res.status(400).send('Image and all required fields are required');
    }

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        const checkQuery = 'SELECT 1 FROM RegistrationForm_pic WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(50), voucherNo);

        const existingRecord = await request.query(checkQuery);
        if (existingRecord.recordset.length > 0) {
            const deleteQuery = 'DELETE FROM RegistrationForm_pic WHERE Voucher_No = @Voucher_No';
            await request.query(deleteQuery);
        }


        const imageBuffer = image.buffer;
        const insertQuery = `
            INSERT INTO RegistrationForm_pic 
            (dated, vtp, AorB, Mnth, Location, vno, Pic, PicSize) 
            VALUES 
            (GETDATE(), @vtp, @AorB, @Mnth, @Location, @vno, @Pic, @PicSize)
        `;

        request.input('vtp', sql.VarChar(20), vtp);
        request.input('AorB', sql.TinyInt, AorB || 0);
        request.input('Mnth', sql.VarChar(8), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);
        request.input('Pic', sql.VarBinary(sql.MAX), imageBuffer);
        request.input('PicSize', sql.Int, imageBuffer.length);

        await request.query(insertQuery);
        await transaction.commit();

        res.status(200).json({
            message: 'Image updated successfully',
            vtp,
            Mnth,
            Location,
            vno,
            AorB: AorB || 0
        });
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('Error updating image: ' + err.message);
    }
});

router.delete('/image', async (req, res) => {
    const { voucherNo } = req.query;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        request.input('Voucher_No', sql.VarChar(50), voucherNo);
        const query = 'DELETE FROM RegistrationForm_pic WHERE Voucher_No = @Voucher_No';

        const result = await request.query(query);

        if (result.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Image not found');
        }

        await transaction.commit();
        res.status(200).json({ message: 'Image deleted successfully' });
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('Error deleting image: ' + err.message);
    }
});

module.exports = router;
