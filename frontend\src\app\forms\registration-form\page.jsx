"use client";
import React, { useEffect, useState } from "react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import TableComboBox from "@components/Custom/TableComboBox/TableComboBox";
import AanzaDataTable from "@components/Custom/AanzaDataTable/AanzaDataTable";
import ConfirmDialog from "@components/Custom/ConfirmDialog/ConfirmDialog";
import axiosInstance from "../../axios";
import Toolbar from "@components/Toolbar/Toolbar";
import { Button, useToast } from "@chakra-ui/react";
import { Box, FormControl, FormLabel, Input, Textarea, Select, Checkbox } from "@chakra-ui/react";
import {
  formatDate,
  validateObjectFields,
} from "@utils/functions";
import {
  RegistrationFormSectionFormFields,
  createRegistrationFormInitialFormData,
  RegistrationFormRequiredFields,
} from "@utils/constant";
import Loader from "@components/Loader/Loader";
import ImageUploader from "@components/Custom/ImageUploader/ImageUploader";

const RegistrationForm = () => {
  const toast = useToast();
  const [clients, setClients] = useState([]);
  const [salesMan, setSalesMan] = useState([]);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [cash, setCash] = useState([]);
  const [items, setItems] = useState([]);
  const [isNavigation, setIsNavigation] = useState(false);
  const [formData, setFormData] = useState(createRegistrationFormInitialFormData());
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setDialogOpen] = useState(false);

  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const handleImageChange = (file, preview) => {
    setImageFile(file);
    setImagePreview(preview);
  };

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const getData = async (url) => {
    try {
      const response = await axiosInstance.get(url);
      return response.data;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const getVoucherNo = async (date) => {
    if (date) {
      setLoading(true);
      const year = new Date(date).getFullYear();
      try {
        const response = await axiosInstance.post(
          "residentRegistrationForm/getVoucherNo",
          {
            Mnth: year.toString(),
            vtp: "RGF",
          }
        );
        const { location, vno, vtp, Mnth, voucherNo } = response.data || {};
        if ((location, vno, vtp, Mnth, voucherNo)) {
          setFormData((prevFormData) => ({
            ...prevFormData,
            Location: location,
            vno,
            vtp,
            Mnth,
            voucherNo,
            mnth: Mnth,
          }));
          setLoading(false);
          toast({
            title: "Voucher No. Generated Successfully !",
            status: "success",
            variant: "left-accent",
            position: "top-right",
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("Error fetching voucher number:", error);
        setLoading(false);
      }
    } else {
      toast({
        title: "Please Select a Date First.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const loadInitialData = async () => {
    const cashData = await getData("getRecords/cashAccounts");
    setCash(cashData);
  };

  // Toolbar funtions starts here

  const handleSave = async () => {
    if (!imageFile) {
      toast({
        title: "Please upload an image file",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      return;
    }

    const formDataToSend = new FormData();
    formDataToSend.append('image', imageFile);
    formDataToSend.append('AorB', 0);

    const data = {
      ...formData,
      dated: formData.date ? formatDate(formData.date) : null,
      CreationDate: new Date().toISOString(),
      ComputerName: window.location.hostname,
    };

    const keys = ['vtp', 'Mnth', 'Location', 'vno'];
    keys.forEach(key => {
      formDataToSend.append(key, data[key]);
    });

    const isValidateObjectFields = validateObjectFields(
      data,
      RegistrationFormRequiredFields
    );

    if (isValidateObjectFields.error) {
      toast({
        title: isValidateObjectFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      return;
    }

    setLoading(true);

    try {
      if (isEdit) {
        const voucherNo = formData.voucherNo;
        formDataToSend.append('voucherNo', voucherNo);
        await Promise.all([
          axiosInstance.put(`residentRegistrationForm/update?voucherNo=${voucherNo}`, data),
          axiosInstance.put(`residentRegistrationForm/image`, formDataToSend, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          })
        ]);
        editForm();
        toast({
          title: "Record modified successfully",
          status: "success",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
      } else {
        await Promise.all([
          axiosInstance.post("residentRegistrationForm/create", data),
          axiosInstance.post("residentRegistrationForm/image", formDataToSend, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          })
        ]);

        clearForm();
        toast({
          title: "Record saved successfully",
          status: "success",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
      }
    } catch (error) {
      console.error("Error saving/updating record:", error);
      toast({
        title: error.response?.data?.message || "Error processing your request",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    const voucherNo = formData.voucherNo;
    if (!voucherNo) return;
    setLoading(true);
    try {
      await axiosInstance.delete(`residentRegistrationForm/delete?voucherNo=${voucherNo}`);
      await axiosInstance.delete(`residentRegistrationForm/image?voucherNo=${voucherNo}`);
      setLoading(false);
      clearForm();
      toast({
        title: formData.voucherNo + " voucher deleted successfully",
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: error.response.data || "Client Order Error",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      console.error("Error navigating to voucher form:", error);
      setLoading(false);
    }
  };

  const clearForm = () => {
    setIsNavigation(false);
    setIsDisabled(false);
    setIsEdit(false);
    setImageFile(null);
    setImagePreview(null);
    setFormData(createRegistrationFormInitialFormData);
    toast({
      title: `Form Cleared`,
      status: "success",
      variant: "left-accent",
      position: "top-right",
      isClosable: true,
    });
  };

  const editForm = () => {
    setIsDisabled((p) => !p);
    setIsEdit((p) => !p);
  };

  const navigateVoucherForm = async (navigate, voucherNo) => {
    setLoading(true);
    setIsNavigation(true);
    setIsDisabled(true);
    try {
      const response = await axiosInstance.post("residentRegistrationForm/navigate", {
        [navigate]: true,
        voucher_no: voucherNo,
      });
      const resData = response.data;

      axiosInstance.get(`residentRegistrationForm/image?voucherNo=${resData?.Voucher_No}`, {
        responseType: 'blob'
      })
        .then((response) => {
          const imageUrl = URL.createObjectURL(response.data);
          setImagePreview(imageUrl);
          setImageFile(response.data);
        })
        .catch(error => {
          console.error('Error fetching image:', error);
          setImagePreview(null);
          setImageFile(null);
        });

      const form = {
        ...resData,
        date: resData?.dated
          ? formatDate(new Date(resData?.dated), true)
          : "",
        DateOfBirth: resData?.DateOfBirth
          ? formatDate(new Date(resData?.DateOfBirth), true)
          : "",
        voucherNo: resData?.Voucher_No,
      };

      setFormData(form);
      setLoading(false);

      toast({
        title: `${navigate.toUpperCase()} Record Fetched !`,
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      console.error(`Error fetching ${navigate} record:`, error);
      setLoading(false);
      toast({
        title: `${navigate} Record Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  // Toolbar funtions ends here

  useEffect(() => {
    loadInitialData();
  }, []);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div>
              <div>
                <div className="page-inner">
                  <div className="row">
                    <div className="bgWhite">
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "24px",
                          fontWeight: "bold",
                          padding: "10px",
                        }}
                      >
                        Registration Form
                      </h1>
                    </div>
                  </div>
                  <div
                    className="row"
                    style={{ gap: "10px", paddingTop: "8px" }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(33% - 5px) !important",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <form>
                        {RegistrationFormSectionFormFields[0].map((field) => (
                          <FormControl
                            key={field.id}
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                md: "row",
                              },
                              marginTop: "10px",
                            }}
                            isRequired={field.isRequired}
                          >
                            <FormLabel
                              htmlFor={field.id}
                              sx={{
                                marginBottom: "0",
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  md: "20%",
                                  lg: "35%",
                                },
                              }}
                            >
                              {field.label}
                            </FormLabel>
                            {field.type === "date" ? (
                              <Input
                                id={field.id}
                                name={field.name}
                                type={field.type}
                                value={formData[field.value]}
                                onChange={handleInputChange}
                                placeholder={field.placeholder}
                                _placeholder={{ color: "gray.500" }}
                                readOnly={field.isReadOnly}
                                // min={field.minDate}
                                // max={field.maxDate}
                                disabled={isEdit || isDisabled}
                                sx={{
                                  marginLeft: { base: "0", sm: "0", lg: "4px" },
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    md: "80%",
                                  },
                                }}
                              />
                            ) : (
                              <Input
                                id={field.id}
                                name={field.name}
                                type={field.type}
                                value={formData[field.value]}
                                onChange={handleInputChange}
                                placeholder={field.placeholder}
                                _placeholder={{ color: "gray.500" }}
                                readOnly={field.isReadOnly}
                                disabled={
                                  field.name === "voucherNo"
                                    ? isEdit || isDisabled
                                    : isDisabled
                                }
                                sx={{
                                  marginLeft: { base: "0", sm: "0", lg: "4px" },
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    md: "80%",
                                  },
                                }}
                              />
                            )}
                          </FormControl>
                        ))}

                        <FormControl>
                          <Button
                            style={{ width: "100%", marginTop: "5px" }}
                            colorScheme="blue"
                            onClick={() => getVoucherNo(formData.date)}
                            isDisabled={isEdit || isDisabled}
                          >
                            Generate Voucher No
                          </Button>
                        </FormControl>
                      </form>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(37% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      {RegistrationFormSectionFormFields[1].map(
                        (control, index) => (
                          <FormControl
                            key={index}
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                              marginTop: "10px",
                              flexWrap: "nowrap",
                            }}
                            isRequired={control.isRequired}
                          >
                            <FormLabel
                              htmlFor={control.fields[0].name}
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "20%" },
                              }}
                            >
                              {control.label}
                            </FormLabel>
                            <Box
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "80%" },
                                display: "flex",
                                gap: control.fields.length > 1 ? "10px" : "0",
                              }}
                            >
                              {control.fields.map((field, fieldIndex) =>
                                field.component === "ComboBox" ? (
                                  <ComboBox
                                    key={fieldIndex}
                                    target={true}
                                    onChange={handleInputChange}
                                    name={field.name}
                                    inputWidth={field.inputWidths}
                                    buttonWidth={field.buttonWidth}
                                    styleButton={{ padding: "3px !important" }}
                                    tableData={
                                      field.tableData == "cash"
                                        ? cash
                                        : []
                                    }
                                    tableHeaders={field.tableHeaders}
                                    nameFields={field.nameFields}
                                    placeholders={field.placeholders}
                                    keys={field.keys}
                                    form={formData}
                                    isDisabled={isDisabled}
                                  />
                                ) : field.component === "Select" ? (
                                  <Select
                                    key={fieldIndex}
                                    onChange={handleInputChange}
                                    name={field.name}
                                    value={formData[field.value]}
                                    placeholder={field.placeholder}
                                    style={{ width: field.inputWidth }}
                                    disabled={isDisabled}
                                  >
                                    <option value="">Please Select</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                                  </Select>
                                ) : (
                                  <Input
                                    key={fieldIndex}
                                    onChange={handleInputChange}
                                    name={field.name}
                                    placeholder={field.placeholder}
                                    value={formData[field.value]}
                                    _placeholder={field._placeholder}
                                    type={field.type}
                                    style={{ width: field.inputWidth }}
                                    disabled={isDisabled}
                                  />
                                )
                              )}
                            </Box>
                          </FormControl>
                        )
                      )}
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(30% - 10px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                          height: "100%",
                        }}
                        isRequired="true"
                      >
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "100%" },
                            display: "flex",
                            height: "100%",
                          }}
                        >
                          <ImageUploader
                            onChange={handleImageChange}
                            disabled={isDisabled}
                            imagePreview={imagePreview}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                  </div>
                  <div className="row">
                    <div
                      className=" ClientDIVVV bdWhite mt-2 pt-1 pb-1"
                      style={{ padding: "0px" }}
                    >
                      <Box
                        sx={{
                          padding: "15px",
                          width: {
                            base: "100% !important",
                            sm: "100%",
                            lg: "100%",
                          },
                          display: "grid",
                          gridTemplateColumns: "auto auto",
                          gap: "0px 20px",
                        }}
                        className=" bgWhite col-sm-12"
                      >
                        {RegistrationFormSectionFormFields[2].map(
                          (control, index) => (
                            <FormControl
                              key={index}
                              sx={{
                                display: "flex",
                                alignItems: "flex-start",
                                flexDirection: {
                                  base: "column",
                                  sm: "column",
                                  lg: "row",
                                },
                                marginTop: "10px",
                                flexWrap: "nowrap",
                              }}
                              isRequired={control.isRequired}
                            >
                              <FormLabel
                                htmlFor={control.fields[0].name}
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "30%",
                                  },
                                }}
                              >
                                {control.label}
                              </FormLabel>
                              <Box
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "70%",
                                  },
                                  display: "flex",
                                  gap: control.fields.length > 1 ? "10px" : "0",
                                }}
                              >
                                {control.fields.map((field, fieldIndex) =>
                                  field.component === "ComboBox" ? (
                                    <ComboBox
                                      key={fieldIndex}
                                      target={true}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      inputWidth={field.inputWidths}
                                      buttonWidth={field.buttonWidth}
                                      styleButton={{
                                        padding: "3px !important",
                                      }}
                                      tableData={
                                        field.tableData == "cash"
                                          ? cash
                                          : []
                                      }
                                      tableHeaders={field.tableHeaders}
                                      nameFields={field.nameFields}
                                      placeholders={field.placeholders}
                                      keys={field.keys}
                                      form={formData}
                                      isDisabled={isDisabled}
                                    />
                                  ) : (
                                    <Input
                                      key={fieldIndex}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      value={formData[field.value]}
                                      _placeholder={field._placeholder}
                                      type={field.type}
                                      style={{ width: field.inputWidth }}
                                      disabled={isDisabled}
                                    />
                                  )
                                )}
                              </Box>
                            </FormControl>
                          )
                        )}
                      </Box>
                      <Box
                        sx={{
                          padding: "15px",
                          width: {
                            base: "100% !important",
                            sm: "100%",
                            lg: "100%",
                          },
                          display: "grid",
                          gridTemplateColumns: "auto",
                        }}
                        className=" bgWhite col-sm-12 mt-2"
                      >
                        {RegistrationFormSectionFormFields[3].map(
                          (control, index) => (
                            <FormControl
                              key={index}
                              sx={{
                                display: "flex",
                                alignItems: "flex-start",
                                flexDirection: {
                                  base: "column",
                                  sm: "column",
                                  lg: "row",
                                },
                                marginTop: "10px",
                                flexWrap: "nowrap",
                              }}
                              isRequired={control.isRequired}
                            >
                              <FormLabel
                                htmlFor={control.fields[0].name}
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "30%",
                                  },
                                }}
                              >
                                {control.label}
                              </FormLabel>
                              <Box
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "70%",
                                  },
                                  display: "flex",
                                  gap: control.fields.length > 1 ? "10px" : "0",
                                }}
                              >
                                {control.fields.map((field, fieldIndex) =>
                                  field.component === "ComboBox" ? (
                                    <ComboBox
                                      key={fieldIndex}
                                      target={true}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      inputWidth={field.inputWidths}
                                      buttonWidth={field.buttonWidth}
                                      styleButton={{
                                        padding: "3px !important",
                                      }}
                                      tableData={
                                        field.tableData == "cash"
                                          ? cash
                                          : []
                                      }
                                      tableHeaders={field.tableHeaders}
                                      nameFields={field.nameFields}
                                      placeholders={field.placeholders}
                                      keys={field.keys}
                                      form={formData}
                                      isDisabled={isDisabled}
                                    />
                                  ) : (
                                    <Input
                                      key={fieldIndex}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      value={formData[field.value]}
                                      _placeholder={field._placeholder}
                                      type={field.type}
                                      style={{ width: field.inputWidth }}
                                      disabled={isDisabled}
                                    />
                                  )
                                )}
                              </Box>
                            </FormControl>
                          )
                        )}
                      </Box>
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2 pt-3 pb-3">
                      <Box
                        sx={{
                          width: {
                            base: "100% !important",
                            sm: "100%",
                            lg: "100%",
                          },
                          display: "grid",
                          gridTemplateColumns: "auto",
                        }}
                        className=" col-sm-12"
                      >
                        {RegistrationFormSectionFormFields[4].map(
                          (control, index) => (
                            <FormControl
                              key={index}
                              sx={{
                                display: "flex",
                                alignItems: "flex-start",
                                flexDirection: {
                                  base: "column",
                                  sm: "column",
                                  lg: "row",
                                },
                                marginTop: "10px",
                                flexWrap: "nowrap",
                              }}
                              isRequired={control.isRequired}
                            >
                              <FormLabel
                                htmlFor={control.fields[0].name}
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "30%",
                                  },
                                }}
                              >
                                {control.label}
                              </FormLabel>
                              <Box
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "70%",
                                  },
                                  display: "flex",
                                  gap: control.fields.length > 1 ? "10px" : "0",
                                }}
                              >
                                {control.fields.map((field, fieldIndex) =>
                                  field.component === "ComboBox" ? (
                                    <ComboBox
                                      key={fieldIndex}
                                      target={true}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      inputWidth={field.inputWidths}
                                      buttonWidth={field.buttonWidth}
                                      styleButton={{
                                        padding: "3px !important",
                                      }}
                                      tableData={
                                        field.tableData == "cash"
                                          ? cash
                                          : []
                                      }
                                      tableHeaders={field.tableHeaders}
                                      nameFields={field.nameFields}
                                      placeholders={field.placeholders}
                                      keys={field.keys}
                                      form={formData}
                                      isDisabled={isDisabled}
                                    />
                                  ) : (
                                    <Input
                                      key={fieldIndex}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      value={formData[field.value]}
                                      _placeholder={field._placeholder}
                                      type={field.type}
                                      style={{ width: field.inputWidth }}
                                      disabled={isDisabled}
                                    />
                                  )
                                )}
                              </Box>
                            </FormControl>
                          )
                        )}
                      </Box>
                    </div>
                  </div>

                  <div className="row">
                    <div className="bgWhite mt-2 pt-4 pb-4">
                      {RegistrationFormSectionFormFields[5].map(
                        (section, index) => (
                          <div key={index}>
                            <FormControl
                              sx={{
                                display: "flex",
                                flexDirection: {
                                  base: "column",
                                  sm: "column",
                                  lg: "row",
                                },
                              }}
                              isRequired
                            >
                              <FormLabel
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "15%",
                                  },
                                }}
                              >
                                {section.label}
                              </FormLabel>
                              <Box
                                sx={{
                                  display: "flex",
                                  gap: "5px",
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "85%",
                                  },
                                }}
                              >
                                {section.fields.map((field, fieldIndex) => (
                                  <ComboBox
                                    key={fieldIndex}
                                    target={true}
                                    onChange={handleInputChange}
                                    nameFields={field.nameFields}
                                    inputWidth={field.inputWidth}
                                    buttonWidth="20px"
                                    styleButton={{ padding: "3px !important" }}
                                    tableData={cash}
                                    tableHeaders={field.tableHeaders}
                                    placeholders={field.placeholders}
                                    keys={field.keys}
                                    form={formData}
                                    isDisabled={isDisabled}
                                  />
                                ))}
                              </Box>
                            </FormControl>
                          </div>
                        )
                      )}

                      <FormControl
                        sx={{
                          display: "flex",
                          marginTop: "10px",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                        }}
                      >
                        <FormLabel
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "15%" },
                          }}
                        >
                          Narration
                        </FormLabel>
                        <Textarea
                          _placeholder={{ color: "gray.500" }}
                          resize="vertical"
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "85%" },
                          }}
                          onChange={handleInputChange}
                          name={"Narration"}
                          value={formData.Narration}
                          disabled={isDisabled}
                        />
                      </FormControl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Toolbar
            save={handleSave}
            clear={clearForm}
            edit={editForm}
            first={() => navigateVoucherForm("first")}
            last={() => navigateVoucherForm("last")}
            previous={() => navigateVoucherForm("prev", formData.voucherNo)}
            next={() => navigateVoucherForm("next", formData.voucherNo)}
            remove={() => setDialogOpen(true)}
            isNavigation={isNavigation}
            isEdit={isEdit}
          />
        </>
      )}
      <ConfirmDialog
        isOpen={isDialogOpen}
        onClose={() => setDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Voucher"
        message={
          <>
            Are you sure you want to delete voucher no{" "}
            <b>{formData.voucherNo}</b> ?
          </>
        }
        confirmText="Delete"
        cancelText="Cancel"
        confirmColorScheme="red"
      />
    </>
  );
};

export default RegistrationForm;
