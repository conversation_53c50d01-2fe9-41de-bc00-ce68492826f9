"use client";
import React, { useState, useEffect } from 'react';
import "@src/app/dashboard/dashboard.css";
import axiosInstance from '@src/app/axios';
import { useRouter } from 'next/navigation';
import Loader from '@src/components/Loader/Loader';
import ReportTable from '@src/components/Custom/ReportTable';

const Dashboard = () => {
    const router = useRouter();
    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchTasks = async () => {
        try {
            const { data } = await axiosInstance.get("client/tasks/delivery");
            if (Array.isArray(data.data) && data.data.length > 0) {
                setTasks(data.data);
            } else {
                setTasks([]);
            }
        } catch (error) {
            console.error("Error: ", error);
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchTasks();
    }, []);

    const getStatusColor = (status) => {
        switch (status) {
            case 'rejected': return 'red';
            case 'pending': return 'yellow';
            case 'in-progress': return 'blue';
            case 'partial-completed': return 'orange';
            case 'completed': return 'green';
            default: return 'gray';
        }
    };

    const columns = [
        { header: 'Quotation no.',field: 'RefVoucherNo' },
        { header: 'Client', field: 'client_name' },
        { header: 'Delivered By', field: 'deliveredBy' },
        { header: 'Delivery Date', field: 'deliveryPersonTime', type: 'date' },
        { header: 'Status', field: 'status', type: 'badge' }
    ];

    const getRowCursor = (task) => (task.status === 'completed' || task.status === 'partial-completed') ? 'pointer' : 'auto';

    const handleRowClick = (task) => {
        if (task.status === 'completed' || task.status === 'partial-completed') {
            router.push(`/forms/delivery-report/follow-up?voucher_No=${task.RefVoucherNo}`);
        }
    };

    return (
        <>
            {loading ? (
                <Loader />
            ) : (
                <>
                    <div className="wrapper">
                        <div>
                            <div>
                                <div className="page-inner">
                                    <div className="row">
                                        <div className="bgWhite">
                                            <h1
                                                style={{
                                                    margin: "0",
                                                    textAlign: "center",
                                                    color: "#2B6CB0",
                                                    fontSize: "24px",
                                                    fontWeight: "bold",
                                                    padding: "10px",
                                                }}
                                            >
                                                Delivery Reports
                                            </h1>
                                        </div>
                                    </div>
                                    <div className="row">
                                        <ReportTable
                                            data={tasks}
                                            columns={columns}
                                            onRowClick={handleRowClick}
                                            getRowCursor={getRowCursor}
                                            getBadgeColor={getStatusColor}
                                            dateField="deliveryPersonTime"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </>
    );
}

export default Dashboard;