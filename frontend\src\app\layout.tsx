import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Script from "next/script";
import "@assets/assets/css/bootstrap.min.css";
import "@assets/assets/css/plugins.min.css";
import "@assets/assets/css/kaiadmin.min.css";
import { UserProvider } from './provider/UserContext';
import { Providers } from './providers';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "ECO Asset Manager",
  description: "Enterprise Resource Planning (ERP) Software developed by NexSol Tech for ECO Asset Management.",
  manifest: "/manifest.json"
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="shortcut icon" href="http://localhost:3000/assets/imgs/ECO_asset-manager.png" type="image/x-icon" />
        {/* Core JS Files */}
        <Script src="../assets/js/core/jquery-3.7.1.min.js" strategy="beforeInteractive" />
        <Script src="../assets/js/core/popper.min.js" strategy="beforeInteractive" />
        <Script src="../assets/js/core/bootstrap.min.js" strategy="beforeInteractive" />

        {/* jQuery Scrollbar */}
        <Script src="../assets/js/plugin/jquery-scrollbar/jquery.scrollbar.min.js" strategy="beforeInteractive" />

        {/* Chart JS */}
        {/* {<Script src="../assets/js/plugin/chart.js/chart.min.js" strategy="beforeInteractive" />} */}

        {/* jQuery Sparkline */}
        <Script src="../assets/js/plugin/jquery.sparkline/jquery.sparkline.min.js" strategy="beforeInteractive" />

        {/* Chart Circle */}
        {/* {<Script src="../assets/js/plugin/chart-circle/circles.min.js" />} */}

        {/* Datatables */}
        {/* {<Script src="../assets/js/plugin/datatables/datatables.min.js" />} */}

        {/* Bootstrap Notify */}
        {/* { <Script src="../assets/js/plugin/bootstrap-notify/bootstrap-notify.min.js" />} */}

        {/* jQuery Vector Maps */}
        <Script src="../assets/js/plugin/jsvectormap/jsvectormap.min.js" />
        <Script src="../assets/js/plugin/jsvectormap/world.js" />

        {/* Sweet Alert */}
        <Script src="../assets/js/plugin/sweetalert/sweetalert.min.js" />

        {/* Kaiadmin JS */}
        <Script src="../assets/js/kaiadmin.min.js" />
      </head>
      <body className={inter.className}>
        <Providers>
          <UserProvider>
            {children}
          </UserProvider>
        </Providers>
      </body>
    </html>
  );
}
