const fs = require("fs");
const https = require("https"); // ⬅️ use HTTPS instead of HTTP
const express = require("express");
const bodyParser = require("body-parser");
const cors = require("cors");
const helmet = require("helmet");
const compression = require("compression");
const rateLimit = require("express-rate-limit");
const socketIo = require("socket.io");
const ioEmitter = require("./modules/ioEmitter");

// Load environment variables
require("dotenv").config();

const app = express();
const PORT = process.env.PORT || 3000;

// Create server based on environment
let server;
let isHttps = false;
const isProduction = process.env.NODE_ENV === 'production';

if (isProduction) {
    // Production: Try to use HTTPS if SSL certificates exist
    try {
        if (fs.existsSync('./cert.pem') && fs.existsSync('./key.pem')) {
            const privateKey = fs.readFileSync("./key.pem", "utf8");
            const certificate = fs.readFileSync("./cert.pem", "utf8");
            const credentials = { key: privateKey, cert: certificate };

            server = https.createServer(credentials, app);
            isHttps = true;
            console.log('🔐 Production: SSL certificates found - running HTTPS server');
        } else {
            server = require('http').createServer(app);
            console.log('⚠️ Production: SSL certificates not found - running HTTP server');
        }
    } catch (error) {
        console.log('❌ Production: Error setting up SSL - falling back to HTTP:', error.message);
        server = require('http').createServer(app);
    }
} else {
    // Development: Always use HTTP
    server = require('http').createServer(app);
    console.log('🔧 Development: Running HTTP server (SSL disabled for local development)');
}

// Initialize Socket.IO on the server
const io = new socketIo.Server(server, { cors: { origin: "*" } });

// CORS configuration
const corsOptions = {
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
    credentials: true,
    preflightContinue: false
};

app.use(cors(corsOptions));
app.use(helmet({ contentSecurityPolicy: false, crossOriginEmbedderPolicy: false }));
app.use(compression());

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: "Too many requests from this IP, please try again later.",
});
app.use(limiter);

app.use(bodyParser.json());

// Swagger UI
const swaggerUi = require('swagger-ui-express');
const specs = require('./config/swagger');
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: "API Documentation",
}));

// Routes
app.use("/api/user", require("./routes/user"));
app.use("/api/clientorders", require("./routes/clientOrder"));
app.use("/api/menus", require("./routes/menu"));
app.use("/api/cashVoucher", require("./routes/cashVoucher"));
app.use("/api/bankVoucher", require("./routes/bankVoucher"));
app.use("/api/goodReceiptNotes", require("./routes/goodReceiptNote"));
app.use("/api/residentRegistrationForm", require("./routes/residentRegistrationForm"));
app.use("/api/storeIssuanceNote", require("./routes/storeIssuanceNote"));
app.use("/api/getRecords", require("./routes/getRecords"));
app.use("/api/offer", require("./routes/offer"));
app.use("/api/recoveryFollowUps", require("./routes/recoveryFollowUps"));
app.use('/api/deliveryChallan', require('./routes/deliveryChallan'));
app.use('/api/salesTaxInvoice', require('./routes/salesTaxInvoice'));
app.use("/api/generalLedger", require("./routes/generalLedger"));
app.use("/api/client", require("./routes/client"));
app.use("/api/email", require("./routes/email"));
app.use("/api/purpose", require("./routes/purpose"));
app.use("/api/notifications", require("./routes/notifications"));
app.use("/api/product", require("./routes/product"));
app.use("/api/employee", require("./routes/employee"));

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: "Something went wrong, please try again later." });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('Socket.IO client connected:', socket.id);
    socket.on('disconnect', (reason) => {
        console.log('Socket.IO client disconnected:', socket.id, 'Reason:', reason);
    });
});

// Emit events to all connected clients
ioEmitter.on("notifyAll", (event) => {
    console.log('Emitting event:', event.name, 'to all clients');
    io.emit(event.name, event.data);
});

// Start server
server.listen(PORT, '0.0.0.0', () => {
    const protocol = isHttps ? 'HTTPS' : 'HTTP';
    const baseUrl = isProduction
        ? (isHttps ? `https://***************:${PORT}` : `http://***************:${PORT}`)
        : `http://localhost:${PORT}`;

    console.log(`✅ ${protocol} server running at ${baseUrl}`);
    console.log(`📘 Swagger docs at ${baseUrl}/api-docs`);
    console.log(`📡 Socket.IO listening on ${protocol}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
