self.addEventListener('install', (event) => {
    self.skipWaiting();
});

self.addEventListener('activate', (event) => {
    event.waitUntil(clients.claim());
});

self.addEventListener('push', (event) => {    const options = {
        body: event.data?.text() || 'Default notification message',
        icon: '/icon512_rounded.png',
        badge: '/icon512_maskable.png',
        vibrate: [100, 50, 100],
        tag: 'eco-asset-manager',
        data: {
            dateOfArrival: Date.now(),
            primaryKey: '1',
            url: '/'  // Default URL to open
        },
        renotify: true
    };

    event.waitUntil(
        self.registration.showNotification('ECO Asset Manager', options)
    );
});

self.addEventListener('notificationclick', (event) => {
    event.notification.close();

    // Get the URL from notification data or use root URL
    const urlToOpen = event.notification.data?.url || '/';
    const fullUrl = new URL(urlToOpen, location.origin).href;

    // Ensure the browser focuses the tab if it exists, or opens a new one
    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true })
            .then((windowClients) => {
                // Try to focus an existing window
                for (let client of windowClients) {
                    if (client.url === fullUrl && 'focus' in client) {
                        return client.focus();
                    }
                }
                // If no matching window, open a new one
                if (clients.openWindow) {
                    return clients.openWindow(rootUrl);
                }
            })
    );
});
