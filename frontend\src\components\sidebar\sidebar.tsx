'use client'
import {  Box, Drawer, DrawerContent, useDisclosure } from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';
import SidebarContent from './SidebarContent';
import MobileNav from './MobileNav';

const Sidebar = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Box h={70} bg="gray.100">
      <>
        <SidebarContent onClose={onClose} display={{ base: 'none', md: 'block' }} />
        <Drawer
          isOpen={isOpen}
          placement="left"
          onClose={onClose}
          returnFocusOnClose={false}
          onOverlayClick={onClose}
          size="full"
        >
          <DrawerContent>
            <SidebarContent onClose={onClose} />
          </DrawerContent>
        </Drawer>
      </>
      <MobileNav onOpen={onOpen} />
    </Box>
  );
};

export default Sidebar;
