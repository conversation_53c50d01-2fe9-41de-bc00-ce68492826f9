import React from 'react';
import {
    <PERSON><PERSON>,
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalCloseButton,
    ModalBody,
    ModalFooter,
} from '@chakra-ui/react';
import ComboBox from '../ComboBox/ComboBox';

const CopyDialog = ({
    isOpen,
    onClose,
    onConfirm,
    formData = {},
    setFormData = () => { },
    tableData = [],
}) => {

    const handleInputChange = (singleInput, bulkInput) => {
        if (singleInput) {
            let { name, value } = singleInput.target || singleInput;
            setFormData((prev) => ({ ...prev, [name]: value }));
        } else if (bulkInput) {
            setFormData((prev) => ({ ...prev, ...bulkInput }));
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose}>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>Copy Voucher</ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                    <ComboBox
                        target={true}
                        onChange={handleInputChange}
                        name={"copy"}
                        buttonWidth={"20px"}
                        styleButton={{ padding: "3px !important" }}
                        tableData={tableData}
                        tableHeaders={["Voucher No"]}
                        nameFields={["copyVoucherNo"]}
                        inputWidths={["100%"]}
                        placeholders={["Voucher ID"]}
                        keys={["ID"]}
                        form={formData}
                    />
                </ModalBody>

                <ModalFooter>
                    <Button
                        colorScheme={"blue"}
                        disabled={!formData.copyVoucherNo}
                        onClick={() => {
                            onConfirm();
                            onClose();
                        }}
                        ml={3}
                    >
                        Copy
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

export default CopyDialog;
