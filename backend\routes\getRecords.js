const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');

router.use(authorization);

async function getRecords(req, res, query) {
    const pool = await getPool();
    const request = new sql.Request(pool);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
}

router.get('/currencies', (req, res) => {
    getRecords(req, res, 'SELECT ID as id FROM Currencies');
});

router.get('/projects', (req, res) => {
    getRecords(req, res, 'SELECT ID as id, Title as title FROM defprojects');
});

router.get('/costCenters', (req, res) => {
    getRecords(req, res, 'SELECT ID as id, Title as title FROM costcenters');
});

router.get('/bankAccounts', (req, res) => {
    getRecords(req, res, "SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'BA'");
});

router.get('/cashAccounts', (req, res) => {
    getRecords(req, res, "SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CA'");
});

router.get('/transporter', (req, res) => {
    getRecords(req, res, "SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'FA'");
});

router.get('/salesMan', (req, res) => {
    getRecords(req, res, 'SELECT ID,Title FROM EmployeeDetails');
});

router.get('/items', (req, res) => {
    getRecords(req, res, 'SELECT id, Title, Unit, Details, Sale_Rate  FROM Coa31');
});

router.get('/clients', (req, res) => {
    getRecords(req, res, "SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CT'");
});

router.get('/client-leads', (req, res) => {
    getRecords(req, res, "Select Voucher_No AS ID from Offer");
});

router.get('/suppliers', (req, res) => {
    getRecords(req, res, "SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'SP'");
});

router.get('/assessors', (req, res) => {
    const { includeAdmin } = req.query;
    getRecords(req, res, "Select E.ID, U.Id AS Title from EmployeeDetails E LEFT JOIN users U on U.Emp_ID = E.ID WHERE U.RoleID = 3" + (includeAdmin ? ' OR U.RoleID = 1' : ''));
});

router.get('/deliveryMen', (req, res) => {
    getRecords(req, res, "Select E.ID,E.Title from EmployeeDetails E LEFT JOIN users U on U.Emp_ID = E.ID WHERE U.RoleID = 4");
});

router.get('/installers', (req, res) => {
    getRecords(req, res, "Select E.ID,E.Title from EmployeeDetails E LEFT JOIN users U on U.Emp_ID = E.ID WHERE U.RoleID = 5");
});

router.get('/categories', (req, res) => {
    getRecords(req, res, 'SELECT id,title FROM Categories');
});

router.get('/manufacturers', (req, res) => {
    getRecords(req, res, 'SELECT id,title FROM Manufacturers');
});

router.get('/roles', (req, res) => {
    getRecords(req, res, 'Select RoleID AS id,RoleName AS title FROM User_Role');
});

router.get('/stats', (req, res) => {
    getRecords(req, res, `
        SELECT 
                (SELECT COUNT(*) FROM Coa32) as totalContacts,
                (SELECT COUNT(*) FROM ClientPurpose) as totalPurposes,
                (SELECT COUNT(*) FROM Users) as totalUsers,
                (SELECT COUNT(*) FROM ClientPurpose WHERE IsApproved = 1) as approvedPurposes,
                (SELECT COUNT(*) FROM ClientPurpose WHERE IsApproved = 0) as rejectedPurposes,
                (SELECT COUNT(*) FROM ClientPurpose WHERE IsApproved IS NULL) as pendingPurposes
    `);
});

router.get('/offers', (req, res) => {
    getRecords(req, res, 'Select O.voucher_No as voucherNo, O.client_id as clientID, C.title AS clientName, O.GrossAmount as total FROM Offer O LEFT JOIN coa32 C ON C.id = O.client_id');
});

module.exports = router;