"use client";
import React, { useState, useEffect } from 'react';
import "@src/app/dashboard/dashboard.css";
import { Box, Button, useToast } from '@chakra-ui/react';
import axiosInstance from '@src/app/axios';
import { useRouter } from 'next/navigation';
import AddDeliveryModal from './AddDeliveryModal';
import ReportTable from '@src/components/Custom/ReportTable';
import Loader from '@src/components/Loader/Loader';

const Dashboard = () => {
    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    const [isDeliveryModalOpen, setIsDeliveryModalOpen] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    const router = useRouter();
    const toast = useToast();

    const fetchTasks = async () => {
        try {
            const { data } = await axiosInstance.get("purpose/assessor/completed");
            if (Array.isArray(data.data) && data.data.length > 0) {
                setTasks(data.data);
            } else {
                setTasks([]);
            }
        } catch (error) {
            console.error("Error: ", error);
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchTasks();
    }, []);

    const handleOpenDeliveryModal = (task) => {
        setSelectedTask(task);
        setIsDeliveryModalOpen(true);
    };

    const handleCloseDeliveryModal = () => {
        setSelectedTask(null);
        setIsDeliveryModalOpen(false);
    };

    const handleDeliveryAssigned = () => {
        fetchTasks();
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'rejected': return 'red';
            case 'pending': return 'yellow';
            case 'approved': return 'green';
            default: return 'gray';
        }
    };

    const columns = [
        { header: '#', field: 'ID' },
        { header: 'Assigned To', field: 'assessorTitle' },
        { header: 'Contact', field: 'contactPerson' },
        { header: 'Assessment Date', field: 'assessmentTime', type: 'date' },
        { header: 'Status', field: 'status', type: 'badge' },
        {
            header: 'Actions',
            field: 'actions',
            render: (task) => (
                <Button
                    colorScheme="blue"
                    size="sm"
                    disabled={task.status !== 'approved' || task.deliveryPersonID}
                    onClick={() => handleOpenDeliveryModal(task)}
                >
                    Assign Jobs
                </Button>
            )
        }
    ];

    const getRowCursor = (task) => task.status === 'pending' ? 'pointer' : 'auto';

    const handleRowClick = (task) => {
        if (task.status === 'pending') {
            router.push(`/forms/audit-report/follow-up?purpose_no=${task.ID}`);
        }
    };

    return (
        <>
            {loading ? (
                <Loader />
            ) : (
                <>
                    <div className="wrapper">
                        <div>
                            <div>
                                <div className="page-inner">
                                    <div className="row">
                                        <div className="bgWhite">
                                            <h1
                                                style={{
                                                    margin: "0",
                                                    textAlign: "center",
                                                    color: "#2B6CB0",
                                                    fontSize: "24px",
                                                    fontWeight: "bold",
                                                    padding: "10px",
                                                }}
                                            >
                                                Audit Reports
                                            </h1>
                                        </div>
                                    </div>
                                    <div className="row">
                                        <ReportTable
                                            data={tasks}
                                            columns={columns}
                                            onRowClick={handleRowClick}
                                            getRowCursor={getRowCursor}
                                            getBadgeColor={getStatusColor}
                                            dateField="assessmentTime"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <AddDeliveryModal
                        isOpen={isDeliveryModalOpen}
                        onClose={handleCloseDeliveryModal}
                        onSave={handleDeliveryAssigned}
                        purposeId={selectedTask?.ID}
                    />
                </>
            )}
        </>
    );
}

export default Dashboard;