const express = require('express');
const router = express.Router();
const employeeController = require('../controllers/employeeController');
const employeeValidationRules = require('../validations/employeeValidation');

/**
 * @swagger
 * /api/employee:
 *   post:
 *     summary: Create a new employee
 *     tags: [Employees]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Employee'
 *     responses:
 *       201:
 *         description: Employee created successfully
 *       400:
 *         description: Bad request - Invalid input
 *       500:
 *         description: Server error
 */
router.post('/create', employeeValidationRules.create, employeeController.createEmployee);

/**
 * @swagger
 * /api/employee/next-id:
 *   get:
 *     summary: Get the next employee ID
 *     tags: [Employees]
 *     responses:
 *       200:
 *         description: Next employee ID generated successfully
 *       500:
 *         description: Server error
 */
router.get('/next-id', employeeController.getNextEmployeeId);

/**
 * @swagger
 * /api/employee/{id}:
 *   get:
 *     summary: Get an employee by ID
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Employee ID
 *     responses:
 *       200:
 *         description: Employee retrieved successfully
 *       404:
 *         description: Employee not found
 *       500:
 *         description: Server error
 */
router.get('/:id', employeeController.getEmployeeById);

/**
 * @swagger
 * /api/employee/{id}:
 *   delete:
 *     summary: Delete an employee by ID
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Employee ID
 *     responses:
 *       200:
 *         description: Employee deleted successfully
 *       404:
 *         description: Employee not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', employeeController.deleteEmployee);

module.exports = router;
