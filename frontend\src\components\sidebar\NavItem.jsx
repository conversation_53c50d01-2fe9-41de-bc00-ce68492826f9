import { Box, Flex, Icon, Text } from '@chakra-ui/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiChevronDown, FiChevronRight } from 'react-icons/fi';

const MotionBox = motion.create(Box);

const NavItem = ({ icon, children, path, subItems, ...rest }) => {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const isActive = path && pathname ? pathname.includes(path) : false;

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      <Box onClick={toggleDropdown}>
        <Link href={path || '#'} passHref>
          <Flex
            className='Rest'
            align="center"
            p="4"
            cursor="pointer"
            bg={isActive ? '#0d265b' : 'transparent'}
            color={isActive ? 'white' : '#d9d9d9'}
            _hover={{
              bg: '#0d265b',
              color: 'white',
            }}
            {...rest}
          >
            {icon && (
              <Icon
                mr="4"
                fontSize="16"
                as={icon}
                color={isActive ? 'white' : '#d9d9d9'}
                _groupHover={{
                  color: 'white',
                }}
              />
            )}
            <Text>{children}</Text>
            {subItems && (
              <Icon as={isOpen ? FiChevronDown : FiChevronRight} ml="auto" />
            )}
          </Flex>
        </Link>
      </Box>

      <MotionBox
        pl="12"
        initial={{ height: 0, opacity: 0 }}
        animate={{
          height: isOpen ? 'auto' : 0,
          opacity: isOpen ? 1 : 0,
          transition: {
            duration: 0.2,
            ease: 'easeInOut',
          },
        }}
        exit={{
          height: 0,
          opacity: 0,
          transition: {
            duration: 0.2,
            ease: 'easeInOut',
          },
        }}
        overflow="hidden"
      >
        {isOpen && subItems && subItems.map((subLink) => (
          <NavItem
            key={subLink.name}
            icon={subLink.icon}
            path={subLink.path}
            subItems={subLink.subItems}
          >
            {subLink.name}
          </NavItem>
        ))}
      </MotionBox>
    </>
  );
};

export default NavItem;
