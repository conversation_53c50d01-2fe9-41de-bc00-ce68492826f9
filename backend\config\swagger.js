const swaggerJsdoc = require('swagger-jsdoc');

const options = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'User Management API',
            version: '1.0.0',
            description: 'API documentation for User Management System',
        },
        servers: [{
                url: 'http://localhost:5000',
                description: 'Development server',
            },
            {
                url: 'http://***************:5000',
                description: 'Production server (HTTPS)',
            }
        ],
        components: {
            securitySchemes: {
                basicAuth: {
                    type: 'http',
                    scheme: 'basic',
                    description: 'Basic authentication using username and password'
                }
            },
            schemas: {
                User: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'string',
                            description: 'User ID'
                        },
                        user_name: {
                            type: 'string',
                            description: 'Username'
                        },
                        Location: {
                            type: 'string',
                            description: 'User location'
                        },
                        Title: {
                            type: 'string',
                            description: 'User title'
                        },
                        RoleID: {
                            type: 'string',
                            description: 'Role ID'
                        },
                        RoleName: {
                            type: 'string',
                            description: 'Role name'
                        },
                        Emp_ID: {
                            type: 'string',
                            description: 'Employee ID'
                        }
                    }
                },
                LoginRequest: {
                    type: 'object',
                    required: ['userName', 'password'],
                    properties: {
                        userName: {
                            type: 'string',
                            description: 'Username for login'
                        },
                        password: {
                            type: 'string',
                            description: 'Password for login'
                        }
                    }
                },
                LoginResponse: {
                    type: 'object',
                    properties: {
                        message: {
                            type: 'string',
                            description: 'Response message'
                        },
                        data: {
                            $ref: '#/components/schemas/User'
                        }
                    }
                }
            }
        },
        security: [{
            basicAuth: []
        }]
    },
    apis: ['./routes/*.js'], // Path to the API routes
};

const specs = swaggerJsdoc(options);
module.exports = specs;