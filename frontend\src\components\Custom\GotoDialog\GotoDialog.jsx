import React from 'react';
import {
    Button,
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalCloseButton,
    ModalBody,
    ModalFooter,
    Input,
    HStack,
} from '@chakra-ui/react';

const GotoDialog = ({
    isOpen,
    onClose,
    onConfirm,
    formData = {},
    setFormData = () => { },
}) => {

    const handleVoucherChange = (e) => {
        const { name, value } = e.target;
        setFormData((prev) => {
            const newFormData = { ...prev, [name]: value };
            newFormData.voucherNo = `${newFormData.vtp || ''}/${newFormData.mnth || ''}/${newFormData.location || ''}/${newFormData.vno || ''}`;
            return newFormData;
        });
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose}>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>Goto Voucher</ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                    <HStack spacing={2} mt={4}>
                        <Input
                            placeholder="VTP"
                            name="vtp"
                            value={formData.vtp || ''}
                            onChange={handleVoucherChange}
                        />
                        <Input
                            placeholder="Mnth"
                            name="mnth"
                            value={formData.mnth || ''}
                            onChange={handleVoucherChange}
                        />
                        <Input
                            placeholder="Location"
                            name="location"
                            value={formData.location || ''}
                            onChange={handleVoucherChange}
                        />
                        <Input
                            placeholder="Vno"
                            name="vno"
                            value={formData.vno || ''}
                            onChange={handleVoucherChange}
                        />
                    </HStack>
                </ModalBody>

                <ModalFooter>
                    <Button
                        colorScheme={"blue"}
                        disabled={!formData.voucherNo || !formData.vtp || !formData.mnth || !formData.location || !formData.vno}
                        onClick={() => {
                            onConfirm("goto", formData.voucherNo);
                            onClose();
                        }}
                        ml={3}
                    >
                        Goto
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

export default GotoDialog;
