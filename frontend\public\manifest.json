{"theme_color": "#196d3c", "background_color": "#1e7c45", "gcm_sender_id": "103953800507", "permissions": ["notifications"], "icons": [{"purpose": "maskable", "sizes": "512x512", "src": "icon512_maskable.png", "type": "image/png"}, {"purpose": "any", "sizes": "512x512", "src": "icon512_rounded.png", "type": "image/png"}, {"purpose": "notification", "sizes": "192x192", "src": "icon512_rounded.png", "type": "image/png"}], "orientation": "any", "display": "standalone", "dir": "auto", "lang": "en-US", "name": "ECO Asset Manager", "short_name": "ECO ERP", "start_url": "/", "id": "ECO", "description": "ECO Asset Manager: Next-Gen Enterprise Resource Planning (ERP) PWA\nDeveloped by NexSol Tech, the ECO Asset Manager is a cutting-edge Progressive Web Application (PWA) designed to revolutionize enterprise resource planning for modern businesses. Built on a robust, future-ready tech stack—Next.js (frontend), Node.js (backend), and Microsoft SQL Server (database)—this application delivers unparalleled performance, scalability, and security to streamline your organization’s asset management, operational workflows, and data-driven decision-making.\n\nKey Features\nSeamless PWA Experience: Accessible offline, lightning-fast load times, and cross-device compatibility (desktop, tablet, mobile) with native app-like functionality.\n\nReal-Time Asset Tracking: Monitor resources, inventory, and equipment in real time with intuitive dashboards and analytics.\n\nAdvanced Database Management: Leverage SQL Server for secure, high-performance data storage, complex query handling, and ACID compliance.\n\nModular ERP Solutions: Customizable modules for procurement, HR, finance, supply chain, and maintenance tailored to your business needs.\n\nScalable Architecture: Next.js ensures server-side rendering (SSR) for SEO-friendly, dynamic UIs, while Node.js powers a non-blocking backend for high concurrency.\n\nRole-Based Access Control: Secure multi-tier user permissions and audit trails to safeguard sensitive enterprise data.\n\nTechnical Excellence\nFrontend: Next.js (React-based framework) for responsive, SEO-optimized interfaces.\n\nBackend: Node.js with Express.js for RESTful APIs, ensuring rapid data processing and integration.\n\nDatabase: SQL Server for enterprise-grade reliability, transactional integrity, and advanced reporting.\n\nSecurity: End-to-end encryption and compliance with industry standards (GDPR, HIPAA-ready).\n\nTransform your resource management today. Experience the power of Next.js, Node.js, and SQL Server with NexSol Tech.\n\nContact NexSol Tech to schedule a demo or customize your ERP solution!", "scope": "/", "display_override": ["fullscreen", "standalone", "window-controls-overlay"], "categories": ["business", "productivity"], "shortcuts": [{"name": "Dashboard", "url": "https://nexsol-erp.vercel.app/dashboard", "description": "Dashboard"}, {"name": "Client Order", "url": "https://nexsol-erp.vercel.app/forms/client-order", "description": "Client Order"}]}