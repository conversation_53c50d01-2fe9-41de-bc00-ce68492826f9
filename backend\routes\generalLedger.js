const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');

router.use(authorization);

// ------------------- General <PERSON><PERSON> Report API ------------------- //
router.post('/', async (req, res) => {
    try {
        const {
            p_id, s_id, st_date, en_date, OrderWise = 0,
            iLocation = '', CCID = '', PrID = '', AorB = 0,
            PeriodFrom = 0, PeriodTo = 0, FinancialPeriod = '',
            CWise = 0, User = ''
        } = req.body;

        if (!p_id || !s_id || !st_date || !en_date) {
            return res.status(400).json({ message: 'p_id, s_id, st_date, and en_date are required.' });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        request.input('p_id', sql.VarChar(50), p_id);
        request.input('s_id', sql.VarChar(50), s_id);
        request.input('st_date', sql.Date, st_date);
        request.input('en_date', sql.Date, en_date);
        request.input('OrderWise', sql.Int, OrderWise);
        request.input('iLocation', sql.VarChar(50), iLocation);
        request.input('CCID', sql.VarChar(50), CCID);
        request.input('PrID', sql.VarChar(50), PrID);
        request.input('AorB', sql.Int, AorB);
        request.input('PeriodFrom', sql.VarChar, PeriodFrom);
        request.input('PeriodTo', sql.VarChar, PeriodTo);
        request.input('FinancialPeriod', sql.VarChar(50), FinancialPeriod);
        request.input('CWise', sql.Int, CWise);
        request.input('User', sql.VarChar(50), User);

        const query = `
            SELECT * FROM f_gvdtfrprt(
                @p_id, @s_id, @st_date, @en_date, 
                @OrderWise, @iLocation, @CCID, @PrID, 
                @AorB, @PeriodFrom, @PeriodTo, @FinancialPeriod, 
                @CWise, @User
            )
        `;

        const result = await request.query(query);

        res.status(200).json(result.recordset);
    } catch (error) {
        console.error('Error fetching General Ledger Report:', error);
        res.status(500).json({ message: 'Internal Server Error', error: error.message });
    }
});

module.exports = router;
