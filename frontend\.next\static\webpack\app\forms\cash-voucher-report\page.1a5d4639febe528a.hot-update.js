"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @src/components/Custom/ServerPaginatedTable */ \"(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx\");\n/* harmony import */ var _src_components_Custom_VoucherDetailsModal_VoucherDetailsModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @src/components/Custom/VoucherDetailsModal/VoucherDetailsModal */ \"(app-pages-browser)/./src/components/Custom/VoucherDetailsModal/VoucherDetailsModal.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        totalCount: 0,\n        totalPages: 0\n    });\n    const [selectedVoucher, setSelectedVoucher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voucherDetails, setVoucherDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentHistory, setPaymentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalLoading, setModalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const fetchData = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n            console.log(response.data);\n            if (response.data && response.data.data) {\n                setData(response.data.data);\n                setPagination({\n                    page: response.data.page,\n                    pageSize: response.data.pageSize,\n                    totalCount: response.data.totalCount,\n                    totalPages: response.data.totalPages\n                });\n            } else {\n                setData([]);\n                setPagination({\n                    page: 1,\n                    pageSize: 20,\n                    totalCount: 0,\n                    totalPages: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch cash voucher report data\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchData();\n    }, []);\n    const fetchVoucherDetails = async (voucherNo)=>{\n        try {\n            setModalLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/voucher-details?voucherNo=\".concat(voucherNo));\n            if (response.data) {\n                setVoucherDetails(response.data.voucher_details);\n                setPaymentHistory(response.data.payment_history || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching voucher details: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch voucher details\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setModalLoading(false);\n        }\n    };\n    const handleOpenModal = async (voucher)=>{\n        setSelectedVoucher(voucher);\n        setIsModalOpen(true);\n        await fetchVoucherDetails(voucher.voucher_no);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedVoucher(null);\n        setVoucherDetails(null);\n        setPaymentHistory([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"voucher_no\"\n        },\n        {\n            header: \"Quotation No (adj)\",\n            field: \"quotation_no\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"client_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"client_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"gross_amount\",\n            render: (item)=>formatCurrency(item.gross_amount)\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"paid_amount\",\n            render: (item)=>formatCurrency(item.paid_amount)\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"remaining_amount\",\n            render: (item)=>formatCurrency(item.remaining_amount)\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        },\n        {\n            header: \"Actions\",\n            field: \"actions\",\n            render: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                    colorScheme: \"blue\",\n                    size: \"sm\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleOpenModal(item);\n                    },\n                    children: \"View Details\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 148,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    const getRowCursor = ()=>\"pointer\";\n    const handleRowClick = (item)=>{\n        handleOpenModal(item);\n    };\n    const handlePageChange = (newPage)=>{\n        fetchData(newPage, pagination.pageSize);\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        fetchData(1, newPageSize);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 179,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Cash Voucher Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            data: data,\n                                            columns: columns,\n                                            pagination: pagination,\n                                            onPageChange: handlePageChange,\n                                            onPageSizeChange: handlePageSizeChange,\n                                            onRowClick: handleRowClick,\n                                            getRowCursor: getRowCursor,\n                                            getBadgeColor: getStatusColor,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                lineNumber: 185,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 183,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 182,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n                    isOpen: isModalOpen,\n                    onClose: handleCloseModal,\n                    size: \"6xl\",\n                    scrollBehavior: \"inside\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalOverlay, {}, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 227,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalContent, {\n                            maxH: \"90vh\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalHeader, {\n                                    bg: \"blue.600\",\n                                    color: \"white\",\n                                    borderTopRadius: \"md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        fontSize: \"xl\",\n                                        fontWeight: \"bold\",\n                                        children: [\n                                            \"Voucher Details - \",\n                                            selectedVoucher === null || selectedVoucher === void 0 ? void 0 : selectedVoucher.voucher_no\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalCloseButton, {\n                                    color: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalBody, {\n                                    p: 6,\n                                    children: modalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Box, {\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        alignItems: \"center\",\n                                        minH: \"200px\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Spinner, {\n                                            size: \"xl\",\n                                            color: \"blue.500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 37\n                                    }, undefined) : voucherDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                        spacing: 6,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Box, {\n                                                p: 4,\n                                                bg: \"gray.50\",\n                                                borderRadius: \"md\",\n                                                border: \"1px solid\",\n                                                borderColor: \"gray.200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: \"Voucher Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Voucher No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.voucher_no\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                        colorScheme: getStatusColor(voucherDetails.status),\n                                                                        fontSize: \"sm\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        children: voucherDetails.status.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Divider, {\n                                                        my: 4\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Gross Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"blue.600\",\n                                                                        children: formatCurrency(voucherDetails.gross_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Paid Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"green.600\",\n                                                                        children: formatCurrency(voucherDetails.paid_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Remaining Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"red.600\",\n                                                                        children: formatCurrency(voucherDetails.remaining_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Created Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.created_at ? dayjs(voucherDetails.created_at).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: [\n                                                            \"Payment History (\",\n                                                            paymentHistory.length,\n                                                            \" payments)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    paymentHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContainer, {\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        borderRadius: \"md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Table, {\n                                                            variant: \"simple\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Thead, {\n                                                                    bg: \"gray.100\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tr, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Th, {\n                                                                                children: \"Payment Voucher No\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Th, {\n                                                                                children: \"Payment Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Th, {\n                                                                                isNumeric: true,\n                                                                                children: \"Amount\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 333,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Th, {\n                                                                                children: \"Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 334,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Th, {\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 65\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 57\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tbody, {\n                                                                    children: paymentHistory.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tr, {\n                                                                            _hover: {\n                                                                                bg: \"gray.50\"\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Td, {\n                                                                                    fontWeight: \"semibold\",\n                                                                                    children: payment.payment_voucher_no\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 341,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Td, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                                                        colorScheme: payment.payment_type === \"CR\" ? \"green\" : \"blue\",\n                                                                                        variant: \"subtle\",\n                                                                                        children: payment.payment_type === \"CR\" ? \"Cash Receipt\" : payment.payment_type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                        lineNumber: 345,\n                                                                                        columnNumber: 73\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 344,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Td, {\n                                                                                    isNumeric: true,\n                                                                                    fontWeight: \"semibold\",\n                                                                                    color: \"green.600\",\n                                                                                    children: formatCurrency(payment.payment_amount)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Td, {\n                                                                                    children: payment.payment_date ? dayjs(payment.payment_date).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 355,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Td, {\n                                                                                    children: payment.payment_description || \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 361,\n                                                                                    columnNumber: 69\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 65\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 49\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Box, {\n                                                        p: 8,\n                                                        textAlign: \"center\",\n                                                        bg: \"gray.50\",\n                                                        borderRadius: \"md\",\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                color: \"gray.500\",\n                                                                fontSize: \"lg\",\n                                                                children: \"No payments found for this voucher\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                color: \"gray.400\",\n                                                                fontSize: \"sm\",\n                                                                mt: 1,\n                                                                children: \"This voucher is still pending payment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 37\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Box, {\n                                        textAlign: \"center\",\n                                        py: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            color: \"gray.500\",\n                                            children: \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 228,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"to0F4F3T6vvp+5aeff+YHPpyvK4=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ })

});