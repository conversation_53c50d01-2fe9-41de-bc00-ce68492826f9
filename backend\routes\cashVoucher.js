const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { cashColumns, cashItemColumns } = require('./utils/constant')

router.use(authorization);

// ------------------- Cash Receipt (CR) Cash Payment (CP) APIS ------------------- //

router.post('/getVoucherNo', async (req, res) => {
    const { Mnth, vtp } = req.body;
    const query = 'select MAX(vno) AS vno from AC where Mnth = @Mnth AND vtp = @vtp';

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('Mnth', sql.VarChar(4), Mnth);
    request.input('vtp', sql.VarChar(4), vtp);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json({
            vtp: vtp,
            location: 'EAM',
            vno: result.recordset[0].vno ? result.recordset[0].vno + 1 : 1,
            Mnth,
            voucherNo: vtp + '/' + Mnth + '/EAM/' + (result.recordset[0].vno ? result.recordset[0].vno + 1 : 1)
        });
    });
});

router.post('/navigate', async (req, res) => {
    const { next, prev, first, last, voucher_no, voucher_vtp } = req.body;
    const pool = await getPool();
    const request = new sql.Request(pool);

    let query = '';
    const baseQuery = `
        SELECT
        TOP 1
            AC.*, 
            C.Title AS CostCenter_Name,
            CashDetails.title AS bc_name
        FROM 
            AC
        LEFT JOIN 
            costcenters C ON C.ID = AC.CostCenter_ID
        LEFT JOIN 
            (SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CA') AS CashDetails ON CashDetails.id = AC.bc_id
    `;

    if ((next && (prev || first || last)) || (prev && (first || last)) || (first && last) || (!next && !prev && !first && !last)) {
        return res.status(400).json({ message: "Invalid request. Use either 'next', 'prev', 'first', or 'last' exclusively, and provide 'voucher_no' if using 'next' or 'prev'." });
    }

    if (next) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'next'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = parts[2];
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                AC.vtp > @vtp 
                OR (AC.vtp = @vtp AND AC.Mnth > @Mnth)
                OR (AC.vtp = @vtp AND AC.Mnth = @Mnth AND AC.Location > @Location)
                OR (AC.vtp = @vtp AND AC.Mnth = @Mnth AND AC.Location = @Location AND AC.vno > @vno)
            ) AND AC.vtp = @vtp
            ORDER BY AC.vtp, AC.Mnth, AC.Location, AC.vno;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (prev) {
        if (!voucher_no) {
            return res.status(400).json({ message: "'voucher_no' is required when using 'prev'." });
        }

        const parts = voucher_no.split('/');
        const vtp = parts[0];
        const Mnth = parts[1];
        const Location = parts[2];
        const vno = parts[3];

        query = `
            ${baseQuery}
            WHERE (
                AC.vtp < @vtp 
                OR (AC.vtp = @vtp AND AC.Mnth < @Mnth)
                OR (AC.vtp = @vtp AND AC.Mnth = @Mnth AND AC.Location < @Location)
                OR (AC.vtp = @vtp AND AC.Mnth = @Mnth AND AC.Location = @Location AND AC.vno < @vno)
            ) AND AC.vtp = @vtp
            ORDER BY AC.vtp DESC, AC.Mnth DESC, AC.Location DESC, AC.vno DESC;`;

        request.input('vtp', sql.VarChar(3), vtp);
        request.input('Mnth', sql.VarChar(4), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);

    } else if (first) {
        if (!voucher_vtp) {
            return res.status(400).json({ message: "'voucher_vtp' is required when using 'first'." });
        }
        query = `${baseQuery} WHERE AC.vtp = @vtp ORDER BY AC.VTP, AC.Mnth, AC.Location, AC.vno;`;
        request.input('vtp', sql.VarChar(3), voucher_vtp);
    } else if (last) {
        if (!voucher_vtp) {
            return res.status(400).json({ message: "'voucher_vtp' is required when using 'last'." });
        }
        query = `${baseQuery} WHERE AC.vtp = @vtp ORDER BY AC.VTP DESC, AC.Mnth DESC, AC.Location DESC, AC.vno DESC;`;
        request.input('vtp', sql.VarChar(3), voucher_vtp);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }

        if (result.recordset.length > 0) {
            const order = result.recordset[0];
            const orderVoucherNo = order.Voucher_No;

            const detailsQuery = `
            SELECT
                ac_det.*,
                Client.title AS acc_name,
                E.Title AS Emp_Name
            FROM 
                ac_det
            LEFT JOIN 
                EmployeeDetails E ON E.ID = ac_det.Emp_ID
            LEFT JOIN 
                (SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CT')
                AS Client ON Client.id = ac_det.acc_id WHERE voucher_no = @orderVoucherNo`;
            const detailsRequest = new sql.Request(pool);
            detailsRequest.input('orderVoucherNo', sql.VarChar, orderVoucherNo);

            detailsRequest.query(detailsQuery, (detailsErr, detailsResult) => {
                if (detailsErr) {
                    return res.status(500).send(detailsErr);
                }

                order["items"] = detailsResult.recordset;
                res.status(200).json(order);
            });
        } else {
            res.status(404).json({ message: 'No more records available in this direction.' });
        }
    });
});

router.post('/', async (req, res) => {
    const { voucher_vtp } = req.body;
    const query = 'SELECT * FROM AC WHERE vtp = @vtp';

    if (!voucher_vtp) {
        return res.status(400).json({ message: "'voucher_vtp' is required." });
    }

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('vtp', sql.VarChar(3), voucher_vtp);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/getbyvoucher', async (req, res) => {
    const { voucherNo } = req.body;

    if (!voucherNo) {
        return res.status(400).send('Voucher_No is required.');
    }

    const query = `
        SELECT
        TOP 1
            AC.*, 
            C.Title AS CostCenter_Name,
            CashDetails.title AS bc_name
        FROM 
            AC
        LEFT JOIN 
            costcenters C ON C.ID = AC.CostCenter_ID
        LEFT JOIN 
            (SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CA') AS CashDetails ON CashDetails.id = AC.bc_id
        WHERE Voucher_No = @Voucher_No
    `;
    const itemQuery = `
        SELECT
            ac_det.*,
            Client.title AS acc_name,
            E.Title AS Emp_Name
        FROM 
            ac_det
        LEFT JOIN 
            EmployeeDetails E ON E.ID = ac_det.Emp_ID
        LEFT JOIN 
            (SELECT C.id, C.title FROM coa32 C LEFT JOIN Coa3 C3 ON C3.id = C.id WHERE C3.atp2_ID = 'CT')
            AS Client ON Client.id = ac_det.acc_id 
        WHERE voucher_no = @Voucher_No`;

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        request.input('Voucher_No', sql.VarChar(16), voucherNo);

        const result = await request.query(query);

        if (result.recordset.length === 0) {
            await transaction.rollback();
            return res.status(404).send('Voucher not found.');
        }

        const items = await request.query(itemQuery);

        await transaction.commit();
        const order = result.recordset[0];
        order['items'] = items.recordset;
        res.status(200).json(result.recordset[0]);
    } catch (err) {
        await transaction.rollback();
        res.status(500).send(err);
    }
});

router.post('/create', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        let columns = [];
        let values = [];

        cashColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columns.push(name);
                values.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const orderQuery = `INSERT INTO AC (${columns.join(', ')}) VALUES (${values.join(', ')})`;

        await request.query(orderQuery);

        const { items } = req.body;

        if (!items || items.length === 0) {
            throw new Error('No items provided.');
        }

        const itemColumnsArray = [];
        const itemValuesArray = [];
        const paramsArray = [];

        cashItemColumns.forEach(({ name }) => {
            if (!itemColumnsArray.includes(name)) {
                itemColumnsArray.push(name);
            }
        });

        items.forEach((item, index) => {
            const rowValues = [];
            itemColumnsArray.forEach((column) => {
                if (item[column] !== undefined) {
                    rowValues.push(`@${column}${index}`);
                    paramsArray.push({ name: `${column}${index}`, value: item[column] });
                } else {
                    rowValues.push('NULL');
                }
            });
            itemValuesArray.push(`(${rowValues.join(', ')})`);
        });

        const itemQuery = `
            INSERT INTO AC_det (${itemColumnsArray.join(', ')}) 
            VALUES ${itemValuesArray.join(', ')};
        `;

        paramsArray.forEach(({ name, value }) => {
            request.input(name, value);
        });

        await request.query(itemQuery);

        await transaction.commit();

        res.status(201).json({
            message: 'Voucher and items successfully created.',
            vtp: req.body['vtp'],
            mnth: req.body['Mnth'],
            location: req.body['Location'],
            vno: req.body['vno']
        });
    } catch (err) {
        await transaction.rollback();

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else if (err.message === 'No items provided.') {
            res.status(400).send(err.message);
        } else {
            res.status(500).send(err.message);
        }
    }
});

router.put('/update', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        const voucherNo = req.query.voucherNo;

        await transaction.begin();
        const request = new sql.Request(transaction);

        let setClause = [];
        cashColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                setClause.push(`${name} = @${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        if (setClause.length === 0) {
            return res.status(400).send('No fields to update.');
        }

        const cashVoucherQuery = `UPDATE AC SET ${setClause.join(', ')} WHERE Voucher_No = @Voucher_No`;
        request.input('Voucher_No', sql.VarChar(50), voucherNo);

        const cashVoucherResult = await request.query(cashVoucherQuery);

        if (cashVoucherResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Voucher not found.');
        }

        const { items } = req.body;

        if (items && Array.isArray(items) && items.length > 0) {
            const existingItemsQuery = `
                SELECT DVoucher_No
                FROM AC_det
                WHERE Voucher_No = @Voucher_No
            `;
            const existingItemsRequest = new sql.Request(transaction);
            existingItemsRequest.input('Voucher_No', sql.VarChar(50), voucherNo);
            const existingItemsResult = await existingItemsRequest.query(existingItemsQuery);

            const existingDVoucherNos = existingItemsResult.recordset.map(row => row.DVoucher_No);
            const itemsToUpdate = [];
            const itemsToInsert = [];
            const newDVoucherNos = new Set();

            items.forEach(item => {
                const { VTP, Mnth, Location, vno, srno } = item;
                const DVoucher_No = `${VTP}/${Mnth}/${Location}/${vno}/${srno}`;
                newDVoucherNos.add(DVoucher_No);

                if (existingDVoucherNos.includes(DVoucher_No)) {
                    itemsToUpdate.push({ ...item, DVoucher_No });
                } else {
                    itemsToInsert.push({ ...item, DVoucher_No });
                }
            });

            const itemsToDelete = existingDVoucherNos.filter(dvNo => !newDVoucherNos.has(dvNo));
            for (const item of itemsToUpdate) {
                const updateItemColumns = [];
                cashItemColumns.forEach(({ name, editable }) => {
                    if (editable && name !== 'DVoucher_No' && item[name] !== undefined) {
                        updateItemColumns.push(`${name} = @${name}`);
                    }
                });

                if (updateItemColumns.length > 0) {
                    const updateItemQuery = `
                        UPDATE AC_det
                        SET ${updateItemColumns.join(', ')}
                        WHERE DVoucher_No = @DVoucher_No
                    `;

                    const updateRequest = new sql.Request(transaction);
                    updateRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                    cashItemColumns.forEach(({ name }) => {
                        if (item[name] !== undefined) {
                            updateRequest.input(name, item[name]);
                        }
                    });

                    await updateRequest.query(updateItemQuery);
                }
            }

            for (const DVoucher_No of itemsToDelete) {
                const deleteItemQuery = `
                    DELETE FROM AC_det
                    WHERE DVoucher_No = @DVoucher_No
                `;

                const deleteRequest = new sql.Request(transaction);
                deleteRequest.input('DVoucher_No', sql.VarChar(50), DVoucher_No);

                await deleteRequest.query(deleteItemQuery);
            }

            for (const item of itemsToInsert) {
                const insertItemColumns = [];
                const insertItemValues = [];

                cashItemColumns.forEach(({ name }) => {
                    if (name !== 'DVoucher_No' && item[name] !== undefined) {
                        insertItemColumns.push(name);
                        insertItemValues.push(`@${name}`);
                    }
                });

                const insertItemQuery = `
                    INSERT INTO AC_det (${[...insertItemColumns].join(', ')})
                    VALUES (${[...insertItemValues].join(', ')})
                `;

                const insertRequest = new sql.Request(transaction);
                insertRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                cashItemColumns.forEach(({ name }) => {
                    if (item[name] !== undefined) {
                        insertRequest.input(name, item[name]);
                    }
                });

                await insertRequest.query(insertItemQuery);
            }
        }

        await transaction.commit();
        res.status(200).send('Voucher and items updated successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.delete('/delete', async (req, res) => {
    const voucherNo = req.query.voucherNo;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const deleteItemsQuery = 'DELETE FROM AC_det WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(16), voucherNo);
        const deleteItemsResult = await request.query(deleteItemsQuery);
        const deleteOrderQuery = 'DELETE FROM AC WHERE Voucher_No = @Voucher_No';
        const deleteOrderResult = await request.query(deleteOrderQuery);
        if (deleteItemsResult.rowsAffected[0] === 0 && deleteOrderResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Voucher not found or already deleted.');
        }
        await transaction.commit();
        res.status(200).send('Voucher and associated items deleted successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.get('/report', async (req, res) => {
    const pool = await getPool();
    const request = new sql.Request(pool);
    const { page = 1, pageSize = 20 } = req.query;
    const offset = (page - 1) * pageSize;
    const query = `
        WITH PagedData AS (
            SELECT 
                a.Voucher_No,
                a.adj_VoucherNo,
                a.acc_id,
                Client.title AS acc_name,
                o.GrossAmount,
                a.TotalAmt,
                o.GrossAmount - a.TotalAmt AS RemainingAmount,
                ROW_NUMBER() OVER (ORDER BY a.Voucher_No) AS RowNum,
                COUNT(*) OVER() AS TotalCount
            FROM ac_det a
            LEFT JOIN Offer O ON O.Voucher_No = a.adj_VoucherNo
            LEFT JOIN (
                SELECT C.id, C.title 
                FROM coa32 C 
                LEFT JOIN Coa3 C3 ON C3.id = C.id 
                WHERE C3.atp2_ID = 'CT'
            ) AS Client ON Client.id = a.acc_id
            WHERE a.adj_VoucherNo IS NOT NULL
        )
        SELECT *
        FROM PagedData
        WHERE RowNum BETWEEN @Offset + 1 AND @Offset + @PageSize
        ORDER BY RowNum;
        `;

    try {
        request.input("Offset", sql.Int, offset)
        request.input("PageSize", sql.Int, pageSize)

        const result = await request.query(query);
        const data = result.recordset.map(row => ({
            voucher_no: row.Voucher_No,
            quotation_no: row.adj_VoucherNo,
            client_id: row.acc_id,
            client_name: row.acc_name || 'N/A',
            gross_amount: row.GrossAmount,
            paid_amount: row.TotalAmt,
            remaining_amount: row.RemainingAmount,
            status: row.status
        }));
        const totalCount = data.length > 0 ? result.recordset[0].TotalCount : 0;

        res.status(200).json({
            page: Number(page),
            pageSize: Number(pageSize),
            totalCount,
            totalPages: Math.ceil(totalCount / pageSize),
            data
        });
    } catch (error) {
        console.error('Error fetching cash voucher report:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// New API endpoint for voucher details with payment history
router.get('/voucher-details', async (req, res) => {
    const { voucherNo } = req.query;
    const pool = await getPool();

    try {
        // Get voucher basic details
        const voucherRequest = new sql.Request(pool);
        voucherRequest.input('VoucherNo', sql.VarChar(50), voucherNo);

        const voucherQuery = `
            SELECT
                o.Voucher_No,
                o.GrossAmount,
                o.acc_id AS client_id,
                Client.title AS client_name,
                o.CreatedAt,
                o.Description
            FROM Offer o
            LEFT JOIN (
                SELECT C.id, C.title
                FROM coa32 C
                LEFT JOIN Coa3 C3 ON C3.id = C.id
                WHERE C3.atp2_ID = 'CT'
            ) AS Client ON Client.id = o.acc_id
            WHERE o.Voucher_No = @VoucherNo
        `;

        const voucherResult = await voucherRequest.query(voucherQuery);

        if (voucherResult.recordset.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }

        // Get payment history
        const paymentRequest = new sql.Request(pool);
        paymentRequest.input('VoucherNo', sql.VarChar(50), voucherNo);

        const paymentQuery = `
            SELECT
                a.Voucher_No AS payment_voucher_no,
                a.TotalAmt AS payment_amount,
                a.CreatedAt AS payment_date,
                a.Description AS payment_description,
                ac.vtp AS payment_type
            FROM ac_det a
            LEFT JOIN AC ac ON ac.Voucher_No = a.Voucher_No
            WHERE a.adj_VoucherNo = @VoucherNo
            ORDER BY a.CreatedAt DESC
        `;

        const paymentResult = await paymentRequest.query(paymentQuery);

        const voucherDetails = voucherResult.recordset[0];
        const paymentHistory = paymentResult.recordset;

        const totalPaid = paymentHistory.reduce((sum, payment) => sum + (payment.payment_amount || 0), 0);
        const remainingAmount = voucherDetails.GrossAmount - totalPaid;

        let status = 'pending';
        if (totalPaid === 0) {
            status = 'pending';
        } else if (totalPaid >= voucherDetails.GrossAmount) {
            status = 'completed';
        } else {
            status = 'partial';
        }

        res.status(200).json({
            voucher_details: {
                voucher_no: voucherDetails.Voucher_No,
                client_id: voucherDetails.client_id,
                client_name: voucherDetails.client_name || 'N/A',
                gross_amount: voucherDetails.GrossAmount,
                paid_amount: totalPaid,
                remaining_amount: remainingAmount,
                status: status,
                created_at: voucherDetails.CreatedAt,
                description: voucherDetails.Description
            },
            payment_history: paymentHistory.map(payment => ({
                payment_voucher_no: payment.payment_voucher_no,
                payment_amount: payment.payment_amount,
                payment_date: payment.payment_date,
                payment_description: payment.payment_description,
                payment_type: payment.payment_type
            }))
        });

    } catch (error) {
        console.error('Error fetching voucher details:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

module.exports = router;
