import { useState, useEffect, useCallback } from 'react';

const useBrowserNotifications = () => {
  const [permission, setPermission] = useState('default');
  const [isSupported, setIsSupported] = useState(false);
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState(null);

  // Check if browser supports notifications
  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setIsSupported(true);
      setPermission(Notification.permission);
    }
  }, []);

  // Register service worker
  useEffect(() => {
    if ('serviceWorker' in navigator && isSupported) {
      const registerServiceWorker = async () => {
        try {
          // Use single service worker file for both environments
          const swPath = '/notification-sw.js';

          // Unregister any existing service workers first
          const registrations = await navigator.serviceWorker.getRegistrations();
          for (let registration of registrations) {
            await registration.unregister();
          }

          // Register the new service worker
          const registration = await navigator.serviceWorker.register(swPath, {
            scope: '/'
          });
          console.log('ServiceWorker registration successful:', registration.scope);
          setServiceWorkerRegistration(registration);

          // Ensure the service worker is controlling the page
          if (registration.active) {
            registration.active.postMessage({ type: 'INIT' });
          }
        } catch (err) {
          console.error('ServiceWorker registration failed:', err);
        }
      };

      registerServiceWorker();
    }
  }, [isSupported]);

  // Request notification permission
  const requestPermission = useCallback(async () => {
    if (!isSupported) {
      console.log('Browser notifications not supported');
      return false;
    }

    if (permission === 'granted') {
      return true;
    }

    if (permission !== 'denied') {
      try {
        const result = await Notification.requestPermission();
        setPermission(result);
        return result === 'granted';
      } catch (error) {
        console.error('Error requesting notification permission:', error);
        return false;
      }
    }

    return false;
  }, [isSupported, permission]);

  // Show browser notification
  const showNotification = useCallback((title, options = {}) => {
    if (!isSupported || permission !== 'granted') {
      console.log('Cannot show notification: permission not granted or not supported');
      return null;
    }

    try {
      const defaultOptions = {
        icon: '/icon512_rounded.png',
        badge: '/icon512_maskable.png',
        tag: 'eco-asset-manager-notification',
        renotify: true,
        requireInteraction: false,
        silent: false,
        vibrate: [100, 50, 100],
        ...options
      };

      // Always try to use ServiceWorker for notifications in production
      if (serviceWorkerRegistration || (window.location.hostname !== 'localhost' && 'serviceWorker' in navigator)) {
        const showWithServiceWorker = async () => {
          const registration = serviceWorkerRegistration || await navigator.serviceWorker.ready;
          await registration.showNotification(title, defaultOptions);
          return true;
        };
        return showWithServiceWorker();
      }

      // Fallback to regular notification in development
      const notification = new Notification(title, defaultOptions);
      
      if (!options.requireInteraction) {
        setTimeout(() => notification.close(), 5000);
      }

      notification.onclick = () => {
        window.focus();
        if (!options.requireInteraction) {
          notification.close();
        }
        if (options.onClick) {
          options.onClick();
        }
      };

      return notification;
    } catch (error) {
      console.error('Error showing notification:', error);
      return null;
    }
  }, [isSupported, permission, serviceWorkerRegistration]);

  // Unified function to show any type of notification
  const pushNotification = useCallback((data = {}) => {
    if (!data) return null;

    const { action, data: notificationData } = data;
    
    // Define notification types and their configurations
    const notificationTypes = {
      'TIME_UPDATE': {
        getTitle: () => 'Time Update',
        getBody: (context) => {
          const { employeeName, quotationNo, newTime, reason } = context;
          return `${employeeName} has updated the time for quotation ${quotationNo} to ${newTime}. Reason: ${reason}`;
        },
        getTag: (id) => `time-update-${id}`,
        getRedirectUrl: (context) => context.quotationNo ? `/quotations/${context.quotationNo}` : '/dashboard'
      },
      'DEFAULT': {
        getTitle: () => 'New Notification',
        getBody: () => 'You have received a new notification',
        getTag: (id) => `notification-${id}`,
        getRedirectUrl: () => '/notifications'
      }
    };

    // Determine notification type
    const type = notificationData?.templateName?.includes('TIME_UPDATE') ? 'TIME_UPDATE' : 'DEFAULT';
    const config = notificationTypes[type];
    const context = notificationData?.context || {};
    const id = notificationData?.purposeId || Date.now();

    // Create notification with unified configuration
    return showNotification(config.getTitle(), {
      body: config.getBody(context),
      icon: '/icon512_rounded.png',
      badge: '/icon512_maskable.png',
      tag: config.getTag(id),
      vibrate: [100, 50, 100],
      requireInteraction: true,
      actions: [
        {
          action: 'open',
          title: 'View Details',
        },
        {
          action: 'close',
          title: 'Close',
        }
      ],
      onClick: () => {
        if (window.focus) window.focus();
        window.location.href = config.getRedirectUrl(context);
      }
    });
  }, [showNotification]);

  const testNotification = useCallback(async () => {
    if (!isSupported) {
      console.log('Browser notifications not supported');
      return false;
    }

    const isPermissionGranted = await requestPermission();
    if (!isPermissionGranted) {
      console.log('Notification permission not granted');
      return false;
    }

    return showNotification('Test Notification', {
      body: 'This is a test notification from ECO Asset Manager',
    }) !== null;
  }, [isSupported, requestPermission, showNotification]);

  // Auto-request permission on first use
  useEffect(() => {
    if (isSupported && permission === 'default') {
      // Automatically request permission when hook is first used
      requestPermission();
    }
  }, [isSupported, permission, requestPermission]);

  return {
    // State
    permission,
    isSupported,
    isGranted: permission === 'granted',

    // Functions
    requestPermission,
    showNotification,
    pushNotification,
    testNotification,
  };
};

export default useBrowserNotifications;
