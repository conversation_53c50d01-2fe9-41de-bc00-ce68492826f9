import { Input } from "@chakra-ui/react";
import { useRef } from "react";

const NumberInput = (props) => {
  const inputRef = useRef(null);

  const handleFocus = (e) => {
    e.target.select();
    
    if (props.onFocus) {
      props.onFocus(e);
    }
  };

  return (
    <Input
      className="number-input"
      ref={inputRef}
      type="number"
      onFocus={handleFocus}
      {...props}
    />
  );
};

export default NumberInput; 