import React, { useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  Box,
  Text,
} from "@chakra-ui/react";
import { useReactToPrint } from "react-to-print";
import Image from "next/image";
import logo from "@assets/assets/imgs/logo.png";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

const PrintModal = ({ isOpen, onClose, children, formName = "Form Name", showHeader = true, buttonText = 'Print', callback }) => {
  const componentRef = useRef();

  const handlePrint = useReactToPrint({
    contentRef: componentRef,
  });

  const handleGeneratePDF = async () => {
    const canvas = await html2canvas(componentRef.current, { scale: 2 });
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF("p", "mm", "a4");
    pdf.addImage(imgData, "PNG", 10, 10, 190, 0);
    const pdfBlob = pdf.output("blob");
    if (callback) {
      callback(pdfBlob);
    } else {
      handlePrint();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="full">
      <ModalOverlay />
      <ModalContent height="100vh" width="auto" overflow={"auto"}>
        <ModalHeader>Print Preview</ModalHeader>
        <ModalCloseButton />
        <ModalBody maxHeight={"90vh"} overflow={"auto"}>
          <Box
            ref={componentRef}
            minWidth="700px"
            className="print-container"
            paddingTop={12}
          >
            {showHeader &&
              <>
                <Text
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Image alt="logo" src={logo} width={150} />
                </Text>
                <Text
                  fontSize="xl"
                  fontStyle="italic"
                  textAlign="center"
                  margin={"20px 0 20px 0 !important"}
                >
                  {formName}
                </Text>
              </>
            }
            {children}
          </Box>
        </ModalBody>
        <ModalFooter gap={2}>
          <Button colorScheme="blue" onClick={handleGeneratePDF}>
            {buttonText}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PrintModal;
