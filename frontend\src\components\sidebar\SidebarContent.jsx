"use client";
import { <PERSON>, <PERSON><PERSON><PERSON>on, Divider, Flex, Text } from "@chakra-ui/react";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import logo from "@assets/assets/imgs/logo1.png";
import {
  AdminLinkItems,
  AssesserLinkItems,
  DeliveryLinkItems,
  InstallerLinkItems,
} from "./LinkItems";
import NavItem from "./NavItem";
import { useUser } from "@src/app/provider/UserContext";

const SidebarContent = ({ onClose, ...rest }) => {
  const { user } = useUser();
  const [linkItems, setLinkItems] = useState([]);

  useEffect(() => {
    if (!user || !user.roleId) return;
    switch (user.roleId) {
      case 1:
      case 2:
        setLinkItems(AdminLinkItems);
        break;
      case 3:
        setLinkItems(AssesserLinkItems);
        break;
      case 4:
        setLinkItems(DeliveryLinkItems);
        break;
      case 5:
        setLinkItems(InstallerLinkItems);
        break;
      default:
        setLinkItems([]);
    }
  }, [user]);

  return (
    <Box
      transition="3s ease"
      bg="white"
      borderRight="1px"
      borderRightColor="#071533"
      w={{ base: "full", md: 60 }}
      pos="fixed"
      h="full"
      style={{ overflow: "auto", background: "#071533" }}
      {...rest}
    >
      <Flex h="120" alignItems="center" mx="8" justifyContent="space-between">
        <Text
          style={{ marginBottom: "0" }}
          fontSize="xl"
          fontFamily="monospace"
          fontWeight="bold"
        >
          <Link href="/dashboard">
            <Image src={logo} alt="Linkers Communication" height={120} />
          </Link>
        </Text>
        <CloseButton display={{ base: "flex", md: "none" }} onClick={onClose} />
      </Flex>

      {linkItems.map((link, index) => (
        <React.Fragment key={link.name}>
          <NavItem icon={link.icon} path={link.path} subItems={link.subItems}>
            {link.name}
          </NavItem>
          {index < linkItems.length - 1 && <Divider className="sidebarHR" />}
        </React.Fragment>
      ))}
    </Box>
  );
};

export default SidebarContent;
