"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @src/components/Custom/ServerPaginatedTable */ \"(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx\");\n/* harmony import */ var _VoucherDetailsModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./VoucherDetailsModal */ \"(app-pages-browser)/./src/app/forms/cash-voucher-report/VoucherDetailsModal.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        totalCount: 0,\n        totalPages: 0\n    });\n    const [selectedVoucher, setSelectedVoucher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voucherDetails, setVoucherDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentHistory, setPaymentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalLoading, setModalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const fetchData = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n            console.log(response.data);\n            if (response.data && response.data.data) {\n                setData(response.data.data);\n                setPagination({\n                    page: response.data.page,\n                    pageSize: response.data.pageSize,\n                    totalCount: response.data.totalCount,\n                    totalPages: response.data.totalPages\n                });\n            } else {\n                setData([]);\n                setPagination({\n                    page: 1,\n                    pageSize: 20,\n                    totalCount: 0,\n                    totalPages: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch cash voucher report data\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchData();\n    }, []);\n    const fetchVoucherDetails = async (quotationNo)=>{\n        try {\n            setModalLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/voucher-details?quotationNo=\".concat(quotationNo));\n            if (response.data) {\n                setVoucherDetails(response.data.voucher_details);\n                setPaymentHistory(response.data.payment_history || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching voucher details: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch voucher details\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setModalLoading(false);\n        }\n    };\n    const handleOpenModal = async (voucher)=>{\n        setSelectedVoucher(voucher);\n        setIsModalOpen(true);\n        await fetchVoucherDetails(voucher.quotation_no);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedVoucher(null);\n        setVoucherDetails(null);\n        setPaymentHistory([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"voucher_no\"\n        },\n        {\n            header: \"Quotation No (adj)\",\n            field: \"quotation_no\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"client_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"client_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"gross_amount\",\n            render: (item)=>formatCurrency(item.gross_amount)\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"paid_amount\",\n            render: (item)=>formatCurrency(item.paid_amount)\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"remaining_amount\",\n            render: (item)=>formatCurrency(item.remaining_amount)\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        },\n        {\n            header: \"Actions\",\n            field: \"actions\",\n            render: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                    colorScheme: \"blue\",\n                    size: \"sm\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleOpenModal(item);\n                    },\n                    children: \"View Details\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 148,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    const getRowCursor = ()=>\"pointer\";\n    const handleRowClick = (item)=>{\n        handleOpenModal(item);\n    };\n    const handlePageChange = (newPage)=>{\n        fetchData(newPage, pagination.pageSize);\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        fetchData(1, newPageSize);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 179,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Cash Voucher Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            data: data,\n                                            columns: columns,\n                                            pagination: pagination,\n                                            onPageChange: handlePageChange,\n                                            onPageSizeChange: handlePageSizeChange,\n                                            onRowClick: handleRowClick,\n                                            getRowCursor: getRowCursor,\n                                            getBadgeColor: getStatusColor,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                lineNumber: 185,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 183,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 182,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VoucherDetailsModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: isModalOpen,\n                    onClose: handleCloseModal,\n                    selectedVoucher: selectedVoucher,\n                    voucherDetails: voucherDetails,\n                    paymentHistory: paymentHistory,\n                    modalLoading: modalLoading\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"to0F4F3T6vvp+5aeff+YHPpyvK4=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ })

});