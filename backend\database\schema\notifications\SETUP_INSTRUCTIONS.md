# Notification System Setup Instructions

## Overview

The notification system has been split into multiple scripts to handle data type compatibility issues safely. This approach ensures that the foreign key constraints match the actual data types in your existing database.

## Setup Process

### Step 1: Run the Safe Table Creation Script

First, run the safe table creation script that automatically detects your existing data types:

```sql
-- Execute this script first
-- File: setup-notifications-safe.sql
```

This script will:
- Check the data types of your existing `users.id`, `EmployeeDetails.ID`, and `User_Role.RoleID` columns
- Create notification tables with matching data types
- Display the detected data types for verification

### Step 2: Run the Constraints and Indexes Script

After the tables are created successfully, run the constraints script:

```sql
-- Execute this script second
-- File: setup-notifications-constraints.sql
```

This script will:
- Add all foreign key constraints
- Create performance indexes
- Add unique constraints where needed

### Step 3: Run the Initial Data Script

Finally, run the data insertion script:

```sql
-- Execute this script third
-- File: setup-notifications-data.sql
```

This script will:
- Insert notification types (TIME_UPDATE, FORM_SUBMISSION, etc.)
- Insert notification templates for time updates
- Create the notification details view
- Add reason columns to ClientPurpose table

## Verification

After running all three scripts, verify the setup:

### 1. Check Tables Created

```sql
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'Notification%'
ORDER BY TABLE_NAME;
```

Expected tables:
- NotificationRecipients
- Notifications
- NotificationSettings
- NotificationTemplates
- NotificationTypes

### 2. Check Foreign Keys

```sql
SELECT 
    fk.name AS ForeignKeyName,
    tp.name AS ParentTable,
    cp.name AS ParentColumn,
    tr.name AS ReferencedTable,
    cr.name AS ReferencedColumn
FROM sys.foreign_keys fk
INNER JOIN sys.tables tp ON fk.parent_object_id = tp.object_id
INNER JOIN sys.tables tr ON fk.referenced_object_id = tr.object_id
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.columns cp ON fkc.parent_column_id = cp.column_id AND fkc.parent_object_id = cp.object_id
INNER JOIN sys.columns cr ON fkc.referenced_column_id = cr.column_id AND fkc.referenced_object_id = cr.object_id
WHERE tp.name LIKE 'Notification%'
ORDER BY tp.name, fk.name;
```

### 3. Check Initial Data

```sql
-- Check notification types
SELECT * FROM NotificationTypes;

-- Check notification templates
SELECT nt.TypeName, ntp.TemplateName, ntp.Priority 
FROM NotificationTemplates ntp
INNER JOIN NotificationTypes nt ON ntp.TypeID = nt.ID
ORDER BY nt.TypeName, ntp.TemplateName;

-- Check view
SELECT TOP 1 * FROM vw_NotificationDetails;
```

### 4. Check ClientPurpose Columns

```sql
SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ClientPurpose' 
AND COLUMN_NAME LIKE '%TimeUpdateReason';
```

## Troubleshooting

### Issue: Foreign Key Constraint Errors

If you still get foreign key constraint errors:

1. Check the actual data types:
```sql
SELECT 
    TABLE_NAME, 
    COLUMN_NAME, 
    DATA_TYPE, 
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME IN ('users', 'EmployeeDetails', 'User_Role')
AND COLUMN_NAME IN ('id', 'ID', 'RoleID');
```

2. Manually adjust the data types in the safe script if needed.

### Issue: Tables Already Exist

If tables already exist from a previous attempt:

```sql
-- Drop tables in correct order (due to foreign keys)
DROP VIEW IF EXISTS vw_NotificationDetails;
DROP TABLE IF EXISTS NotificationSettings;
DROP TABLE IF EXISTS NotificationRecipients;
DROP TABLE IF EXISTS Notifications;
DROP TABLE IF EXISTS NotificationTemplates;
DROP TABLE IF EXISTS NotificationTypes;
```

### Issue: Permission Errors

Ensure your database user has the following permissions:
- CREATE TABLE
- ALTER TABLE
- CREATE INDEX
- CREATE VIEW
- INSERT, UPDATE, DELETE on created tables

## Testing the Setup

After successful setup, test the notification system:

### 1. Test Notification Creation

```sql
-- This should work without errors
EXEC sp_executesql N'
INSERT INTO Notifications (TypeID, Title, Message, Priority, SenderUserID, CreatedAt)
VALUES (
    (SELECT ID FROM NotificationTypes WHERE TypeName = ''TIME_UPDATE''),
    ''Test Notification'',
    ''This is a test notification'',
    2,
    (SELECT TOP 1 id FROM users WHERE RoleID = 1),
    GETDATE()
);';
```

### 2. Test View Query

```sql
-- This should return the test notification
SELECT * FROM vw_NotificationDetails 
WHERE Title = 'Test Notification';
```

### 3. Clean Up Test Data

```sql
DELETE FROM Notifications WHERE Title = 'Test Notification';
```

## Next Steps

After successful database setup:

1. **Backend**: The notification service and routes are already implemented
2. **Frontend**: The notification components are already integrated
3. **Testing**: Test the time update functionality in the application

## Support

If you encounter issues:

1. Check the error messages carefully
2. Verify your database permissions
3. Ensure all prerequisite tables (users, EmployeeDetails, User_Role) exist
4. Check the data types match between tables

The notification system is designed to be robust and handle various database configurations, but the exact data types must match for foreign key constraints to work properly.
