import { IconType } from 'react-icons';
import { FiHome, FiCompass, FiUploadCloud, FiUserPlus, FiBox, FiTrendingUp, FiStar, FiSettings, FiUsers } from 'react-icons/fi';
import { BiStore } from 'react-icons/bi';
import { FaRegMoneyBillAlt } from 'react-icons/fa';
import { TbDashboard, TbBrandPaypal } from 'react-icons/tb';
import { TfiMenuAlt } from 'react-icons/tfi';
import { IoMdContact } from 'react-icons/io';

interface LinkItemProps {
    name: string;
    icon: IconType;
    path?: string;
    subItems?: Array<LinkItemProps>; // For dropdown items
    href?: string;
    children?: Array<{
        name: string;
        href: string;
    }>;
}

export const AdminLinkItems: Array<LinkItemProps> = [
    // {
    //   name: 'User Control',
    //   icon: FiHome,
    //   subItems: [
    //     { name: 'Log Out', icon: FiLogOut, path: '/forms/login' },
    //     { name: 'Change Password', icon: MdPassword, path: '/forms/ChangePassword' },
    //     { name: 'Manage Users', icon: FiUsers, path: '/forms/ManageUsers' },
    //     { name: 'Assign User Rights', icon: MdAssignmentTurnedIn, path: '/forms/AssignUserRights' },
    //     { name: 'Usage History', icon: FaHistory, path: '/forms/UsageHistory' },
    //     { name: 'Backup Data', icon: BsDatabase, path: '/forms/BackupData' },
    //   ]
    // },
    // {
    //   name: 'Main Definitions',
    //   icon: FiUploadCloud,
    //   subItems: [
    //     { name: 'Main Account Type', icon: FiFileText, path: '/forms/MainAccountType' },
    //     { name: 'Main Voucher Type', icon: FiFileText, path: '/forms/MainVoucherType' },
    //     { name: 'Define Godowns', icon: GrPowerForceShutdown, path: '/forms/DefineGodowns' },
    //     { name: 'Define Areas', icon: GrPowerForceShutdown, path: '/forms/DefineAreas' },
    //     { name: 'Define Machines', icon: GrPowerForceShutdown, path: '/forms/DefineMachines' },
    //     { name: 'Define Services', icon: GrPowerForceShutdown, path: '/forms/DefineServices' },
    //     {
    //       name: "Chart Of Accounts",
    //       icon: BsDashCircleFill,
    //       subItems: [
    //         { name: "Multi-Level Chart Of Account", icon: GrMultiple, path: '/forms/MultiLevelCOA' },
    //         {
    //           name: "Stock Items",
    //           icon: RiAlignItemBottomFill,
    //           subItems: [
    //             {
    //               name: "Raw Material",
    //               icon: DiMaterializecss,
    //               subItems: [
    //                 { name: "Categories", icon: BiCategory, path: '/forms/RawMaterial/Category' },
    //                 { name: "Types", icon: FiType, path: '/forms/RawMaterial/Types' },
    //                 { name: "Stock Items", icon: SiStockx, path: '/forms/RawMaterial/StockItems' },
    //               ],
    //             },
    //             {
    //               name: "Packing Material",
    //               icon: DiMaterializecss,
    //               subItems: [
    //                 { name: "Categories", icon: BiCategory, path: '/forms/PackingMaterial/Category' },
    //                 { name: "Types", icon: FiType, path: '/forms/PackingMaterial/Types' },
    //                 { name: "Stock Items", icon: SiStockx, path: '/forms/PackingMaterial/StockItems' },
    //               ],
    //             },
    //             {
    //               name: "Finished Goods",
    //               icon: DiMaterializecss,
    //               subItems: [
    //                 { name: "Categories", icon: BiCategory, path: '/forms/FinishedGoods/Category' },
    //                 { name: "Types", icon: FiType, path: '/forms/FinishedGoods/Types' },
    //                 { name: "Stock Items", icon: SiStockx, path: '/forms/FinishedGoods/StockItems' },
    //               ],
    //             },
    //             { name: "Edit Opening Balance", icon: FaEdit, path: '/forms/EditOpeningBalance' },
    //             { name: "Transfer Transactions", icon: GrTransaction, path: '/forms/TransferTransactions' },
    //           ],
    //         },
    //         { name: "Location Wise Opening Balance", icon: ImLocation2, path: '/forms/LocationWiseOpeningBalance' },
    //         { name: "User Wise Accounts", icon: FaUser, path: '/forms/UserWiseAccounts' },
    //       ],
    //     },
    //     { name: "Define Leave/Holidays", icon: FcLeave, path: '/forms/DefineLeave' },
    //     { name: "Re-Post Transactions", icon: AiOutlineTransaction, path: '/forms/RePostTransactions' },
    //     { name: "Change/Close Date", icon: MdDateRange, path: '/forms/ChangeDate' },
    //     { name: "Global Settings", icon: RiGlobalLine, path: '/forms/GlobalSettings' },
    //     { name: "General Defaults", icon: SiGeneralelectric, path: '/forms/GeneralDefaults' },
    //     { name: "Digital Library", icon: BiLibrary, path: '/forms/DigitalLibrary' },
    //     {
    //       name: "Employee Accounts",
    //       icon: MdAccountCircle,
    //       subItems: [
    //         { name: "Define Employee Designations", icon: ImLocation2, path: '/forms/DefineEmployeeDesignations' },
    //         { name: "Define Employee Departments", icon: BsFillBuildingsFill, path: '/forms/DefineEmployeeDepartments' },
    //         { name: "Define Employee Sections", icon: FaBuilding, path: '/forms/DefineEmployeeSections' },
    //         { name: "Define Employee Units", icon: BsUnity, path: '/forms/DefineEmployeeUnits' },
    //         { name: "Define Employee Shifts", icon: BsShiftFill, path: '/forms/DefineEmployeeShifts' },
    //         { name: "User Wise Sections", icon: FaUser, path: '/forms/UserWiseSections' },
    //         { name: "Set Salary Structure", icon: PiTreeStructureFill, path: '/forms/SetSalaryStructure' },
    //         { name: "Approved Strength", icon: TbCircleCheckFilled, path: '/forms/ApprovedStrength' },
    //         { name: "Define Employees", icon: FaUser, path: '/forms/DefineEmployees' },
    //         { name: "Link Employee Codes", icon: FaLink, path: '/forms/LinkEmployeeCodes' },
    //         { name: "Import Employee Codes", icon: BiImport, path: '/forms/ImportEmployeeCodes' },
    //         { name: "Change Employee Designations", icon: ImLocation2, path: '/forms/ChangeEmployeeDesignations' },
    //         { name: "Change Employee Departments", icon: BsFillBuildingsFill, path: '/forms/ChangeEmployeeDepartments' },
    //         { name: "Change Employee Sections", icon: FaBuilding, path: '/forms/ChangeEmployeeSections' },
    //         { name: "Change Employee Units", icon: BsUnity, path: '/forms/ChangeEmployeeUnits' },
    //         { name: "Change Employee Shifts", icon: BsShiftFill, path: '/forms/ChangeEmployeeShifts' },
    //         { name: "Change Employee Rest Days", icon: MdHolidayVillage, path: '/forms/ChangeEmployeeShiftsRestDays' },
    //         { name: "Employee Salary Increment", icon: FaRupeeSign, path: '/forms/EmployeeSalaryIncrement' },
    //         { name: "Update Employee Resignation Date", icon: GiExitDoor, path: '/forms/UpdateEmployeeResignationDate' },
    //       ]
    //     }
    //   ]
    // },
    // {
    //   name: 'Payroll',
    //   icon: TbBrandPaypal,
    //   subItems: [
    //     { name: "Standards - Tax Deduction Policy", icon: MdPolicy, path: '/forms/StandardsTaxDeductionPolicy' },
    //     { name: "Appointment Letter", icon: PiVideoConferenceFill, path: '/forms/AppointmentLetter' },
    //   ]
    // },
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
    {
        name: 'Business Leads',
        icon: IoMdContact,
        path: '/forms/business-leads'
    },
    {
        name: 'Tasks',
        icon: FiUsers,
        path: '/tasks'
    },
    {
        name: 'Main Definitions',
        icon: FiUploadCloud,
        subItems: [
            { name: 'Define Employees', icon: FiUserPlus, path: '/forms/define-employees' },
            { name: 'Define Products', icon: FiBox, path: '/forms/define-products' },
        ]
    },
    {
        name: 'Financials',
        icon: TbBrandPaypal,
        subItems: [
            { name: 'Cash Receipt', icon: FiHome, path: '/forms/cash-receipt-voucher' },
            // { name: 'Cash Payment', icon: FiHome, path: '/forms/cash-payment-voucher' },
            { name: 'Bank Receipt', icon: FiHome, path: '/forms/bank-receipt-voucher' },
            // { name: 'Bank Payment', icon: FiHome, path: '/forms/bank-payment-voucher' },
            // { name: "Standards - Tax Deduction Policy", icon: MdPolicy, path: '/forms/StandardsTaxDeductionPolicy' },
            // { name: "Appointment Letter", icon: PiVideoConferenceFill, path: '/forms/AppointmentLetter' },
        ]
    },
    // {
    //     name: 'Sales',
    //     icon: FaRegMoneyBillAlt,
    //     subItems: [
    //         // { name: 'Registration Form', icon: TfiMenuAlt, path: '/forms/registration-form' },
    //         { name: 'Client Lead', icon: TfiMenuAlt, path: '/forms/client-lead' },
    //         // { name: 'Site Assessment Follow-up', icon: TfiMenuAlt, path: '/forms/recovery-follow-up' },
    //         { name: 'Client Order', icon: FiCompass, path: '/forms/client-order' },
    //         { name: 'Delivery Challan', icon: FiCompass, path: '/forms/delivery-challan' },
    //         // { name: 'Sales Tax Invoice', icon: TfiMenuAlt, path: '/forms/sales-tax-invoice' },
    //     ]
    // },
    {
        name: 'Reports',
        icon: FaRegMoneyBillAlt,
        subItems: [
            { name: 'Assessors Report', icon: TfiMenuAlt, path: '/forms/assessors-report' },
            { name: 'Audit Report', icon: TfiMenuAlt, path: '/forms/audit-report' },
            { name: 'Delivery Report', icon: TfiMenuAlt, path: '/forms/delivery-report' },
            { name: 'Installer Report', icon: TfiMenuAlt, path: '/forms/installer-report' },
            { name: 'Cash Voucher Report', icon: TfiMenuAlt, path: '/forms/cash-voucher-report' },
        ]
    },
    // {
    //   name: 'Residents',
    //   icon: BiStore,
    //   subItems: [
    //     { name: 'Admission Form', icon: TfiMenuAlt, path: '/forms/goods-receipt-note' },
    //   ]
    // },
    // {
    //     name: 'Stores',
    //     icon: BiStore,
    //     subItems: [
    //         { name: 'Goods Receipt Note', icon: TfiMenuAlt, path: '/forms/goods-receipt-note' },
    //         // { name: 'Store Issuance Note', icon: TfiMenuAlt, path: '/forms/goods-receipt-note' },
    //     ]
    // },
];

export const AssesserLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
    {
        name: 'Business Leads',
        icon: IoMdContact,
        path: '/forms/business-leads'
    },
];

export const InstallerLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
];

export const DeliveryLinkItems: Array<LinkItemProps> = [
    {
        name: 'Dashboard',
        icon: TbDashboard,
        path: '/dashboard',
    },
];
