import React, { useState, useRef, useEffect } from 'react';
import { Box, Button, Text, useToast, Link } from '@chakra-ui/react';
import { HiUpload } from "react-icons/hi";
import { BiTrash } from "react-icons/bi";

const MultipleAudioUploader = ({ initial = [], onChange, disabled = false }) => {
    const [audios, setAudios] = useState([]);
    const [newAudios, setNewAudios] = useState([]);
    const fileInputRef = useRef(null);
    const toast = useToast();

    useEffect(() => {
        if (initial.length > 0) {
            const formattedAudios = initial.map(audio => ({
                file: { name: audio.FileName },
                preview: `data:audio/mp3;base64,${Buffer.from(audio.FileData.data).toString('base64')}`,
                timestamp: new Date(audio.Timestamp).toLocaleString(),
                location: { lat: audio.Latitude, lng: audio.Longitude },
                googleMapsLink: audio.GoogleMapsLink,
            }));
            setAudios(formattedAudios);
        }
    }, [initial]);
    
    useEffect(() => {
        if (newAudios.length > 0) {
            setAudios(prev => {
                const updatedAudios = [...prev, ...newAudios];
                onChange?.(updatedAudios);
                return updatedAudios;
            });
            setNewAudios([]);
        }
    }, [newAudios, onChange]);

    const handleAudioChange = (e) => {
        const files = Array.from(e.target.files);
        files.forEach(file => {
            const fileExists = audios.some(audio => audio.file.name === file.name);
            if (fileExists) {
                toast({
                    title: "Audio already uploaded",
                    status: "warning",
                    variant: "left-accent",
                    position: "top-right",
                    isClosable: true,
                });
                return;
            }

            const reader = new FileReader();
            const timestamp = new Date().toLocaleString();
            reader.onloadend = () => {
                navigator.geolocation.getCurrentPosition((position) => {
                    const location = `Lat: ${position.coords.latitude}, Lon: ${position.coords.longitude}`;
                    const googleMapsLink = `https://www.google.com/maps?q=${position.coords.latitude},${position.coords.longitude}`;
                    setNewAudios(prev => [...prev, { file, preview: reader.result, timestamp, location: { lat: position.coords.latitude, lng: position.coords.longitude }, googleMapsLink }]);
                }, (error) => {
                    console.error("Error getting location:", error);
                    toast({
                        title: "Location permission denied. Audio not uploaded.",
                        status: "error",
                        variant: "left-accent",
                        position: "top-right",
                        isClosable: true,
                    });
                });
            };
            reader.readAsDataURL(file);
        });
    };

    const handleRemove = (index) => {
        const updatedAudios = audios.filter((_, i) => i !== index);
        setAudios(updatedAudios);
        onChange?.(updatedAudios);
    };

    const handleUploadClick = () => {
        fileInputRef.current?.click();
    };

    return (
        <Box width="100%" height="100%">
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleAudioChange}
                accept="audio/*"
                style={{ display: 'none' }}
                disabled={disabled}
                multiple
            />

            <Button
                width="100%"
                colorScheme="blue"
                onClick={handleUploadClick}
                disabled={disabled}
            >
                <HiUpload size={20} style={{ marginRight: "5px" }} />
                Upload Audios
            </Button>

            <Box mt={4}>
                {audios.map((audio, index) => (
                    <Box key={index} mb={4  }>
                        <Box>
                            <Box display="flex" alignItems="center" mb={2} justifyContent="space-between">
                                <Box>
                                    <Text><strong>File Name:</strong> {audio.file.name}</Text>
                                    <Text><strong>Timestamp:</strong> {audio.timestamp}</Text>
                                    <Text><strong>Location: &nbsp;</strong>
                                        {audio.googleMapsLink && (
                                            <Link href={audio.googleMapsLink} isExternal color="blue.500">
                                                View on Google Maps
                                            </Link>
                                        )}
                                    </Text>
                                </Box>
                                <Button
                                    colorScheme="blue"
                                    onClick={() => handleRemove(index)}
                                    disabled={disabled}
                                    size="sm"
                                    ml={2}
                                >
                                    <BiTrash />
                                </Button>
                            </Box>
                            <audio controls src={audio.preview} style={{ marginTop: '10px', width: '100%' }} />
                        </Box>
                    </Box>
                ))}
            </Box>
        </Box>
    );
};

export default MultipleAudioUploader;
