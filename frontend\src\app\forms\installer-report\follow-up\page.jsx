"use client";
import React, { Suspense, useEffect, useState } from "react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import AanzaDataTable from "@components/Custom/AanzaDataTable/AanzaDataTable";
import axiosInstance from "../../../axios";
import { Button, Select, useToast } from "@chakra-ui/react";
import { Box, FormControl, FormLabel, Input } from "@chakra-ui/react";
import { formatDate, validateObjectFields } from "@utils/functions";
import {
  InstallerFollowUpHeaders,
  InstallerFollowUpSectionFormFields,
  createInstallerFollowUpInitialFormData,
  createInstallerFollowUpEmptyTableRow,
  InstallerFollowUpRequiredFields,
} from "@utils/constant";
import { useSearchParams } from "next/navigation";
import Loader from "@components/Loader/Loader";
import MultipleImageUploader from "@components/Custom/MultipleImageUploader/MultipleImageUploader";
import MultipleAudioUploader from "@components/Custom/MultipleAudioUploader/MultipleAudioUploader";

const RecoveryFollowUp = () => {
  const toast = useToast();
  const searchParams = useSearchParams();
  const [clients, setClients] = useState([]);
  const isDisabled = true;
  const [isEdit, setIsEdit] = useState(false);
  const [isFollowUpExist, setIsFollowUpExist] = useState(false);
  const [tableData, setTableData] = useState(
    createInstallerFollowUpEmptyTableRow()
  );
  const [formData, setFormData] = useState(
    createInstallerFollowUpInitialFormData()
  );
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setDialogOpen] = useState(false);
  const [images, setImages] = useState([]);
  const [audios, setAudios] = useState([]);
  const [loadedImages, setLoadedImages] = useState([]);
  const [loadedAudios, setLoadedAudios] = useState([]);

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;
      setFormData((prev) => ({ ...prev, [name]: value }));
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const handleImagesChange = (newImages) => {
    setImages(newImages);
  };

  const handleAudiosChange = (newAudios) => {
    setAudios(newAudios);
  };

  const transformData = (
    orderData = {},
    itemsArray,
    isNavigationdata = false
  ) => {
    if (isNavigationdata) {
      return itemsArray.map((item) => {
        return {
          qty: item?.Qty,
          rate: item?.Rate || item?.GrossRate,
          total: item.Total,
          Item_Title: item.itemTitle,
          unit: item.itemUnit,
          Installation_Charges: item.InstallationCharges,
          Item_ID: item.Item_ID || item.item_id,
          remarks: item.ItemName || item.details,
          DeliveredItems: item.DeliveredItems ? Number(item.DeliveredItems) : 0,
          DeliveredRemaining: item.DeliveredRemainingItems ?? 0,
          InstalledItems: item.InstalledItems ? Number(item.InstalledItems) : 0,
          InstalledRemaining: item.InstalledRemainingItems ?? 0,
        };
      });
    } else {
      return itemsArray.map((item, index) => {
        return {
          Dated: orderData.Dated,
          VTP: orderData.VTP,
          Mnth: orderData.Mnth,
          Location: orderData.Location,
          vno: orderData.vno,
          srno: index + 1,
          DeliveredItems: item.delivered ? Number(item.delivered) : 0,
          DeliveredRemainingItems: item.remaining ? Number(item.remaining) : 0,
          InstalledItems: item.InstalledItems ? Number(item.InstalledItems) : 0,
          InstalledRemainingItems: item.InstalledRemaining ? Number(item.InstalledRemaining) : 0
        };
      });
    }
  };

  const cellRender = (
    value,
    key,
    rowIndex,
    colIndex,
    cellData,
    handleInputChange
  ) => {
    return (
      cellData.type !== 'Select' ? <Input
        width={cellData.width}
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        size="sm"
        type={cellData.type}
        isReadOnly={cellData.isReadOnly}
        disabled={isDisabled}
      /> : <Select
        value={value}
        width={cellData.width}
        onChange={(e) => handleInputChange(e.target.value)}
        placeholder="Select"
        disabled={isDisabled}
        isReadOnly={cellData.isReadOnly}
      >
        {[...Array(tableData[rowIndex].qty)].map((_, i) => (
          <option key={i + 1} value={i + 1}>
            {i + 1}
          </option>
        ))}
      </Select>
    );
  };

  const getData = async (url) => {
    try {
      const response = await axiosInstance.get(url);
      return response.data;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const calculation = (header, value, rowIndex) => {
    setTableData((prevData) => {
      return prevData.map((r, i) => {
        if (i === rowIndex) {
          const updatedRow = {
            ...r,
            [header.key]: value,
          };
          if (header.key == 'DeliveredItems') {
            updatedRow.DeliveredRemaining = updatedRow.qty - updatedRow.DeliveredItems;
          }
          if (header.key == 'InstalledItems') {
            updatedRow.InstalledRemaining = updatedRow.qty - updatedRow.InstalledItems;
          }
          return updatedRow;
        }
        return r;
      });
    });
  };

  // Toolbar funtions ends here
  const loadInitialData = async (voucher_No) => {
    setLoading(true);
    try {
      const clientsData = await getData("getRecords/clients");
      const { data: voucherDetails } = await axiosInstance.post("offer/getbyvoucher", { voucherNo: voucher_No });
      const { data: clientDetail } = await getData("client/" + voucherDetails.client_id);
      const fileResponse = await axiosInstance.get("offer/get-files", {
        params: {
          refVoucherNo: voucher_No,
        }
      });

      // Process files by type
      const allFiles = fileResponse.data;
      const images = allFiles.filter(file => file.Type === 'image' && file.FileName !== 'signature.png');
      const audios = allFiles.filter(file => file.Type === 'audio');

      setLoadedAudios(audios);
      setLoadedImages(images);
      const form = {
        date: voucherDetails?.Dated
          ? formatDate(new Date(voucherDetails?.Dated), true)
          : null,
        VTP: voucherDetails?.VTP,
        mnth: voucherDetails?.Mnth,
        location: voucherDetails?.Location,
        vno: voucherDetails?.vno,
        voucherNo: voucherDetails?.Voucher_No,
        clientId: voucherDetails?.Client_ID ?? "",
        clientTitle: voucherDetails?.ClientName ?? "",
        remarks: voucherDetails?.StartingComments ?? 0,
      };
      const { items: voucherItems } = voucherDetails;
      if (voucherItems.length > 0) {
        setTableData(() => transformData([], voucherItems, true));
      }

      if (
        Object.keys(voucherDetails).length !== 0 &&
        Array.isArray(voucherDetails.items) &&
        voucherDetails.items.length > 0
      ) {
        if (!voucherDetails.items.some(item => item.InstalledRemainingItems !== 0)) {
          setIsFollowUpExist(Object.keys(voucherDetails).length !== 0);
        } else {
          setIsEdit(true)
        }
      }

      setClients(clientsData);
      setFormData((prev) => ({
        ...prev, ...form, ...{
          clientId: clientDetail?.id ?? "",
          clientTitle: clientDetail?.title ?? "",
          landlineNo: clientDetail?.tel ?? "",
          phoneNo: clientDetail?.Mobile ?? "",
          address: clientDetail?.add1 ?? "",
          email: clientDetail?.email ?? "",
          contact: voucherDetails?.contactPerson ?? "",
          comments: voucherDetails?.StartingComments ?? ""
        }
      }));
    } catch (error) {
      console.error("Error fetching voucher number:", error);
    } finally {
      setLoading(false);
    }
  }
  useEffect(() => {
    const voucher_No = searchParams.get("voucher_No");
    if (voucher_No) {
      loadInitialData(voucher_No);
    }
  }, [searchParams]);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div>
              <div>
                <div className="page-inner">
                  <div className="row">
                    <div className="bgWhite">
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "20px",
                          fontWeight: "bold",
                          padding: "10px",
                        }}
                      >
                        Site Assessment Follow-Up
                      </h1>
                    </div>
                  </div>
                  <div
                    className="row"
                    style={{ gap: "10px", paddingTop: "8px" }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "20px",
                          fontWeight: "bold",
                        }}
                      >
                        SOP&apos;s
                      </h1>
                      <div className="question">
                        <p>1. Is the property more than 2 years old ?</p>
                        <Select name={"question1"} placeholder="Select">
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                        </Select>
                      </div>
                      <div className="question">
                        <p>
                          2. Is the combined yearly income of the household
                          equal to or less than 180,000 ?
                        </p>
                        <Select name={"question1"} placeholder="Select">
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                        </Select>
                      </div>
                      <div className="question">
                        <p>
                          3. Are you ready to decommission the ducted gas heated
                          system ?
                        </p>
                        <Select name={"question1"} placeholder="Select">
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                        </Select>
                      </div>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexWrap: {
                            base: "wrap",
                            sm: "wrap",
                            md: "wrap",
                            lg: "nowrap",
                          },
                          gap: "10px",
                        }}
                      >
                        <Box
                          sx={{
                            width: {
                              base: "100%",
                              sm: "100%",
                              md: "100%",
                              lg: "100%",
                            },
                          }}
                        >
                          <Box
                            className=""
                            sx={{
                              display: "grid",
                              gridTemplateColumns: {
                                base: "repeat(auto-fit,minmax(200px,1fr)) !important",
                                sm: "repeat(auto-fit,minmax(200px,1fr)) !important",
                                md: "repeat(auto-fit,minmax(200px,1fr)) !important",
                                lg: "repeat(auto-fit,minmax(500px,1fr))",
                              },
                              gap: "5px",
                            }}
                          >
                            {InstallerFollowUpSectionFormFields[0].map(
                              (field) => (
                                <FormControl
                                  key={field.id}
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    flexWrap: {
                                      base: "wrap",
                                      sm: "wrap",
                                      md: "wrap",
                                      lg: "nowrap",
                                    },
                                    flexDirection: {
                                      base: "column",
                                      sm: "column",
                                      md: "row",
                                    },
                                    marginTop: "10px",
                                  }}
                                  isRequired={field.isRequired}
                                >
                                  <FormLabel
                                    htmlFor={field.id}
                                    sx={{
                                      marginBottom: "0",
                                      width: {
                                        base: "100%",
                                        sm: "100%",
                                        md: "100%",
                                        lg: "27%",
                                      },
                                    }}
                                  >
                                    {field.label}
                                  </FormLabel>
                                  {field.type === "date" ? (
                                    <Input
                                      id={field.id}
                                      name={field.name}
                                      type={field.type}
                                      value={formData[field.value]}
                                      onChange={handleInputChange}
                                      placeholder={field.placeholder}
                                      _placeholder={{ color: "gray.500" }}
                                      readOnly={field.isReadOnly}
                                      // min={field.minDate}
                                      // max={field.maxDate}
                                      disabled={isEdit || isDisabled}
                                      sx={{
                                        marginLeft: {
                                          base: "0",
                                          sm: "0",
                                          lg: "4px",
                                        },
                                        width: {
                                          base: "100%",
                                          sm: "100%",
                                          md: "100%",
                                          lg: "80%",
                                        },
                                      }}
                                    />
                                  ) : (
                                    <Input
                                      id={field.id}
                                      name={field.name}
                                      type={field.type}
                                      value={formData[field.value]}
                                      onChange={handleInputChange}
                                      placeholder={field.placeholder}
                                      _placeholder={{ color: "gray.500" }}
                                      readOnly={field.isReadOnly}
                                      disabled={
                                        field.name === "voucherNo"
                                          ? isEdit || isDisabled
                                          : isDisabled
                                      }
                                      sx={{
                                        marginLeft: {
                                          base: "0",
                                          sm: "0",
                                          lg: "4px",
                                        },
                                        width: {
                                          base: "100%",
                                          sm: "100%",
                                          md: "100%",
                                          lg: "80%",
                                        },
                                      }}
                                    />
                                  )}
                                </FormControl>
                              )
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <Box
                        className="pt-4 pb-4"
                        sx={{
                          display: "grid",
                          gridTemplateColumns: {
                            base: "repeat(auto-fit,minmax(200,1fr))",
                            sm: "repeat(auto-fit,minmax(200,1fr))",
                            md: "repeat(auto-fit,minmax(200,1fr))",
                            lg: "repeat(auto-fit,minmax(500px,1fr))",
                          },
                          gap: "5px",
                        }}
                      >
                        {InstallerFollowUpSectionFormFields[1].map(
                          (control, index) => (
                            <FormControl
                              key={index}
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                flexDirection: {
                                  base: "column",
                                  sm: "column",
                                  lg: "row",
                                },
                                marginTop: "10px",
                                flexWrap: "nowrap",
                              }}
                              isRequired={control.isRequired}
                            >
                              <FormLabel
                                htmlFor={control.fields[0].name}
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "20%",
                                  },
                                }}
                              >
                                {control.label}
                              </FormLabel>
                              <Box
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "80%",
                                  },
                                  display: "flex",
                                  gap: control.fields.length > 1 ? "10px" : "0",
                                }}
                              >
                                {control.fields.map((field, fieldIndex) =>
                                  field.component === "ComboBox" ? (
                                    <ComboBox
                                      key={fieldIndex}
                                      target={true}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      inputWidths={field.inputWidths}
                                      buttonWidth={field.buttonWidth}
                                      styleButton={{
                                        padding: "3px !important",
                                      }}
                                      tableData={clients}
                                      tableHeaders={field.tableHeaders}
                                      nameFields={field.nameFields}
                                      placeholders={field.placeholders}
                                      keys={field.keys}
                                      form={formData}
                                      isDisabled={isDisabled}
                                    />
                                  ) : (
                                    <Input
                                      key={fieldIndex}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      value={formData[field.value]}
                                      _placeholder={field._placeholder}
                                      type={field.type}
                                      style={{ width: field.inputWidth }}
                                      disabled={isDisabled}
                                    />
                                  )
                                )}
                              </Box>
                            </FormControl>
                          )
                        )}
                      </Box>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(50% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                          height: "100%",
                        }}
                        isRequired="true"
                      >
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "100%" },
                            display: "flex",
                            height: "100%",
                          }}
                        >
                          <MultipleImageUploader
                            initial={loadedImages}
                            onChange={handleImagesChange}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(50% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                          height: "100%",
                        }}
                        isRequired="true"
                      >
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "100%" },
                            display: "flex",
                            height: "100%",
                          }}
                        >
                          <MultipleAudioUploader
                            initial={loadedAudios}
                            onChange={handleAudiosChange}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                  </div>
                  <Box
                    className="row"
                    sx={{
                      gap: "10px",
                      paddingTop: "10px !important",
                      display: "flex",
                      flexDirection: {
                        base: "column-reverse",
                        sm: "column-reverse",
                        md: "column-reverse",
                        lg: "row",
                      },
                    }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "100% !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <div
                        style={{ padding: "0" }}
                        className="bgWhite col-md-12 mt-2"
                      >
                        <AanzaDataTable
                          tableData={tableData}
                          setTableData={setTableData}
                          headers={InstallerFollowUpHeaders}
                          tableWidth="100%"
                          tableHeight="400px"
                          fontSize="lg"
                          cellRender={cellRender}
                          styleHead={{
                            background: "#3275bb",
                            color: "white !important",
                          }}
                          styleBody={{ background: "white !important" }}
                          isDisabled={isDisabled}
                          showAddIcon={false}
                          showDeleteIcon={false}
                          calculation={calculation}
                        />
                      </div>
                    </Box>
                    <Box
                      sx={{
                        padding: "0px !important",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "100% !important",
                        },
                        display: "flex",
                        flexDirection: "column",
                        gap: "10px",
                      }}
                    >
                      <Box
                        sx={{
                          padding: "15px",
                          width: {
                            base: "100% !important",
                            sm: "100%",
                          },
                        }}
                        className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                      >
                        {InstallerFollowUpSectionFormFields[2].map(
                          (control, index) => (
                            <FormControl
                              key={index}
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                flexDirection: {
                                  base: "column",
                                  sm: "column",
                                  lg: "row",
                                },
                                marginTop: "10px",
                                flexWrap: "wrap",
                              }}
                              isRequired={control.isRequired}
                            >
                              <FormLabel
                                htmlFor={control.fields[0].name}
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "100%",
                                  },
                                }}
                              >
                                {control.label}
                              </FormLabel>
                              <Box
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "100%",
                                  },
                                  display: "flex",
                                  gap: control.fields.length > 1 ? "10px" : "0",
                                }}
                              >
                                {control.fields.map((field, fieldIndex) =>
                                  field.component === "ComboBox" ? (
                                    <ComboBox
                                      key={fieldIndex}
                                      target={true}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      inputWidths={field.inputWidths}
                                      buttonWidth={field.buttonWidth}
                                      styleButton={{
                                        padding: "3px !important",
                                      }}
                                      tableData={clients}
                                      tableHeaders={field.tableHeaders}
                                      nameFields={field.nameFields}
                                      placeholders={field.placeholders}
                                      keys={field.keys}
                                      form={formData}
                                      isDisabled={isDisabled}
                                    />
                                  ) : (
                                    <Input
                                      key={fieldIndex}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      value={formData[field.value]}
                                      _placeholder={field._placeholder}
                                      type={field.type}
                                      style={{ width: field.inputWidth }}
                                      disabled={isDisabled}
                                    />
                                  )
                                )}
                              </Box>
                            </FormControl>
                          )
                        )}
                      </Box>
                    </Box>
                    <Box
                      sx={{
                        padding: "0px !important",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "100% !important",
                        },
                        display: "flex",
                        flexDirection: "column",
                        gap: "10px",
                      }}
                    >
                    </Box>
                  </Box>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

const RecoveryFollowUpPage = () => (
  <Suspense fallback={<Loader />}>
    <RecoveryFollowUp />
  </Suspense>
);

export default RecoveryFollowUpPage;
