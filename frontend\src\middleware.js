import { NextResponse } from "next/server";
import { cookies } from "next/headers";

const roleBasedRoutes = {
    1: [ // Admin
        "/dashboard",
        "/forms/business-leads",
        "/forms/define-employees",
        "/forms/define-products",
        "/forms/assessors-report",
        "/forms/audit-report",
        "/forms/delivery-report",
        "/forms/installer-report",
        "/forms/cash-receipt-voucher",
        "/forms/cash-payment-voucher",
        "/forms/bank-receipt-voucher",
        "/forms/bank-payment-voucher",
        "/forms/cash-voucher-report",
        "/profile",
        "/tasks",
    ],
    3: [ // Assessor
        "/dashboard",
        "/forms/business-leads",
        "/profile",
    ],
    5: [ // Installer
        "/dashboard",
        "/profile",
    ],
    4: [ // Delivery
        "/dashboard",
        "/profile",
    ],
};

export function middleware(req) {
    const cookieStore = cookies();
    const userCookie = cookieStore.get("user");
    const authToken = cookieStore.get("auth-token");

    // If accessing login page
    if (req.url.includes("/login")) {
        // If valid session exists, redirect to dashboard
        if (userCookie && authToken) {
            return NextResponse.redirect(new URL("/dashboard", req.url));
        }
        return NextResponse.next();
    }

    // Check for valid session for all other routes
    if (!userCookie || !authToken) {
        const response = NextResponse.redirect(new URL("/login", req.url));
        response.cookies.delete("user");
        response.cookies.delete("auth-token");
        return response;
    }

    try {
        const userData = JSON.parse(userCookie.value);
        let userRole = userData.roleId;
        if(userRole && roleBasedRoutes[userRole].some(route => req.url.includes(route))) {
            return NextResponse.next();
        } else {
            return NextResponse.redirect(new URL("/dashboard", req.url));
        }
    } catch (error) {
        console.error("Error parsing user cookie:", error);
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        "/dashboard",
        "/forms",
        "/forms/((?!general).*)",
        "/assessor/((?!general).*)",
        "/delivery/((?!general).*)",
        "/installer/((?!general).*)",
        "/login",
        "/profile",
        "/tasks",
    ],
};