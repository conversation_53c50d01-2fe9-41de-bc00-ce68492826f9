import { Box, Table, Thead, Tbody, Tr, Th, Td, Text, Divider, Flex, Spacer } from "@chakra-ui/react";
import { useUser } from "@src/app/provider/UserContext";
import numberToWords from 'number-to-words';
import dayjs from 'dayjs';

const ComponentToPrint = ({ innerRef, data }) => {
    const { user } = useUser();

    return (
        <Box ref={innerRef} p={8} maxW="800px" mx="auto">
            <Text><strong>Voucher No:</strong> {data?.Voucher_No}</Text>
            <Text><strong>Voucher Date:</strong> {dayjs(data?.Dated).format('DD MMM, YYYY')}</Text>
            <Text><strong>Cash Account:</strong> {data?.bc_name + ` (${data?.bc_id})` || "N/A"}</Text>

            <Table variant="simple" size="sm" marginTop={'40px !important'} marginBottom={'20px !important'}>
                <Thead>
                    <Tr>
                        <Th>Account Code</Th>
                        <Th>Account Title</Th>
                        <Th>Employee Name</Th>
                        <Th>Total Amount</Th>
                    </Tr>
                </Thead>
                <Tbody>
                    {data?.items?.map((item, index) => (
                        <Tr key={index}>
                            <Td>{item?.acc_id || "N/A"}</Td>
                            <Td>{item?.acc_name || "N/A"}</Td>
                            <Td>{item?.Emp_Name || "N/A"}</Td>
                            <Td>{item?.TotalAmt || 0.00}</Td>
                        </Tr>
                    ))}
                    <Tr>
                        <Td colSpan={3}>Total Amount</Td>
                        <Td>{data?.amt || 0.00}</Td>
                    </Tr>
                </Tbody>
            </Table>

            <Text textTransform={'capitalize'}><strong>Rupees:</strong> {numberToWords.toWords(data?.amt) || "N/A"}</Text>
            <Text><strong>Narration:</strong> {data?.nara || "N/A"}</Text>

            <Flex marginTop={'80px !important'}>
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Prepared by</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Checked by</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Approved by</Text>
                </Flex>
            </Flex>
            <Flex marginTop={'40px !important'} alignItems={'flex-end'}>
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text textTransform={'capitalize'}>{user?.userName || 'N/A'}</Text>
                    <Text>_____________________</Text>
                    <Text>User</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Receipient Signature</Text>
                </Flex>
            </Flex>
        </Box>
    );
};

export default ComponentToPrint;
