"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/spinner/spinner.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table-container.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @src/components/Custom/ServerPaginatedTable */ \"(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        totalCount: 0,\n        totalPages: 0\n    });\n    const [selectedVoucher, setSelectedVoucher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voucherDetails, setVoucherDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentHistory, setPaymentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalLoading, setModalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const fetchData = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n            console.log(response.data);\n            if (response.data && response.data.data) {\n                setData(response.data.data);\n                setPagination({\n                    page: response.data.page,\n                    pageSize: response.data.pageSize,\n                    totalCount: response.data.totalCount,\n                    totalPages: response.data.totalPages\n                });\n            } else {\n                setData([]);\n                setPagination({\n                    page: 1,\n                    pageSize: 20,\n                    totalCount: 0,\n                    totalPages: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch cash voucher report data\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchData();\n    }, []);\n    const fetchVoucherDetails = async (voucherNo)=>{\n        try {\n            setModalLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/voucher-details/\".concat(voucherNo));\n            if (response.data) {\n                setVoucherDetails(response.data.voucher_details);\n                setPaymentHistory(response.data.payment_history || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching voucher details: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch voucher details\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setModalLoading(false);\n        }\n    };\n    const handleOpenModal = async (voucher)=>{\n        setSelectedVoucher(voucher);\n        setIsModalOpen(true);\n        await fetchVoucherDetails(voucher.voucher_no);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedVoucher(null);\n        setVoucherDetails(null);\n        setPaymentHistory([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"voucher_no\"\n        },\n        {\n            header: \"Quotation No (adj)\",\n            field: \"quotation_no\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"client_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"client_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"gross_amount\",\n            render: (item)=>formatCurrency(item.gross_amount)\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"paid_amount\",\n            render: (item)=>formatCurrency(item.paid_amount)\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"remaining_amount\",\n            render: (item)=>formatCurrency(item.remaining_amount)\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        },\n        {\n            header: \"Actions\",\n            field: \"actions\",\n            render: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    colorScheme: \"blue\",\n                    size: \"sm\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleOpenModal(item);\n                    },\n                    children: \"View Details\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 170,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    const getRowCursor = ()=>\"pointer\";\n    const handleRowClick = (item)=>{\n        handleOpenModal(item);\n    };\n    const handlePageChange = (newPage)=>{\n        fetchData(newPage, pagination.pageSize);\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        fetchData(1, newPageSize);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 201,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Cash Voucher Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReportTable, {\n                                            data: data,\n                                            columns: columns,\n                                            onRowClick: handleRowClick,\n                                            getRowCursor: getRowCursor,\n                                            getBadgeColor: getStatusColor,\n                                            showDateFilter: false\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 206,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 205,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 204,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Modal, {\n                    isOpen: isModalOpen,\n                    onClose: handleCloseModal,\n                    size: \"6xl\",\n                    scrollBehavior: \"inside\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalOverlay, {}, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 246,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalContent, {\n                            maxH: \"90vh\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.ModalHeader, {\n                                    bg: \"blue.600\",\n                                    color: \"white\",\n                                    borderTopRadius: \"md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                        fontSize: \"xl\",\n                                        fontWeight: \"bold\",\n                                        children: [\n                                            \"Voucher Details - \",\n                                            selectedVoucher === null || selectedVoucher === void 0 ? void 0 : selectedVoucher.voucher_no\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalCloseButton, {\n                                    color: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.ModalBody, {\n                                    p: 6,\n                                    children: modalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        alignItems: \"center\",\n                                        minH: \"200px\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Spinner, {\n                                            size: \"xl\",\n                                            color: \"blue.500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 37\n                                    }, undefined) : voucherDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                        spacing: 6,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                p: 4,\n                                                bg: \"gray.50\",\n                                                borderRadius: \"md\",\n                                                border: \"1px solid\",\n                                                borderColor: \"gray.200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: \"Voucher Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Voucher No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.voucher_no\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Badge, {\n                                                                        colorScheme: getStatusColor(voucherDetails.status),\n                                                                        fontSize: \"sm\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        children: voucherDetails.status.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Divider, {\n                                                        my: 4\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Gross Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"blue.600\",\n                                                                        children: formatCurrency(voucherDetails.gross_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Paid Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"green.600\",\n                                                                        children: formatCurrency(voucherDetails.paid_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Remaining Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"red.600\",\n                                                                        children: formatCurrency(voucherDetails.remaining_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Created Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.created_at ? dayjs__WEBPACK_IMPORTED_MODULE_7___default()(voucherDetails.created_at).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: [\n                                                            \"Payment History (\",\n                                                            paymentHistory.length,\n                                                            \" payments)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    paymentHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.TableContainer, {\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        borderRadius: \"md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.Table, {\n                                                            variant: \"simple\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.Thead, {\n                                                                    bg: \"gray.100\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Tr, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Th, {\n                                                                                children: \"Payment Voucher No\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Th, {\n                                                                                children: \"Payment Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Th, {\n                                                                                isNumeric: true,\n                                                                                children: \"Amount\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Th, {\n                                                                                children: \"Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Th, {\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 65\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 57\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Tbody, {\n                                                                    children: paymentHistory.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Tr, {\n                                                                            _hover: {\n                                                                                bg: \"gray.50\"\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.Td, {\n                                                                                    fontWeight: \"semibold\",\n                                                                                    children: payment.payment_voucher_no\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 360,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.Td, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Badge, {\n                                                                                        colorScheme: payment.payment_type === \"CR\" ? \"green\" : \"blue\",\n                                                                                        variant: \"subtle\",\n                                                                                        children: payment.payment_type === \"CR\" ? \"Cash Receipt\" : payment.payment_type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                        lineNumber: 364,\n                                                                                        columnNumber: 73\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 363,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.Td, {\n                                                                                    isNumeric: true,\n                                                                                    fontWeight: \"semibold\",\n                                                                                    color: \"green.600\",\n                                                                                    children: formatCurrency(payment.payment_amount)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 371,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.Td, {\n                                                                                    children: payment.payment_date ? dayjs__WEBPACK_IMPORTED_MODULE_7___default()(payment.payment_date).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 374,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.Td, {\n                                                                                    children: payment.payment_description || \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 380,\n                                                                                    columnNumber: 69\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 65\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 49\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                                        p: 8,\n                                                        textAlign: \"center\",\n                                                        bg: \"gray.50\",\n                                                        borderRadius: \"md\",\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                color: \"gray.500\",\n                                                                fontSize: \"lg\",\n                                                                children: \"No payments found for this voucher\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                color: \"gray.400\",\n                                                                fontSize: \"sm\",\n                                                                mt: 1,\n                                                                children: \"This voucher is still pending payment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 37\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                                        textAlign: \"center\",\n                                        py: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                            color: \"gray.500\",\n                                            children: \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 240,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"qpqJ1ZYh2Yxi5koS9WCjAkO1iWI=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx":
/*!**************************************************************!*\
  !*** ./src/components/Custom/ServerPaginatedTable/index.jsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table-container.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/select/select.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nconst ServerPaginatedTable = (param)=>{\n    let { data, columns, pagination, onPageChange, onPageSizeChange, onRowClick, getRowCursor, getBadgeColor, loading = false } = param;\n    _s();\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const filteredData = data.filter((item)=>{\n        if (!searchText) return true;\n        return Object.values(item).some((value)=>value === null || value === void 0 ? void 0 : value.toString().toLowerCase().includes(searchText.toLowerCase()));\n    });\n    const handlePageChange = (newPage)=>{\n        if (onPageChange && newPage >= 1 && newPage <= pagination.totalPages) {\n            onPageChange(newPage);\n        }\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        if (onPageSizeChange) {\n            onPageSizeChange(Number(newPageSize));\n        }\n    };\n    const renderPaginationButtons = ()=>{\n        const buttons = [];\n        const { page, totalPages } = pagination;\n        // Always show first page\n        if (totalPages > 0) {\n            buttons.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>handlePageChange(1),\n                colorScheme: page === 1 ? \"blue\" : \"gray\",\n                size: \"sm\",\n                children: \"1\"\n            }, 1, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                lineNumber: 58,\n                columnNumber: 17\n            }, undefined));\n        }\n        // Show ellipsis if needed\n        if (page > 4) {\n            buttons.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                px: 2,\n                children: \"...\"\n            }, \"ellipsis1\", false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                lineNumber: 71,\n                columnNumber: 26\n            }, undefined));\n        }\n        // Show pages around current page\n        const start = Math.max(2, page - 2);\n        const end = Math.min(totalPages - 1, page + 2);\n        for(let i = start; i <= end; i++){\n            buttons.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>handlePageChange(i),\n                colorScheme: page === i ? \"blue\" : \"gray\",\n                size: \"sm\",\n                children: i\n            }, i, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                lineNumber: 80,\n                columnNumber: 17\n            }, undefined));\n        }\n        // Show ellipsis if needed\n        if (page < totalPages - 3) {\n            buttons.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                px: 2,\n                children: \"...\"\n            }, \"ellipsis2\", false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                lineNumber: 93,\n                columnNumber: 26\n            }, undefined));\n        }\n        // Always show last page if more than 1 page\n        if (totalPages > 1) {\n            buttons.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>handlePageChange(totalPages),\n                colorScheme: page === totalPages ? \"blue\" : \"gray\",\n                size: \"sm\",\n                children: totalPages\n            }, totalPages, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                lineNumber: 99,\n                columnNumber: 17\n            }, undefined));\n        }\n        return buttons;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n        className: \"card card-round mt-4\",\n        boxShadow: \"none !important\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    gap: 5\n                },\n                className: \"p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                    width: \"100%\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            children: \"Search\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                            lineNumber: 117,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            type: \"text\",\n                            name: \"search\",\n                            value: searchText,\n                            onChange: (e)=>setSearchText(e.target.value),\n                            placeholder: \"Search in all columns...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                            lineNumber: 118,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                    lineNumber: 116,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                lineNumber: 115,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                className: \"card-body\",\n                boxShadow: \"none !important\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TableContainer, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                            variant: \"simple\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Thead, {\n                                    style: {\n                                        background: \"#071533\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tr, {\n                                        children: columns.map((column, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Th, {\n                                                style: {\n                                                    color: \"white\"\n                                                },\n                                                children: column.header\n                                            }, index, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Tbody, {\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tr, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Td, {\n                                            colSpan: columns.length,\n                                            textAlign: \"center\",\n                                            children: \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 33\n                                    }, undefined) : filteredData.length > 0 ? filteredData.map((item, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tr, {\n                                            onClick: ()=>onRowClick && onRowClick(item),\n                                            style: {\n                                                cursor: getRowCursor ? getRowCursor(item) : \"default\"\n                                            },\n                                            sx: {\n                                                transition: \"all .3s\",\n                                                \"&:hover\": {\n                                                    background: \"#0403292c\"\n                                                }\n                                            },\n                                            children: columns.map((column, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Td, {\n                                                    children: column.type === \"badge\" ? column.render ? (()=>{\n                                                        const badge = column.render(item);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                            colorScheme: badge.color,\n                                                            children: badge.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 61\n                                                        }, undefined);\n                                                    })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                        colorScheme: getBadgeColor(item[column.field]),\n                                                        children: item[column.field]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 53\n                                                    }, undefined) : column.render ? column.render(item) : item[column.field] ? item[column.field] : \"N/A\"\n                                                }, colIndex, false, {\n                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 41\n                                                }, undefined))\n                                        }, rowIndex, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 33\n                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tr, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Td, {\n                                            colSpan: columns.length,\n                                            textAlign: \"center\",\n                                            children: \"No Data Found\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                            lineNumber: 129,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                        lineNumber: 128,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                        mt: 4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.HStack, {\n                            spacing: 2,\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                        value: pagination.pageSize,\n                                        onChange: (e)=>handlePageSizeChange(e.target.value),\n                                        size: \"sm\",\n                                        width: \"120px\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 10,\n                                                children: \"10 per page\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 20,\n                                                children: \"20 per page\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 50,\n                                                children: \"50 per page\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 100,\n                                                children: \"100 per page\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        children: [\n                                            \"Showing \",\n                                            (pagination.page - 1) * pagination.pageSize + 1,\n                                            \" to\",\n                                            \" \",\n                                            Math.min(pagination.page * pagination.pageSize, pagination.totalCount),\n                                            \" of\",\n                                            \" \",\n                                            pagination.totalCount,\n                                            \" entries\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                    display: \"flex\",\n                                    gap: 2,\n                                    alignItems: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handlePageChange(pagination.page - 1),\n                                            disabled: pagination.page === 1,\n                                            size: \"sm\",\n                                            children: \"\\xab Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        renderPaginationButtons(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handlePageChange(pagination.page + 1),\n                                            disabled: pagination.page === pagination.totalPages,\n                                            size: \"sm\",\n                                            children: \"Next \\xbb\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                            lineNumber: 190,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                        lineNumber: 189,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n                lineNumber: 127,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\components\\\\Custom\\\\ServerPaginatedTable\\\\index.jsx\",\n        lineNumber: 114,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ServerPaginatedTable, \"lpZkT7pWeo+MC0liMHDzSFPwEJc=\");\n_c = ServerPaginatedTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServerPaginatedTable);\nvar _c;\n$RefreshReg$(_c, \"ServerPaginatedTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx\n"));

/***/ })

});