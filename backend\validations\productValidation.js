const { param, body, validationResult } = require('express-validator');
const { coa31Columns } = require('../routes/utils/constant');
const sql = require('mssql');

const validate = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

const productValidationRules = {
    create: [
        ...coa31Columns.map(column => {
            const validator = column.isRequired ? body(column.name).notEmpty() : body(column.name).optional();
            if (column.type === sql.VarChar) {
                return column.length
                    ? validator.isString().isLength({ max: column.length }).withMessage(`${column.name} must be a string with a maximum length of ${column.length}`)
                    : validator.isString().withMessage(`${column.name} must be a string`);
            } else if (column.type === sql.Float) {
                return validator.isFloat().withMessage(`${column.name} must be a float`);
            } else if (column.type === sql.Int) {
                return validator.isInt().withMessage(`${column.name} must be an integer`);
            } else if (column.type === sql.TinyInt || column.type === sql.SmallInt) {
                return validator.isInt().withMessage(`${column.name} must be a small integer`);
            } else if (column.type === sql.DateTime) {
                return validator.isISO8601().withMessage(`${column.name} must be a valid date`);
            }
            return null; // Skip unsupported types
        }).filter(Boolean), // Remove null entries
        validate
    ]
};

module.exports = productValidationRules;