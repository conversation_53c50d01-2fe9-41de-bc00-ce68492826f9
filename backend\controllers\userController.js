const { sql, getPool } = require('../db');
const { validationResult } = require('express-validator');
const zlib = require('zlib');

const userController = {
    getAllUsers: async(req, res) => {
        try {
            const query = 'SELECT * FROM users';
            const pool = await getPool();
            const request = new sql.Request(pool);
            const result = await request.query(query);
            res.status(200).json(result.recordset);
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    getUserById: async(req, res) => {
        try {
            const userId = req.params.id;
            const query = 'SELECT * FROM users WHERE ID = @ID';
            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('ID', sql.VarChar(8), userId);
            const result = await request.query(query);

            if (result.recordset.length === 0) {
                return res.status(404).json({ message: 'User not found.' });
            }
            res.status(200).json(result.recordset[0]);
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    login: async(req, res) => {
        try {
            const { userName, password } = req.body;

            // First get the user by username to check case sensitivity
            const userQuery = `
                SELECT U.*, R.RoleName AS RoleName,
                       U.image as compressed_image,
                       U.image_mime_type as mime_type
                FROM users U
                LEFT JOIN User_Role R ON U.RoleID = R.RoleID
                WHERE id = @ID`;

            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('ID', sql.VarChar(32), userName);
            const result = await request.query(userQuery);

            if (result.recordset.length === 0) {
                return res.status(401).json({ message: 'Invalid credentials.' });
            }

            const user = result.recordset[0];

            // Compare passwords with exact case matching
            if (user.pwd !== password || user.id !== userName) {
                return res.status(401).json({ message: 'Invalid credentials.' });
            }

            // Decompress the image if it exists
            let imageUrl = null;
            if (user.compressed_image) {
                try {
                    const decompressedImage = zlib.gunzipSync(user.compressed_image);
                    imageUrl = `data:${user.mime_type};base64,${decompressedImage.toString('base64')}`;
                } catch (err) {
                    console.error('Error decompressing image:', err);
                }
            }

            res.status(200).json({
                message: 'Login successfully..!',
                data: {
                    id: user.id,
                    location: user.Location,
                    userName: user.user_name,
                    EmailAddress: user.EmailAddress,
                    title: user.Title,
                    roleId: user.RoleID,
                    roleName: user.RoleName,
                    empID: user.Emp_ID || null,
                    imageUrl: imageUrl
                }
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    updateProfile: async(req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { userName, email } = req.body;
            const userId = req.user.id;

            const pool = await getPool();
            const request = new sql.Request(pool);

            let updateQuery = 'UPDATE users SET ';
            const updateFields = [];

            if (userName) {
                updateFields.push('user_name = @userName');
                request.input('userName', sql.VarChar(50), userName);
            }
            if (email) {
                updateFields.push('EmailAddress = @email');
                request.input('email', sql.VarChar(100), email);
            }

            if (updateFields.length === 0) {
                return res.status(400).json({
                    message: 'No fields to update'
                });
            }

            updateQuery += updateFields.join(', ') + ' WHERE id = @userId';
            request.input('userId', sql.VarChar(32), userId);

            await request.query(updateQuery);

            // Fetch updated user data
            const selectQuery = `
                SELECT id, user_name as userName, EmailAddress
                FROM users 
                WHERE id = @userId`;

            const result = await request.query(selectQuery);

            res.json(result.recordset[0]);
        } catch (error) {
            console.error('Error updating profile:', error);
            res.status(500).json({
                message: 'Error updating profile'
            });
        }
    },

    uploadProfileImage: async(req, res) => {
        try {
            if (!req.file) {
                return res.status(400).json({
                    message: 'No image file provided'
                });
            }

            const userId = req.user.id;
            const imageBuffer = req.file.buffer;

            const compressedImage = zlib.gzipSync(imageBuffer);

            const pool = await getPool();
            const request = new sql.Request(pool);

            request.input('userId', sql.VarChar(32), userId);
            request.input('image', sql.VarBinary(sql.MAX), compressedImage);
            request.input('originalSize', sql.Int, imageBuffer.length);
            request.input('compressedSize', sql.Int, compressedImage.length);
            request.input('mimeType', sql.VarChar(50), req.file.mimetype);

            await request.query(`
                UPDATE users 
                SET image = @image,
                    image_original_size = @originalSize,
                    image_compressed_size = @compressedSize,
                    image_mime_type = @mimeType
                WHERE id = @userId
            `);

            const base64Image = `data:${req.file.mimetype};base64,${imageBuffer.toString('base64')}`;

            res.json({
                imageUrl: base64Image,
                originalSize: imageBuffer.length,
                compressedSize: compressedImage.length,
                compressionRatio: ((1 - (compressedImage.length / imageBuffer.length)) * 100).toFixed(2) + '%'
            });
        } catch (error) {
            console.error('Error uploading profile image:', error);
            res.status(500).json({
                message: 'Error uploading profile image'
            });
        }
    },

    updatePassword: async(req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }

            const { currentPassword, newPassword } = req.body;
            const userId = req.user.id;

            // Verify current password
            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('userId', sql.VarChar(32), userId);

            const result = await request.query('SELECT pwd FROM users WHERE id = @userId');

            if (result.recordset.length === 0) {
                return res.status(404).json({
                    message: 'User not found'
                });
            }

            const user = result.recordset[0];

            // Compare current password
            if (user.pwd !== currentPassword) {
                return res.status(401).json({
                    message: 'Current password is incorrect'
                });
            }

            // Update password
            const updateRequest = new sql.Request(pool);
            updateRequest.input('userId', sql.VarChar(32), userId);
            updateRequest.input('newPassword', sql.VarChar(100), newPassword);

            await updateRequest.query('UPDATE users SET pwd = @newPassword WHERE id = @userId');

            res.json({
                message: 'Password updated successfully'
            });
        } catch (error) {
            console.error('Error updating password:', error);
            res.status(500).json({
                message: 'Error updating password'
            });
        }
    }
};

module.exports = userController;