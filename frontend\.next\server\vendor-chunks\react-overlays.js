"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-overlays";
exports.ids = ["vendor-chunks/react-overlays"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-overlays/esm/Overlay.js":
/*!****************************************************!*\
  !*** ./node_modules/react-overlays/esm/Overlay.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _restart_hooks_useCallbackRef__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @restart/hooks/useCallbackRef */ \"(ssr)/./node_modules/@restart/hooks/esm/useCallbackRef.js\");\n/* harmony import */ var _restart_hooks_useMergedRefs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @restart/hooks/useMergedRefs */ \"(ssr)/./node_modules/@restart/hooks/esm/useMergedRefs.js\");\n/* harmony import */ var _popper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./popper */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _usePopper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./usePopper */ \"(ssr)/./node_modules/react-overlays/esm/usePopper.js\");\n/* harmony import */ var _useRootClose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useRootClose */ \"(ssr)/./node_modules/react-overlays/esm/useRootClose.js\");\n/* harmony import */ var _useWaitForDOMRef__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./useWaitForDOMRef */ \"(ssr)/./node_modules/react-overlays/esm/useWaitForDOMRef.js\");\n/* harmony import */ var _mergeOptionsWithPopperConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./mergeOptionsWithPopperConfig */ \"(ssr)/./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Built on top of `Popper.js`, the overlay component is\n * great for custom tooltip overlays.\n */\nvar Overlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef(function (props, outerRef) {\n  var flip = props.flip,\n      offset = props.offset,\n      placement = props.placement,\n      _props$containerPaddi = props.containerPadding,\n      containerPadding = _props$containerPaddi === void 0 ? 5 : _props$containerPaddi,\n      _props$popperConfig = props.popperConfig,\n      popperConfig = _props$popperConfig === void 0 ? {} : _props$popperConfig,\n      Transition = props.transition;\n\n  var _useCallbackRef = (0,_restart_hooks_useCallbackRef__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n      rootElement = _useCallbackRef[0],\n      attachRef = _useCallbackRef[1];\n\n  var _useCallbackRef2 = (0,_restart_hooks_useCallbackRef__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n      arrowElement = _useCallbackRef2[0],\n      attachArrowRef = _useCallbackRef2[1];\n\n  var mergedRef = (0,_restart_hooks_useMergedRefs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(attachRef, outerRef);\n  var container = (0,_useWaitForDOMRef__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(props.container);\n  var target = (0,_useWaitForDOMRef__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(props.target);\n\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(!props.show),\n      exited = _useState[0],\n      setExited = _useState[1];\n\n  var _usePopper = (0,_usePopper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(target, rootElement, (0,_mergeOptionsWithPopperConfig__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    placement: placement,\n    enableEvents: !!props.show,\n    containerPadding: containerPadding || 5,\n    flip: flip,\n    offset: offset,\n    arrowElement: arrowElement,\n    popperConfig: popperConfig\n  })),\n      styles = _usePopper.styles,\n      attributes = _usePopper.attributes,\n      popper = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_usePopper, [\"styles\", \"attributes\"]);\n\n  if (props.show) {\n    if (exited) setExited(false);\n  } else if (!props.transition && !exited) {\n    setExited(true);\n  }\n\n  var handleHidden = function handleHidden() {\n    setExited(true);\n\n    if (props.onExited) {\n      props.onExited.apply(props, arguments);\n    }\n  }; // Don't un-render the overlay while it's transitioning out.\n\n\n  var mountOverlay = props.show || Transition && !exited;\n  (0,_useRootClose__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(rootElement, props.onHide, {\n    disabled: !props.rootClose || props.rootCloseDisabled,\n    clickTrigger: props.rootCloseEvent\n  });\n\n  if (!mountOverlay) {\n    // Don't bother showing anything if we don't have to.\n    return null;\n  }\n\n  var child = props.children((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, popper, {\n    show: !!props.show,\n    props: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, attributes.popper, {\n      style: styles.popper,\n      ref: mergedRef\n    }),\n    arrowProps: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, attributes.arrow, {\n      style: styles.arrow,\n      ref: attachArrowRef\n    })\n  }));\n\n  if (Transition) {\n    var onExit = props.onExit,\n        onExiting = props.onExiting,\n        onEnter = props.onEnter,\n        onEntering = props.onEntering,\n        onEntered = props.onEntered;\n    child = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().createElement(Transition, {\n      \"in\": props.show,\n      appear: true,\n      onExit: onExit,\n      onExiting: onExiting,\n      onExited: handleHidden,\n      onEnter: onEnter,\n      onEntering: onEntering,\n      onEntered: onEntered\n    }, child);\n  }\n\n  return container ? /*#__PURE__*/react_dom__WEBPACK_IMPORTED_MODULE_3___default().createPortal(child, container) : null;\n});\nOverlay.displayName = 'Overlay';\nOverlay.propTypes = {\n  /**\n   * Set the visibility of the Overlay\n   */\n  show: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /** Specify where the overlay element is positioned in relation to the target element */\n  placement: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOf(_popper__WEBPACK_IMPORTED_MODULE_11__.placements),\n\n  /**\n   * A DOM Element, Ref to an element, or function that returns either. The `target` element is where\n   * the overlay is positioned relative to.\n   */\n  target: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().any),\n\n  /**\n   * A DOM Element, Ref to an element, or function that returns either. The `container` will have the Portal children\n   * appended to it.\n   */\n  container: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().any),\n\n  /**\n   * Enables the Popper.js `flip` modifier, allowing the Overlay to\n   * automatically adjust it's placement in case of overlap with the viewport or toggle.\n   * Refer to the [flip docs](https://popper.js.org/popper-documentation.html#modifiers..flip.enabled) for more info\n   */\n  flip: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /**\n   * A render prop that returns an element to overlay and position. See\n   * the [react-popper documentation](https://github.com/FezVrasta/react-popper#children) for more info.\n   *\n   * @type {Function ({\n   *   show: boolean,\n   *   placement: Placement,\n   *   update: () => void,\n   *   forceUpdate: () => void,\n   *   props: {\n   *     ref: (?HTMLElement) => void,\n   *     style: { [string]: string | number },\n   *     aria-labelledby: ?string\n   *     [string]: string | number,\n   *   },\n   *   arrowProps: {\n   *     ref: (?HTMLElement) => void,\n   *     style: { [string]: string | number },\n   *     [string]: string | number,\n   *   },\n   * }) => React.Element}\n   */\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func).isRequired,\n\n  /**\n   * Control how much space there is between the edge of the boundary element and overlay.\n   * A convenience shortcut to setting `popperConfig.modfiers.preventOverflow.padding`\n   */\n  containerPadding: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().number),\n\n  /**\n   * A set of popper options and props passed directly to react-popper's Popper component.\n   */\n  popperConfig: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().object),\n\n  /**\n   * Specify whether the overlay should trigger `onHide` when the user clicks outside the overlay\n   */\n  rootClose: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /**\n   * Specify event for toggling overlay\n   */\n  rootCloseEvent: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOf(['click', 'mousedown']),\n\n  /**\n   * Specify disabled for disable RootCloseWrapper\n   */\n  rootCloseDisabled: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /**\n   * A Callback fired by the Overlay when it wishes to be hidden.\n   *\n   * __required__ when `rootClose` is `true`.\n   *\n   * @type func\n   */\n  onHide: function onHide(props) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    if (props.rootClose) {\n      var _PropTypes$func;\n\n      return (_PropTypes$func = (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func)).isRequired.apply(_PropTypes$func, [props].concat(args));\n    }\n\n    return prop_types__WEBPACK_IMPORTED_MODULE_10___default().func.apply((prop_types__WEBPACK_IMPORTED_MODULE_10___default()), [props].concat(args));\n  },\n\n  /**\n   * A `react-transition-group@2.0.0` `<Transition/>` component\n   * used to animate the overlay as it changes visibility.\n   */\n  // @ts-ignore\n  transition: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().elementType),\n\n  /**\n   * Callback fired before the Overlay transitions in\n   */\n  onEnter: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired as the Overlay begins to transition in\n   */\n  onEntering: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired after the Overlay finishes transitioning in\n   */\n  onEntered: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired right before the Overlay transitions out\n   */\n  onExit: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired as the Overlay begins to transition out\n   */\n  onExiting: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired after the Overlay finishes transitioning out\n   */\n  onExited: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Overlay);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/Overlay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mergeOptionsWithPopperConfig),\n/* harmony export */   toModifierArray: () => (/* binding */ toModifierArray),\n/* harmony export */   toModifierMap: () => (/* binding */ toModifierMap)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\nfunction toModifierMap(modifiers) {\n  var result = {};\n\n  if (!Array.isArray(modifiers)) {\n    return modifiers || result;\n  } // eslint-disable-next-line no-unused-expressions\n\n\n  modifiers == null ? void 0 : modifiers.forEach(function (m) {\n    result[m.name] = m;\n  });\n  return result;\n}\nfunction toModifierArray(map) {\n  if (map === void 0) {\n    map = {};\n  }\n\n  if (Array.isArray(map)) return map;\n  return Object.keys(map).map(function (k) {\n    map[k].name = k;\n    return map[k];\n  });\n}\nfunction mergeOptionsWithPopperConfig(_ref) {\n  var _modifiers$preventOve, _modifiers$preventOve2, _modifiers$offset, _modifiers$arrow;\n\n  var enabled = _ref.enabled,\n      enableEvents = _ref.enableEvents,\n      placement = _ref.placement,\n      flip = _ref.flip,\n      offset = _ref.offset,\n      fixed = _ref.fixed,\n      containerPadding = _ref.containerPadding,\n      arrowElement = _ref.arrowElement,\n      _ref$popperConfig = _ref.popperConfig,\n      popperConfig = _ref$popperConfig === void 0 ? {} : _ref$popperConfig;\n  var modifiers = toModifierMap(popperConfig.modifiers);\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, popperConfig, {\n    placement: placement,\n    enabled: enabled,\n    strategy: fixed ? 'fixed' : popperConfig.strategy,\n    modifiers: toModifierArray((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, modifiers, {\n      eventListeners: {\n        enabled: enableEvents\n      },\n      preventOverflow: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, modifiers.preventOverflow, {\n        options: containerPadding ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n          padding: containerPadding\n        }, (_modifiers$preventOve = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve.options) : (_modifiers$preventOve2 = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve2.options\n      }),\n      offset: {\n        options: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n          offset: offset\n        }, (_modifiers$offset = modifiers.offset) == null ? void 0 : _modifiers$offset.options)\n      },\n      arrow: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, modifiers.arrow, {\n        enabled: !!arrowElement,\n        options: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (_modifiers$arrow = modifiers.arrow) == null ? void 0 : _modifiers$arrow.options, {\n          element: arrowElement\n        })\n      }),\n      flip: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        enabled: !!flip\n      }, modifiers.flip)\n    }))\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/ownerDocument.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-overlays/esm/ownerDocument.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dom-helpers/ownerDocument */ \"(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\");\n/* harmony import */ var _safeFindDOMNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./safeFindDOMNode */ \"(ssr)/./node_modules/react-overlays/esm/safeFindDOMNode.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (componentOrElement) {\n  return (0,dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_safeFindDOMNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(componentOrElement));\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL293bmVyRG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNEO0FBQ047QUFDaEQsaUVBQWdCO0FBQ2hCLFNBQVMscUVBQWEsQ0FBQyw0REFBZTtBQUN0QyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL3JlYWN0LW92ZXJsYXlzL2VzbS9vd25lckRvY3VtZW50LmpzPzgwMWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG93bmVyRG9jdW1lbnQgZnJvbSAnZG9tLWhlbHBlcnMvb3duZXJEb2N1bWVudCc7XG5pbXBvcnQgc2FmZUZpbmRET01Ob2RlIGZyb20gJy4vc2FmZUZpbmRET01Ob2RlJztcbmV4cG9ydCBkZWZhdWx0IChmdW5jdGlvbiAoY29tcG9uZW50T3JFbGVtZW50KSB7XG4gIHJldHVybiBvd25lckRvY3VtZW50KHNhZmVGaW5kRE9NTm9kZShjb21wb25lbnRPckVsZW1lbnQpKTtcbn0pOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/ownerDocument.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/popper.js":
/*!***************************************************!*\
  !*** ./node_modules/react-overlays/esm/popper.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPopper: () => (/* binding */ createPopper),\n/* harmony export */   placements: () => (/* reexport safe */ _popperjs_core_lib_enums__WEBPACK_IMPORTED_MODULE_9__.placements)\n/* harmony export */ });\n/* harmony import */ var _popperjs_core_lib_modifiers_arrow__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/arrow */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/arrow.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_computeStyles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/computeStyles */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_eventListeners__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/eventListeners */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_flip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/flip */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/flip.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_hide__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/hide */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/hide.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_offset__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/offset */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/offset.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_popperOffsets__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/popperOffsets */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_preventOverflow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/preventOverflow */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js\");\n/* harmony import */ var _popperjs_core_lib_enums__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @popperjs/core/lib/enums */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _popperjs_core_lib_popper_base__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @popperjs/core/lib/popper-base */ \"(ssr)/./node_modules/@popperjs/core/lib/createPopper.js\");\n\n\n\n\n\n\n\n\n\n // For the common JS build we will turn this file into a bundle with no imports.\n// This is b/c the Popper lib is all esm files, and would break in a common js only environment\n\nvar createPopper = (0,_popperjs_core_lib_popper_base__WEBPACK_IMPORTED_MODULE_0__.popperGenerator)({\n  defaultModifiers: [_popperjs_core_lib_modifiers_hide__WEBPACK_IMPORTED_MODULE_1__[\"default\"], _popperjs_core_lib_modifiers_popperOffsets__WEBPACK_IMPORTED_MODULE_2__[\"default\"], _popperjs_core_lib_modifiers_computeStyles__WEBPACK_IMPORTED_MODULE_3__[\"default\"], _popperjs_core_lib_modifiers_eventListeners__WEBPACK_IMPORTED_MODULE_4__[\"default\"], _popperjs_core_lib_modifiers_offset__WEBPACK_IMPORTED_MODULE_5__[\"default\"], _popperjs_core_lib_modifiers_flip__WEBPACK_IMPORTED_MODULE_6__[\"default\"], _popperjs_core_lib_modifiers_preventOverflow__WEBPACK_IMPORTED_MODULE_7__[\"default\"], _popperjs_core_lib_modifiers_arrow__WEBPACK_IMPORTED_MODULE_8__[\"default\"]]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL3BvcHBlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDZ0I7QUFDRTtBQUNwQjtBQUNBO0FBQ0k7QUFDYztBQUNJO0FBQ3JCO0FBQ1csQ0FBQztBQUNsRTs7QUFFTyxtQkFBbUIsK0VBQWU7QUFDekMscUJBQXFCLHlFQUFJLEVBQUUsa0ZBQWEsRUFBRSxrRkFBYSxFQUFFLG1GQUFjLEVBQUUsMkVBQU0sRUFBRSx5RUFBSSxFQUFFLG9GQUFlLEVBQUUsMEVBQUs7QUFDN0csQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9yZWFjdC1vdmVybGF5cy9lc20vcG9wcGVyLmpzPzMzMTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFycm93IGZyb20gJ0Bwb3BwZXJqcy9jb3JlL2xpYi9tb2RpZmllcnMvYXJyb3cnO1xuaW1wb3J0IGNvbXB1dGVTdHlsZXMgZnJvbSAnQHBvcHBlcmpzL2NvcmUvbGliL21vZGlmaWVycy9jb21wdXRlU3R5bGVzJztcbmltcG9ydCBldmVudExpc3RlbmVycyBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL2V2ZW50TGlzdGVuZXJzJztcbmltcG9ydCBmbGlwIGZyb20gJ0Bwb3BwZXJqcy9jb3JlL2xpYi9tb2RpZmllcnMvZmxpcCc7XG5pbXBvcnQgaGlkZSBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL2hpZGUnO1xuaW1wb3J0IG9mZnNldCBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL29mZnNldCc7XG5pbXBvcnQgcG9wcGVyT2Zmc2V0cyBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL3BvcHBlck9mZnNldHMnO1xuaW1wb3J0IHByZXZlbnRPdmVyZmxvdyBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL3ByZXZlbnRPdmVyZmxvdyc7XG5pbXBvcnQgeyBwbGFjZW1lbnRzIH0gZnJvbSAnQHBvcHBlcmpzL2NvcmUvbGliL2VudW1zJztcbmltcG9ydCB7IHBvcHBlckdlbmVyYXRvciB9IGZyb20gJ0Bwb3BwZXJqcy9jb3JlL2xpYi9wb3BwZXItYmFzZSc7IC8vIEZvciB0aGUgY29tbW9uIEpTIGJ1aWxkIHdlIHdpbGwgdHVybiB0aGlzIGZpbGUgaW50byBhIGJ1bmRsZSB3aXRoIG5vIGltcG9ydHMuXG4vLyBUaGlzIGlzIGIvYyB0aGUgUG9wcGVyIGxpYiBpcyBhbGwgZXNtIGZpbGVzLCBhbmQgd291bGQgYnJlYWsgaW4gYSBjb21tb24ganMgb25seSBlbnZpcm9ubWVudFxuXG5leHBvcnQgdmFyIGNyZWF0ZVBvcHBlciA9IHBvcHBlckdlbmVyYXRvcih7XG4gIGRlZmF1bHRNb2RpZmllcnM6IFtoaWRlLCBwb3BwZXJPZmZzZXRzLCBjb21wdXRlU3R5bGVzLCBldmVudExpc3RlbmVycywgb2Zmc2V0LCBmbGlwLCBwcmV2ZW50T3ZlcmZsb3csIGFycm93XVxufSk7XG5leHBvcnQgeyBwbGFjZW1lbnRzIH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/popper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/safeFindDOMNode.js":
/*!************************************************************!*\
  !*** ./node_modules/react-overlays/esm/safeFindDOMNode.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ safeFindDOMNode)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    return react_dom__WEBPACK_IMPORTED_MODULE_0___default().findDOMNode(componentOrElement);\n  }\n\n  return componentOrElement != null ? componentOrElement : null;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL3NhZmVGaW5kRE9NTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFDbEI7QUFDZjtBQUNBLFdBQVcsNERBQW9CO0FBQy9COztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL3NhZmVGaW5kRE9NTm9kZS5qcz82ZTllIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdERPTSBmcm9tICdyZWFjdC1kb20nO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc2FmZUZpbmRET01Ob2RlKGNvbXBvbmVudE9yRWxlbWVudCkge1xuICBpZiAoY29tcG9uZW50T3JFbGVtZW50ICYmICdzZXRTdGF0ZScgaW4gY29tcG9uZW50T3JFbGVtZW50KSB7XG4gICAgcmV0dXJuIFJlYWN0RE9NLmZpbmRET01Ob2RlKGNvbXBvbmVudE9yRWxlbWVudCk7XG4gIH1cblxuICByZXR1cm4gY29tcG9uZW50T3JFbGVtZW50ICE9IG51bGwgPyBjb21wb25lbnRPckVsZW1lbnQgOiBudWxsO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/safeFindDOMNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/usePopper.js":
/*!******************************************************!*\
  !*** ./node_modules/react-overlays/esm/usePopper.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _restart_hooks_useSafeState__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @restart/hooks/useSafeState */ \"(ssr)/./node_modules/@restart/hooks/esm/useSafeState.js\");\n/* harmony import */ var _popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./popper */ \"(ssr)/./node_modules/react-overlays/esm/popper.js\");\n\n\n\n\n\n\nvar initialPopperStyles = function initialPopperStyles(position) {\n  return {\n    position: position,\n    top: '0',\n    left: '0',\n    opacity: '0',\n    pointerEvents: 'none'\n  };\n};\n\nvar disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false\n}; // In order to satisfy the current usage of options, including undefined\n\nvar ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: function effect(_ref) {\n    var state = _ref.state;\n    return function () {\n      var _state$elements = state.elements,\n          reference = _state$elements.reference,\n          popper = _state$elements.popper;\n\n      if ('removeAttribute' in reference) {\n        var ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(function (id) {\n          return id.trim() !== popper.id;\n        });\n        if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n      }\n    };\n  },\n  fn: function fn(_ref2) {\n    var _popper$getAttribute;\n\n    var state = _ref2.state;\n    var _state$elements2 = state.elements,\n        popper = _state$elements2.popper,\n        reference = _state$elements2.reference;\n    var role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      var ids = reference.getAttribute('aria-describedby');\n\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n\n      reference.setAttribute('aria-describedby', ids ? ids + \",\" + popper.id : popper.id);\n    }\n  }\n};\nvar EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {boolean=}    options.eventsEnabled have Popper listen on window resize events to reposition the element\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\n\nfunction usePopper(referenceElement, popperElement, _temp) {\n  var _ref3 = _temp === void 0 ? {} : _temp,\n      _ref3$enabled = _ref3.enabled,\n      enabled = _ref3$enabled === void 0 ? true : _ref3$enabled,\n      _ref3$placement = _ref3.placement,\n      placement = _ref3$placement === void 0 ? 'bottom' : _ref3$placement,\n      _ref3$strategy = _ref3.strategy,\n      strategy = _ref3$strategy === void 0 ? 'absolute' : _ref3$strategy,\n      _ref3$modifiers = _ref3.modifiers,\n      modifiers = _ref3$modifiers === void 0 ? EMPTY_MODIFIERS : _ref3$modifiers,\n      config = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref3, [\"enabled\", \"placement\", \"strategy\", \"modifiers\"]);\n\n  var popperInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  var update = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {\n    var _popperInstanceRef$cu;\n\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  var forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {\n    var _popperInstanceRef$cu2;\n\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n\n  var _useSafeState = (0,_restart_hooks_useSafeState__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n    placement: placement,\n    update: update,\n    forceUpdate: forceUpdate,\n    attributes: {},\n    styles: {\n      popper: initialPopperStyles(strategy),\n      arrow: {}\n    }\n  })),\n      popperState = _useSafeState[0],\n      setState = _useSafeState[1];\n\n  var updateModifier = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {\n    return {\n      name: 'updateStateModifier',\n      enabled: true,\n      phase: 'write',\n      requires: ['computeStyles'],\n      fn: function fn(_ref4) {\n        var state = _ref4.state;\n        var styles = {};\n        var attributes = {};\n        Object.keys(state.elements).forEach(function (element) {\n          styles[element] = state.styles[element];\n          attributes[element] = state.attributes[element];\n        });\n        setState({\n          state: state,\n          styles: styles,\n          attributes: attributes,\n          update: update,\n          forceUpdate: forceUpdate,\n          placement: state.placement\n        });\n      }\n    };\n  }, [update, forceUpdate, setState]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement: placement,\n      strategy: strategy,\n      modifiers: [].concat(modifiers, [updateModifier, disabledApplyStylesModifier])\n    }); // intentionally NOT re-running on new modifiers\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [strategy, placement, updateModifier, enabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n\n    popperInstanceRef.current = (0,_popper__WEBPACK_IMPORTED_MODULE_4__.createPopper)(referenceElement, popperElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config, {\n      placement: placement,\n      strategy: strategy,\n      modifiers: [].concat(modifiers, [ariaDescribedByModifier, updateModifier])\n    }));\n    return function () {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(function (s) {\n          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, s, {\n            attributes: {},\n            styles: {\n              popper: initialPopperStyles(strategy)\n            }\n          });\n        });\n      }\n    }; // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (usePopper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/usePopper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/useRootClose.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-overlays/esm/useRootClose.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var dom_helpers_contains__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dom-helpers/contains */ \"(ssr)/./node_modules/dom-helpers/esm/contains.js\");\n/* harmony import */ var dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dom-helpers/listen */ \"(ssr)/./node_modules/dom-helpers/esm/listen.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _restart_hooks_useEventCallback__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @restart/hooks/useEventCallback */ \"(ssr)/./node_modules/@restart/hooks/esm/useEventCallback.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(warning__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ownerDocument__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ownerDocument */ \"(ssr)/./node_modules/react-overlays/esm/ownerDocument.js\");\n\n\n\n\n\n\nvar escapeKeyCode = 27;\n\nvar noop = function noop() {};\n\nfunction isLeftClickEvent(event) {\n  return event.button === 0;\n}\n\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nvar getRefTarget = function getRefTarget(ref) {\n  return ref && ('current' in ref ? ref.current : ref);\n};\n\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose, _temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      disabled = _ref.disabled,\n      _ref$clickTrigger = _ref.clickTrigger,\n      clickTrigger = _ref$clickTrigger === void 0 ? 'click' : _ref$clickTrigger;\n\n  var preventMouseRootCloseRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n  var onClose = onRootClose || noop;\n  var handleMouseCapture = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {\n    var _e$composedPath$;\n\n    var currentTarget = getRefTarget(ref);\n    warning__WEBPACK_IMPORTED_MODULE_4___default()(!!currentTarget, 'RootClose captured a close event but does not have a ref to compare it to. ' + 'useRootClose(), should be passed a ref that resolves to a DOM node');\n    preventMouseRootCloseRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!(0,dom_helpers_contains__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(currentTarget, (_e$composedPath$ = e.composedPath == null ? void 0 : e.composedPath()[0]) != null ? _e$composedPath$ : e.target);\n  }, [ref]);\n  var handleMouse = (0,_restart_hooks_useEventCallback__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function (e) {\n    if (!preventMouseRootCloseRef.current) {\n      onClose(e);\n    }\n  });\n  var handleKeyUp = (0,_restart_hooks_useEventCallback__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function (e) {\n    if (e.keyCode === escapeKeyCode) {\n      onClose(e);\n    }\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    if (disabled || ref == null) return undefined; // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n\n    var currentEvent = window.event;\n    var doc = (0,_ownerDocument__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(getRefTarget(ref)); // Use capture for this listener so it fires before React's listener, to\n    // avoid false positives in the contains() check below if the target DOM\n    // element is removed in the React mouse callback.\n\n    var removeMouseCaptureListener = (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(doc, clickTrigger, handleMouseCapture, true);\n    var removeMouseListener = (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(doc, clickTrigger, function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleMouse(e);\n    });\n    var removeKeyupListener = (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(doc, 'keyup', function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleKeyUp(e);\n    });\n    var mobileSafariHackListeners = [];\n\n    if ('ontouchstart' in doc.documentElement) {\n      mobileSafariHackListeners = [].slice.call(doc.body.children).map(function (el) {\n        return (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(el, 'mousemove', noop);\n      });\n    }\n\n    return function () {\n      removeMouseCaptureListener();\n      removeMouseListener();\n      removeKeyupListener();\n      mobileSafariHackListeners.forEach(function (remove) {\n        return remove();\n      });\n    };\n  }, [ref, disabled, clickTrigger, handleMouseCapture, handleMouse, handleKeyUp]);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRootClose);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/useRootClose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/useWaitForDOMRef.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-overlays/esm/useWaitForDOMRef.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useWaitForDOMRef),\n/* harmony export */   resolveContainerRef: () => (/* binding */ resolveContainerRef)\n/* harmony export */ });\n/* harmony import */ var dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dom-helpers/ownerDocument */ \"(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar resolveContainerRef = function resolveContainerRef(ref) {\n  var _ref;\n\n  if (typeof document === 'undefined') return null;\n  if (ref == null) return (0,dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().body;\n  if (typeof ref === 'function') ref = ref();\n  if (ref && 'current' in ref) ref = ref.current;\n  if ((_ref = ref) != null && _ref.nodeType) return ref || null;\n  return null;\n};\nfunction useWaitForDOMRef(ref, onResolved) {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {\n    return resolveContainerRef(ref);\n  }),\n      resolvedRef = _useState[0],\n      setRef = _useState[1];\n\n  if (!resolvedRef) {\n    var earlyRef = resolveContainerRef(ref);\n    if (earlyRef) setRef(earlyRef);\n  }\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (onResolved && resolvedRef) {\n      onResolved(resolvedRef);\n    }\n  }, [onResolved, resolvedRef]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var nextRef = resolveContainerRef(ref);\n\n    if (nextRef !== resolvedRef) {\n      setRef(nextRef);\n    }\n  }, [ref, resolvedRef]);\n  return resolvedRef;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/useWaitForDOMRef.js\n");

/***/ })

};
;