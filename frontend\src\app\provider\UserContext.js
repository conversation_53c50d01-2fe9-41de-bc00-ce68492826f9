"use client";

import React, { createContext, useState, useContext, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@chakra-ui/react";
import axiosInstance from "../axios";
import Cookies from "js-cookie";

const UserContext = createContext();

export const UserProvider = ({ children }) => {
    const [loading, setLoading] = useState(false);
    const [user, setUser] = useState(null);
    const router = useRouter();
    const toast = useToast();
    const userRole = useMemo(() => user && user?.roleName, [user]);

    const stripImageDataForCookie = (userData) => {
        if (!userData) return null;
        const { imageUrl, ...strippedData } = userData;
        return strippedData;
    };

    const storeUserData = (userData, authToken) => {
        setUser(userData);

        const cookieData = stripImageDataForCookie(userData);

        Cookies.set("user", JSON.stringify(cookieData), {
            expires: 7,
            path: "/",
            sameSite: 'strict'
        });

        if (authToken) {
            Cookies.set("auth-token", authToken, {
                expires: 7,
                path: "/",
                sameSite: 'strict'
            });
        }
    };

    const autoLogin = async (userName, password) => {
        try {
            const res = await axiosInstance.post("user/login", {
                userName: userName,
                password: password,
            });

            let userData = res.data.data;
            const authToken = btoa(`${userName}:${password}`);

            storeUserData(userData, authToken);

            return true;
        } catch (error) {
            console.error("Auto login failed:", error);
            return false;
        }
    };

    const clearSession = (shouldRedirect = true) => {
        Cookies.remove("auth-token", { path: "/" });
        Cookies.remove("user", { path: "/" });
        setUser(null);
        if (shouldRedirect) {
            // Check if already on login page before redirecting
            const currentPath = window.location.pathname;
            if (currentPath !== '/login') {
                router.push("/login");
            }
        }
    };

    const updateUserProfile = (updatedData) => {
        const newUserData = { ...user, ...updatedData };
        storeUserData(newUserData);
    };

    const checkSession = async () => {
        const userCookie = Cookies.get("user");
        const authToken = Cookies.get("auth-token");

        if (!userCookie || !authToken) {
            clearSession(false);
            return false;
        }

        try {
            const userData = JSON.parse(userCookie);
            if (!userData || !userData.roleName) {
                clearSession(false);
                return false;
            }

            const credentials = atob(authToken).split(':');
            const loginSuccess = await autoLogin(credentials[0], credentials[1]);
            if (!loginSuccess) {
                clearSession(false);
            }

            if (!success) {
                clearSession(false);
                return false;
            }

            return true;
        } catch (error) {
            clearSession(false);
            return false;
        }
    };

    useEffect(() => {
        const initializeSession = async () => {
            setLoading(true);
            const userCookie = Cookies.get("user");
            const authToken = Cookies.get("auth-token");

            if (!userCookie || !authToken) {
                clearSession(false);
                setLoading(false);
                return;
            }

            try {
                const credentials = atob(authToken).split(':');
                const res = await axiosInstance.post("user/login", {
                    userName: credentials[0],
                    password: credentials[1],
                });

                const userData = res.data.data;
                storeUserData(userData, authToken);

                const currentPath = window.location.pathname;
                if (currentPath === '/login') {
                    router.push("/dashboard");
                }
            } catch (error) {
                console.error("Session initialization error:", error);
                clearSession(false);
            } finally {
                setLoading(false);
            }
        };

        initializeSession();
    }, []);

    const login = async (userName, password) => {
        setLoading(true);

        try {
            const res = await axiosInstance.post("user/login", {
                userName: userName,
                password: password,
            });

            let userData = res.data.data;
            const authToken = btoa(`${userName}:${password}`);

            storeUserData(userData, authToken);

            router.push("/dashboard");

            toast({
                title: "Login Successfully !",
                status: "success",
                variant: "left-accent",
                position: "top-right",
                isClosable: true,
            });
        } catch (err) {
            toast({
                title: "Login Failed !",
                status: "error",
                variant: "left-accent",
                position: "top-right",
                isClosable: true,
            });
        } finally {
            setLoading(false);
        }
    };

    const logout = () => {
        clearSession(true);
    };

    return (
        <UserContext.Provider value={
            {
                user,
                login,
                logout,
                loading,
                userRole,
                checkSession,
                updateUserProfile
            }
        } > {children}
        </UserContext.Provider>
    );
};

export const useUser = () => {
    const context = useContext(UserContext);
    if (!context) {
        throw new Error("useUser must be used within a UserProvider");
    }
    return context;
};