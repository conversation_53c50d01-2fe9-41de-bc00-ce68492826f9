const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');
const { offerQuotationColumns, offerQuotationItemColumns } = require('./utils/constant');
const multer = require('multer');
const dayjs = require('dayjs');
const zlib = require('zlib');
const upload = multer({ storage: multer.memoryStorage() });

router.use(authorization);

// ------------------- Quotation/Offer (Offer) APIS ------------------- //

router.get('/tasks', async (req, res) => {
    const { getAll } = req.query;
    let query = `
        SELECT 
            o.*, 
            c.Title AS client_name, 
            o.ContactPerson AS contact_person,
            c1.add1 AS client_address, 
            c1.email AS client_email, 
            c1.Mobile AS client_mobile_no, 
            c1.tel AS client_telephone,
            emp.Title AS emp_name,
            CASE 
                WHEN o.chk_id IS NOT NULL AND r.RefVoucherNo IS NOT NULL THEN 'completed'
                WHEN o.chk_id IS NOT NULL THEN 'in-progress'
                WHEN o.chk_id IS NULL THEN 'pending'
            END AS status
        FROM 
            Offer o
        LEFT JOIN 
            Coa32 c ON o.client_id = c.id
        LEFT JOIN 
            RecoveryFollowUps r ON r.RefVoucherNo = o.Voucher_No
        LEFT JOIN 
            Coa321 c1 ON o.client_id = c1.id
        LEFT JOIN 
            EmployeeDetails emp ON emp.ID= o.EmployeeID`;
    const pool = await getPool();
    const request = new sql.Request(pool);
    if (getAll == 'true') {
        query += ' WHERE O.EmployeeID IS NOT NULL';
    } else {
        query += ' WHERE O.EmployeeID = @EmployeeID';
        request.input('EmployeeID', sql.VarChar, req.user.Emp_ID);
    }
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        if (result.recordset.length === 0) { return res.status(404).send('No tasks found.'); }
        res.status(200).json({
            message: 'Tasks fetched successfully',
            data: result.recordset
        });
    });
});

router.post('/getVoucherNo', async (req, res) => {
    const { Mnth } = req.body;
    const query = 'select MAX(vno) AS vno from Offer where Mnth = @Mnth';
    const pool = await getPool();
    const request = new sql.Request(pool);
    request.input('Mnth', sql.VarChar(4), Mnth);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json({
            vtp: 'QO',
            location: 'EAM',
            vno: result.recordset[0].vno ? result.recordset[0].vno + 1 : 1,
            Mnth,
            voucherNo: 'QO/' + Mnth + '/EAM/' + (result.recordset[0].vno ? result.recordset[0].vno + 1 : 1)
        });
    });
});

router.post('/create-pdf', upload.single('pdf'), async (req, res) => {
    const { VTP, Mnth, Location, vno } = req.body;
    const pdfFile = req.file;

    if (!pdfFile) {
        return res.status(400).send('No PDF file uploaded.');
    }

    const compressedPdf = zlib.gzipSync(pdfFile.buffer);

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        request.input('VTP', sql.VarChar(20), VTP);
        request.input('Mnth', sql.VarChar(8), Mnth);
        request.input('Location', sql.VarChar(8), Location);
        request.input('vno', sql.Int, vno);
        request.input('FileName', sql.NVarChar(255), pdfFile.originalname);
        request.input('FileData', sql.VarBinary, compressedPdf);
        request.input('UploadedAt', sql.DateTime, dayjs().toDate());

        const checkQuery = `
            SELECT FileName
            FROM Client_Lead_PDFs
            WHERE VTP = @VTP AND Mnth = @Mnth AND Location = @Location AND vno = @vno
        `;

        const checkResult = await request.query(checkQuery);

        if (checkResult.recordset.length > 0) {
            const deleteQuery = `
                DELETE FROM Client_Lead_PDFs
                WHERE VTP = @VTP AND Mnth = @Mnth AND Location = @Location AND vno = @vno
            `;
            await request.query(deleteQuery);
        }

        const insertQuery = `
            INSERT INTO Client_Lead_PDFs (VTP, Mnth, Location, vno, FileName, FileData, UploadedAt)
            VALUES (@VTP, @Mnth, @Location, @vno, @FileName, @FileData, @UploadedAt)
        `;

        await request.query(insertQuery);
        await transaction.commit();

        res.status(201).send('PDF file uploaded and saved successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.get('/get-pdf', async (req, res) => {
    const { voucher_no } = req.query;

    if (!voucher_no) {
        return res.status(400).send('voucher_no is required.');
    }

    const pool = await getPool();
    const request = new sql.Request(pool);

    request.input('voucher_no', sql.VarChar(120), voucher_no);

    const query = `
        SELECT FileName, FileData
        FROM Client_Lead_PDFs
        WHERE Voucher_No = @voucher_no
    `;

    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }

        if (result.recordset.length === 0) {
            return res.status(404).send('PDF file not found.');
        }

        const pdf = result.recordset[0];
        const decompressedPdf = zlib.gunzipSync(pdf.FileData);
        res.setHeader('Content-Disposition', `attachment; filename=${pdf.FileName}`);
        res.setHeader('Content-Type', 'application/pdf');
        res.send(decompressedPdf);
    });
});

router.post('/navigate', async (req, res) => {
    const { next, prev, first, last, goto, voucher_no } = req.body;
    const pool = await getPool();
    const request = new sql.Request(pool);
    let query = '';

    if ((next && (prev || first || last || goto)) || (prev && (first || last || goto)) || (first && (last || goto)) || (last && goto) || (!next && !prev && !first && !last && !goto)) {
        return res.status(400).json({ message: "Invalid request. Use either 'next', 'prev', 'first', 'last', or 'goto' exclusively, and provide 'voucher_no' if using 'next', 'prev', or 'goto'." });
    }

    const baseQuery = `
        SELECT TOP 1 
            Offer.*,
            client.Title AS ClientName,
            emp.Title AS EmployeeTitle,
            cc.Title AS CostCenterName,
            dp.Title AS ProjectName,
            Empl.Title AS EmployeeName
        FROM 
            Offer
        LEFT JOIN
            EmployeeDetails Empl ON Offer.EmployeeID = Empl.id
        LEFT JOIN
            Coa32 client ON Offer.client_id = client.id
        LEFT JOIN
            Coa32 emp ON Offer.Emp_id = emp.id
        LEFT JOIN
            CostCenters cc ON Offer.CostCenter_ID = cc.ID
        LEFT JOIN
            DefProjects dp ON Offer.Project_ID = dp.ID
    `;

    if (next) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'next'." });
        query = `${baseQuery}
            WHERE (Offer.VTP > (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) 
                OR (Offer.VTP = (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Mnth > (SELECT Mnth FROM Offer WHERE voucher_no = @voucher_no))
                OR (Offer.VTP = (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Mnth = (SELECT Mnth FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Location > (SELECT Location FROM Offer WHERE voucher_no = @voucher_no))
                OR (Offer.VTP = (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Mnth = (SELECT Mnth FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Location = (SELECT Location FROM Offer WHERE voucher_no = @voucher_no) AND Offer.vno > (SELECT vno FROM Offer WHERE voucher_no = @voucher_no)))
            ORDER BY Offer.VTP, Offer.Mnth, Offer.Location, Offer.vno`;
    } else if (prev) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'prev'." });
        query = `${baseQuery}
            WHERE (Offer.VTP < (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) 
                OR (Offer.VTP = (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Mnth < (SELECT Mnth FROM Offer WHERE voucher_no = @voucher_no))
                OR (Offer.VTP = (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Mnth = (SELECT Mnth FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Location < (SELECT Location FROM Offer WHERE voucher_no = @voucher_no))
                OR (Offer.VTP = (SELECT VTP FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Mnth = (SELECT Mnth FROM Offer WHERE voucher_no = @voucher_no) AND Offer.Location = (SELECT Location FROM Offer WHERE voucher_no = @voucher_no) AND Offer.vno < (SELECT vno FROM Offer WHERE voucher_no = @voucher_no)))
            ORDER BY Offer.VTP DESC, Offer.Mnth DESC, Offer.Location DESC, Offer.vno DESC`;
    } else if (first) {
        query = `${baseQuery} ORDER BY Offer.VTP, Offer.Mnth, Offer.Location, Offer.vno`;
    } else if (last) {
        query = `${baseQuery} ORDER BY Offer.VTP DESC, Offer.Mnth DESC, Offer.Location DESC, Offer.vno DESC`;
    } else if (goto) {
        if (!voucher_no) return res.status(400).json({ message: "'voucher_no' is required when using 'goto'." });
        query = `${baseQuery} WHERE Offer.Voucher_No = @voucher_no`;
    }

    if (voucher_no) {
        request.input('voucher_no', sql.VarChar, voucher_no);
    }

    request.query(query, async (err, result) => {
        if (err) return res.status(500).send(err);

        if (result.recordset.length > 0) {
            const order = result.recordset[0];
            const detailsRequest = new sql.Request(pool);
            detailsRequest.input('voucher_no', sql.VarChar, order.Voucher_No);

            const detailsQuery = `
                SELECT 
                    det.*,
                    COA.Title as itemTitle,
                    COA.Unit as itemUnit,
                    site.Title as SiteName,
                    proj.Title as DetailProjectName,
                    cc.Title as DetailCostCenterName,
                    vendor.Title as VendorName,
                    gdw.Title as GodownName,
                    job.Title as JobName,
                    loc.Title as LocationName,
                    cat.Title as CategoryName,
                    typ.Title as TypeName
                FROM 
                    Offer_det det
                    LEFT JOIN
                        Coa31 COA ON COA.id = det.Item_ID
                    LEFT JOIN
                        Coa31 site ON site.id = det.Site_ID
                    LEFT JOIN
                        DefProjects proj ON proj.ID = det.Project_ID
                    LEFT JOIN
                        CostCenters cc ON cc.ID = det.CostCenter_ID
                    LEFT JOIN
                        Coa32 vendor ON vendor.id = det.Vendor_ID
                    LEFT JOIN
                        Godown gdw ON gdw.Id = det.Godown_ID
                    LEFT JOIN
                        DefJobs job ON job.ID = det.Job_ID
                    LEFT JOIN
                        Locations loc ON loc.ID = det.OtherForLoc
                    LEFT JOIN
                        DefClientItemCategory cat ON cat.ID = det.ClientItemCategory_ID
                    LEFT JOIN
                        DefClientItemType typ ON typ.ID = det.ClientItemType_ID
                WHERE 
                    det.voucher_no = @voucher_no
            `;

            detailsRequest.query(detailsQuery, (detailsErr, detailsResult) => {
                if (detailsErr) return res.status(500).send(detailsErr);
                order["items"] = detailsResult.recordset;
                res.status(200).json(order);
            });
        } else {
            res.status(404).json({ message: 'No more records available in this direction.' });
        }
    });
});

router.get('/', async (req, res) => {
    const query = 'SELECT * FROM Offer';

    const pool = await getPool();
    const request = new sql.Request(pool);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

router.post('/getbyvoucher', async (req, res) => {
    const { voucherNo } = req.body;

    if (!voucherNo) {
        return res.status(400).send('Voucher_No is required.');
    }

    const query = `
        SELECT 
            Offer.*,
            client.Title AS ClientName,
            emp.Title AS EmployeeTitle,
            cc.Title AS CostCenterName,
            dp.Title AS ProjectName,
            Empl.Title AS EmployeeName,
            cp.*
        FROM 
            Offer
        LEFT JOIN
            EmployeeDetails Empl ON Offer.EmployeeID = Empl.id
        LEFT JOIN
            Coa32 client ON Offer.client_id = client.id
        LEFT JOIN
            Coa32 emp ON Offer.Emp_id = emp.id
        LEFT JOIN
            CostCenters cc ON Offer.CostCenter_ID = cc.ID
        LEFT JOIN
            DefProjects dp ON Offer.Project_ID = dp.ID
        LEFT JOIN
            ClientPurpose cp ON Offer.Voucher_No = cp.RefVoucherNo
        WHERE Voucher_No = @Voucher_No
    `;
    const itemQuery = `
        SELECT 
            det.*,
            COA.Title as itemTitle,
            COA.Unit as itemUnit,
            site.Title as SiteName,
            proj.Title as DetailProjectName,
            cc.Title as DetailCostCenterName,
            vendor.Title as VendorName,
            gdw.Title as GodownName,
            job.Title as JobName,
            loc.Title as LocationName,
            cat.Title as CategoryName,
            typ.Title as TypeName
        FROM 
            Offer_det det
            LEFT JOIN
                Coa31 COA ON COA.id = det.Item_ID
            LEFT JOIN
                Coa31 site ON site.id = det.Site_ID
            LEFT JOIN
                DefProjects proj ON proj.ID = det.Project_ID
            LEFT JOIN
                CostCenters cc ON cc.ID = det.CostCenter_ID
            LEFT JOIN
                Coa32 vendor ON vendor.id = det.Vendor_ID
            LEFT JOIN
                Godown gdw ON gdw.Id = det.Godown_ID
            LEFT JOIN
                DefJobs job ON job.ID = det.Job_ID
            LEFT JOIN
                Locations loc ON loc.ID = det.OtherForLoc
            LEFT JOIN
                DefClientItemCategory cat ON cat.ID = det.ClientItemCategory_ID
            LEFT JOIN
                DefClientItemType typ ON typ.ID = det.ClientItemType_ID
        WHERE voucher_no = @Voucher_No`;

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        request.input('Voucher_No', sql.VarChar(16), voucherNo);

        const result = await request.query(query);

        if (result.recordset.length === 0) {
            await transaction.rollback();
            return res.status(404).send('Quotation not found.');
        }

        const items = await request.query(itemQuery);

        await transaction.commit();
        const order = result.recordset[0];
        order['items'] = items.recordset;
        res.status(200).json(result.recordset[0]);
    } catch (err) {
        await transaction.rollback();
        res.status(500).send(err);
    }
});

router.post('/create', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);
    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        let columns = [];
        let values = [];

        offerQuotationColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                columns.push(name);
                values.push(`@${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        const orderQuery = `INSERT INTO Offer (${columns.join(', ')}) VALUES (${values.join(', ')})`;

        await request.query(orderQuery);

        const { items } = req.body;

        if (!items || items.length === 0) {
            throw new Error('No items provided.');
        }

        const itemColumnsArray = [];
        const itemValuesArray = [];
        const paramsArray = [];

        offerQuotationItemColumns.forEach(({ name }) => {
            if (!itemColumnsArray.includes(name)) {
                itemColumnsArray.push(name);
            }
        });

        items.forEach((item, index) => {
            const rowValues = [];
            itemColumnsArray.forEach((column) => {
                if (item[column] !== undefined) {
                    rowValues.push(`@${column}${index}`);
                    paramsArray.push({ name: `${column}${index}`, value: item[column] });
                } else {
                    rowValues.push('NULL');
                }
            });
            itemValuesArray.push(`(${rowValues.join(', ')})`);
        });

        const itemQuery = `
            INSERT INTO Offer_det (${itemColumnsArray.join(', ')})
            VALUES ${itemValuesArray.join(', ')};
        `;

        paramsArray.forEach(({ name, value }) => {
            request.input(name, value);
        });

        await request.query(itemQuery);

        await transaction.commit();

        res.status(201).json({
            message: 'Quotation and items successfully created.',
            vtp: req.body['VTP'],
            mnth: req.body['Mnth'],
            location: req.body['Location'],
            vno: req.body['vno']
        });
    } catch (err) {
        await transaction.rollback();

        if (err.message.includes('Cannot insert duplicate key')) {
            res.status(400).send('Voucher number already exists.');
        } else if (err.message === 'No items provided.') {
            res.status(400).send(err.message);
        } else {
            res.status(500).send(err.message);
        }
    }
});

router.put('/update', async (req, res) => {
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        const voucherNo = req.query.voucherNo;

        await transaction.begin();
        const request = new sql.Request(transaction);

        let setClause = [];

        offerQuotationColumns.forEach(({ name, type }) => {
            if (req.body[name] !== undefined && req.body[name] !== null) {
                setClause.push(`${name} = @${name}`);
                request.input(name, type, req.body[name]);
            }
        });

        if (setClause.length === 0) {
            return res.status(400).send('No fields to update.');
        }

        const QuotationQuery = `UPDATE Offer SET ${setClause.join(', ')} WHERE Voucher_No = @Voucher_No`;
        request.input('Voucher_No', sql.VarChar(50), voucherNo);

        const QuotationResult = await request.query(QuotationQuery);

        if (QuotationResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Quotation not found.');
        }

        const { items } = req.body;

        if (items && Array.isArray(items) && items.length > 0) {
            const existingItemsQuery = `
                SELECT DVoucher_No
                FROM Offer_det
                WHERE Voucher_No = @Voucher_No
            `;
            const existingItemsRequest = new sql.Request(transaction);
            existingItemsRequest.input('Voucher_No', sql.VarChar(50), voucherNo);
            const existingItemsResult = await existingItemsRequest.query(existingItemsQuery);
            const existingDVoucherNos = existingItemsResult.recordset.map(row => row.DVoucher_No);

            const itemsToUpdate = [];
            const itemsToInsert = [];
            const newDVoucherNos = new Set();
            let isError = false;
            let errorItems = [];

            items.forEach((item, i) => {
                const { VTP, Mnth, Location, vno, srno } = item;
                if (!VTP || !Mnth || !Location || !vno || !srno) {
                    isError = true;
                    return errorItems.push(i + 1);
                }

                const DVoucher_No = `${VTP}/${Mnth}/${Location}/${vno}/${srno}`;
                newDVoucherNos.add(DVoucher_No);

                if (existingDVoucherNos.includes(DVoucher_No)) {
                    itemsToUpdate.push({ ...item, DVoucher_No });
                } else {
                    itemsToInsert.push({ ...item, DVoucher_No });
                }
            });
            if (isError) {
                return res.status(400).send(`Invalid DVoucher_No format in item ${errorItems.join(', ')}. Expected format: VTP/Mnth/Location/vno/srno.`);
            }

            const itemsToDelete = existingDVoucherNos.filter(dvNo => !newDVoucherNos.has(dvNo));

            for (const item of itemsToUpdate) {
                const updateItemColumns = [];
                const updateParams = [];

                offerQuotationItemColumns.forEach(({ name, editable }) => {
                    if (item[name] !== undefined && editable) {
                        updateItemColumns.push(`${name} = @${name}`);
                        updateParams.push({ name, value: item[name] });
                    }
                });

                if (updateItemColumns.length > 0) {
                    const updateItemQuery = `
                        UPDATE Offer_det
                        SET ${updateItemColumns.join(', ')}
                        WHERE DVoucher_No = @DVoucher_No
                    `;

                    const updateRequest = new sql.Request(transaction);
                    updateRequest.input('DVoucher_No', sql.VarChar(50), item.DVoucher_No);

                    updateParams.forEach(({ name, value }) => {
                        updateRequest.input(name, value);
                    });

                    await updateRequest.query(updateItemQuery);
                }
            }

            for (const DVoucher_No of itemsToDelete) {
                const deleteItemQuery = `
                    DELETE FROM Offer_det
                    WHERE DVoucher_No = @DVoucher_No
                `;

                const deleteRequest = new sql.Request(transaction);
                deleteRequest.input('DVoucher_No', sql.VarChar(50), DVoucher_No);

                await deleteRequest.query(deleteItemQuery);
            }

            for (const item of itemsToInsert) {
                const insertItemColumns = [];
                const insertItemValues = [];
                const insertParams = [];

                offerQuotationItemColumns.forEach(({ name }) => {
                    if (item[name] !== undefined) {
                        insertItemColumns.push(name);
                        insertItemValues.push(`@${name}`);
                        insertParams.push({ name, value: item[name] });
                    }
                });

                const insertItemQuery = `
                    INSERT INTO Offer_det (${insertItemColumns.join(', ')})
                    VALUES (${insertItemValues.join(', ')})
                `;

                const insertRequest = new sql.Request(transaction);
                insertParams.forEach(({ name, value }) => {
                    insertRequest.input(name, value);
                });

                await insertRequest.query(insertItemQuery);
            }
        }

        await transaction.commit();
        res.status(200).send('Quotation and items updated successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.delete('/delete', async (req, res) => {
    const voucherNo = req.query.voucherNo;
    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);
        const deleteItemsQuery = 'DELETE FROM Offer_det WHERE Voucher_No = @Voucher_No';
        request.input('Voucher_No', sql.VarChar(16), voucherNo);
        const deleteItemsResult = await request.query(deleteItemsQuery);
        const deleteOrderQuery = 'DELETE FROM Offer WHERE Voucher_No = @Voucher_No';
        const deleteOrderResult = await request.query(deleteOrderQuery);
        if (deleteItemsResult.rowsAffected[0] === 0 && deleteOrderResult.rowsAffected[0] === 0) {
            await transaction.rollback();
            return res.status(404).send('Quotation not found or already deleted.');
        }
        await transaction.commit();
        res.status(200).send('Quotation and associated items deleted successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.post('/store-file', upload.single('file'), async (req, res) => {
    const { timestamp, location, googleMapsLink, type, refVoucherNo, refVTP, refMnth, refLocation, refVNo } = req.body;

    if (!req.file || !timestamp || !location || !type || !refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {
        return res.status(400).send('All fields (file, timestamp, location, type, refVoucherNo, refVTP, refMnth, refLocation, refVNo) are required.');
    }

    const locationObj = JSON.parse(location);
    const compressedFile = zlib.gzipSync(req.file.buffer);

    const pool = await getPool();
    const request = new sql.Request(pool);

    try {
        const query = `
            INSERT INTO OfferFileData (FileName, FileData, Timestamp, Latitude, Longitude, GoogleMapsLink, Type, RefVoucherNo, RefVTP, RefMnth, RefLocation, RefVNo, RoleID)
            VALUES (@FileName, @FileData, @Timestamp, @Latitude, @Longitude, @GoogleMapsLink, @Type, @RefVoucherNo, @RefVTP, @RefMnth, @RefLocation, @RefVNo, @RoleID)
        `;

        request.input('FileName', sql.NVarChar, req.file.originalname);
        request.input('FileData', sql.VarBinary, compressedFile);
        request.input('Timestamp', sql.DateTime, new Date(timestamp));
        request.input('Latitude', sql.Float, locationObj.lat);
        request.input('Longitude', sql.Float, locationObj.lng);
        request.input('GoogleMapsLink', sql.NVarChar(sql.MAX), googleMapsLink || null);
        request.input('Type', sql.VarChar(10), type);
        request.input('RefVoucherNo', sql.VarChar(70), refVoucherNo);
        request.input('RefVTP', sql.VarChar(20), refVTP);
        request.input('RefMnth', sql.VarChar(8), refMnth);
        request.input('RefLocation', sql.VarChar(8), refLocation);
        request.input('RefVNo', sql.Int, refVNo);
        request.input('RoleID', sql.Int, req.user.RoleID);

        await request.query(query);
        res.status(201).send('File data stored successfully.');
    } catch (err) {
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.get('/get-files', async (req, res) => {
    const { refVoucherNo } = req.query;

    if (!refVoucherNo) {
        return res.status(400).send('refVoucherNo is required.');
    }

    const pool = await getPool();
    const request = new sql.Request(pool);

    try {
        let query = `
            SELECT FileName, FileData, Timestamp, Latitude, Longitude, GoogleMapsLink, Type, RefVoucherNo, RefVTP, RefMnth, RefLocation, RefVNo, U.RoleName AS Role
            FROM OfferFileData O
            LEFT JOIN User_Role U ON U.RoleID = O.RoleID
            WHERE RefVoucherNo = @RefVoucherNo
        `;

        if (req.user.RoleID !== 1) {
            query = `
                SELECT FileName, FileData, Timestamp, Latitude, Longitude, GoogleMapsLink, Type, RefVoucherNo, RefVTP, RefMnth, RefLocation, RefVNo
                FROM OfferFileData
                WHERE RefVoucherNo = @RefVoucherNo  AND RoleID = @RoleID
            `;
            request.input('RoleID', sql.Int, req.user.RoleID);
        }

        request.input('RefVoucherNo', sql.VarChar(70), refVoucherNo);

        const result = await request.query(query);

        const files = result.recordset.map(file => ({
            ...file,
            FileData: zlib.gunzipSync(file.FileData)
        }));

        res.status(200).json(files);
    } catch (err) {
        res.status(500).send('An error occurred: ' + err.message);
    }
});

router.post('/store-signature', upload.single('file'), async (req, res) => {
    const { timestamp, location, googleMapsLink, refVoucherNo, refVTP, refMnth, refLocation, refVNo } = req.body;

    if (!req.file || !refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {
        return res.status(400).send('Required fields missing (file, refVoucherNo, refVTP, refMnth, refLocation, refVNo).');
    }

    const locationObj = location ? JSON.parse(location) : { lat: 0, lng: 0 };
    const compressedFile = zlib.gzipSync(req.file.buffer);

    const pool = await getPool();
    const transaction = new sql.Transaction(pool);

    try {
        await transaction.begin();
        const request = new sql.Request(transaction);

        const checkQuery = `
            SELECT ID FROM OfferFileData 
            WHERE RefVoucherNo = @RefVoucherNo 
            AND Type = 'signature'
        `;

        request.input('RefVoucherNo', sql.VarChar(70), refVoucherNo);
        const checkResult = await request.query(checkQuery);

        if (checkResult.recordset.length > 0) {
            const deleteQuery = `
                DELETE FROM OfferFileData
                WHERE RefVoucherNo = @RefVoucherNo
                AND Type = 'signature'
            `;
            await request.query(deleteQuery);
        }

        const insertQuery = `
            INSERT INTO OfferFileData (FileName, FileData, Timestamp, Latitude, Longitude, GoogleMapsLink, Type, RefVoucherNo, RefVTP, RefMnth, RefLocation, RefVNo)
            VALUES (@FileName, @FileData, @Timestamp, @Latitude, @Longitude, @GoogleMapsLink, @Type, @RefVoucherNo, @RefVTP, @RefMnth, @RefLocation, @RefVNo)
        `;

        request.input('FileName', sql.NVarChar, 'signature.png');
        request.input('FileData', sql.VarBinary, compressedFile);
        request.input('Timestamp', sql.DateTime, timestamp ? new Date(timestamp) : new Date());
        request.input('Latitude', sql.Float, locationObj.lat);
        request.input('Longitude', sql.Float, locationObj.lng);
        request.input('GoogleMapsLink', sql.NVarChar(sql.MAX), googleMapsLink || null);
        request.input('Type', sql.VarChar(10), 'image');
        request.input('RefVTP', sql.VarChar(20), refVTP);
        request.input('RefMnth', sql.VarChar(8), refMnth);
        request.input('RefLocation', sql.VarChar(8), refLocation);
        request.input('RefVNo', sql.Int, refVNo);

        await request.query(insertQuery);
        await transaction.commit();

        res.status(201).send('Signature stored successfully.');
    } catch (err) {
        await transaction.rollback();
        res.status(500).send('An error occurred: ' + err.message);
    }
});

module.exports = router;
