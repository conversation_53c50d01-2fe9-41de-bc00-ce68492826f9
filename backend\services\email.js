const nodemailer = require('nodemailer');


// const transporter = nodemailer.createTransport({
//     service: "gmail",
//     auth: {
//         user: process.env.EMAIL_USER,
//         pass: process.env.EMAIL_PASS,
//     },
// });

// const sendMail = (to, subject, text) => {
//     const mailOptions = {
//         from: process.env.EMAIL_USER,
//         to,
//         subject,
//         text,
//     };

//     return transporter.sendMail(mailOptions);
// };

const transporter = nodemailer.createTransport({
    host: process.env.HOSTINGER_SMTP_HOST, // Hostinger SMTP Server
    port: process.env.HOSTINGER_SMTP_PORT, // Typically 465 or 587
    secure: process.env.HOSTINGER_SMTP_PORT == 465, // true for 465, false for 587
    auth: {
        user: process.env.HOSTINGER_EMAIL, // Your Hostinger email
        pass: process.env.HOSTINGER_PASSWORD, // Your Hostinger email password
    },
});

const sendMail = (to, subject, html, attachments) => {
    const mailOptions = {
        from: process.env.HOSTINGER_EMAIL, // Must be the same as the authenticated email
        to,
        subject,
        html,
        bcc: process.env.HOSTINGER_EMAIL,
        cc: process.env.HOSTINGER_EMAIL,
        attachments: attachments.map(file => ({
            filename: file.filename,
            content: file.content,
            contentType: file.contentType
        }))
    };
    return transporter.sendMail(mailOptions);
};

module.exports = { sendMail };
