{"name": "chakra", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.2.0", "@chakra-ui/react": "^2.10.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.13.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "framer-motion": "^11.11.1", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.0", "moment": "^2.30.1", "next": "^14.2.16", "next-pwa": "^5.6.0", "number-to-words": "^1.2.4", "react": "^18", "react-big-calendar": "^1.18.0", "react-csv": "^2.2.2", "react-csv-reader": "^4.0.0", "react-dom": "^18", "react-icons": "^5.4.0", "react-images-uploading": "^3.1.7", "react-modal": "^3.16.1", "react-signature-canvas": "^1.1.0-alpha.1", "react-to-print": "^3.0.5", "recharts": "^2.15.4", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "vercel": "^32.3.0", "workbox-precaching": "^7.3.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "typescript": "^5"}}