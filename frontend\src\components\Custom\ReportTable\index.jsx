import React, { useState } from 'react';
import {
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    Badge,
    Box,
    Input,
    Button,
    HStack,
    Select,
} from '@chakra-ui/react';
import dayjs from 'dayjs';

const ReportTable = ({
    data,
    columns,
    onRowClick,
    getRowCursor,
    getBadgeColor,
    dateField = 'createdAt',
    sortField = 'creationDate',
    showDateFilter = true,
}) => {
    const [searchText, setSearchText] = useState('');
    const [fromDate, setFromDate] = useState('');
    const [toDate, setToDate] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [tasksPerPage, setTasksPerPage] = useState(20);

    const filterData = (items) => {
        return items.sort((a, b) => new Date(b[sortField]) - new Date(a[sortField])).filter(item => {
            const matchesSearchText = Object.values(item).some(value =>
                value?.toString().toLowerCase().includes(searchText.toLowerCase())
            );
            const matchesDateRange = !showDateFilter || (
                (!fromDate || new Date(item[dateField]) >= new Date(fromDate)) &&
                (!toDate || new Date(item[dateField]) <= new Date(toDate))
            );
            return matchesSearchText && matchesDateRange;
        });
    };

    const sortedData = filterData(data);
    const totalPages = Math.ceil(sortedData.length / tasksPerPage);
    const indexOfLastItem = currentPage * tasksPerPage;
    const indexOfFirstItem = indexOfLastItem - tasksPerPage;
    const currentItems = sortedData.slice(indexOfFirstItem, indexOfLastItem);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);
    const paginateNext = () => currentPage < totalPages && setCurrentPage(currentPage + 1);
    const paginatePrev = () => currentPage > 1 && setCurrentPage(currentPage - 1);

    return (
        <Box className="card card-round mt-4" boxShadow={'none !important'}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 5 }} className="p-3">
                <Box width={'100%'}>
                    <h6>Search</h6>
                    <Input 
                        type='text' 
                        name='search' 
                        value={searchText} 
                        onChange={(e) => setSearchText(e.target.value)} 
                        placeholder="Search in all columns..."
                    />
                </Box>
                {showDateFilter && (
                    <>
                        <Box width={'100%'}>
                            <h6>From</h6>
                            <Input 
                                type='date' 
                                name='from-datetime' 
                                value={fromDate} 
                                onChange={(e) => setFromDate(e.target.value)} 
                            />
                        </Box>
                        <Box width={'100%'}>
                            <h6>To</h6>
                            <Input 
                                type='date' 
                                name='to-datetime' 
                                value={toDate} 
                                onChange={(e) => setToDate(e.target.value)} 
                            />
                        </Box>
                    </>
                )}
            </Box>
            <Box className="card-body" boxShadow={'none !important'}>
                <TableContainer>
                    <Table variant='simple'>
                        <Thead style={{ background: "#071533" }}>
                            <Tr>
                                {columns.map((column, index) => (
                                    <Th key={index} style={{ color: "white" }}>{column.header}</Th>
                                ))}
                            </Tr>
                        </Thead>
                        <Tbody>
                            {currentItems.length > 0 ? currentItems.map((item, rowIndex) => (
                                <Tr
                                    key={rowIndex}
                                    onClick={() => onRowClick && onRowClick(item)}
                                    style={{ cursor: getRowCursor ? getRowCursor(item) : 'default' }}
                                    sx={{
                                        transition: 'all .3s',
                                        '&:hover': {
                                            background: '#0403292c'
                                        }
                                    }}
                                >
                                    {columns.map((column, colIndex) => (
                                        <Td key={colIndex}>
                                            {column.type === 'badge' ? (
                                                column.render ? (
                                                    (() => {
                                                        const badge = column.render(item);
                                                        return (
                                                            <Badge colorScheme={badge.color}>
                                                                {badge.label}
                                                            </Badge>
                                                        );
                                                    })()
                                                ) : (
                                                    <Badge colorScheme={getBadgeColor(item[column.field])}>
                                                        {item[column.field]}
                                                    </Badge>
                                                )
                                            ) : column.type === 'date' ? (
                                                item[column.field] ? dayjs(item[column.field]).format('DD MMM, YYYY hh:mm:ss A') : 'N/A'
                                            ) : column.render ? (
                                                column.render(item)
                                            ) : (
                                                item[column.field] ? item[column.field] : 'N/A'
                                            )}
                                        </Td>
                                    ))}
                                </Tr>
                            )) : (
                                <Tr>
                                    <Td colSpan={columns.length} textAlign={'center'}>No Data Found</Td>
                                </Tr>
                            )}
                        </Tbody>
                    </Table>
                </TableContainer>
                <Box mt={4}>
                    <HStack spacing={2} justifyContent="space-between" alignItems="center">
                        <Box>
                            <Select
                                value={tasksPerPage}
                                onChange={(e) => {
                                    setTasksPerPage(Number(e.target.value));
                                    setCurrentPage(1);
                                }}
                                size="sm"
                                width="120px"
                            >
                                <option value={10}>10 per page</option>
                                <option value={20}>20 per page</option>
                                <option value={50}>50 per page</option>
                                <option value={100}>100 per page</option>
                            </Select>
                        </Box>
                        <Box display={'flex'} gap={2}>
                            <Button onClick={paginatePrev} disabled={currentPage === 1}>
                                &laquo;
                            </Button>
                            {Array.from({ length: totalPages }, (_, index) => (
                                <Button
                                    key={index + 1}
                                    onClick={() => paginate(index + 1)}
                                    colorScheme={currentPage === index + 1 ? 'blue' : 'gray'}
                                >
                                    {index + 1}
                                </Button>
                            ))}
                            <Button onClick={paginateNext} disabled={currentPage === totalPages}>
                                &raquo;
                            </Button>
                        </Box>
                    </HStack>
                </Box>
            </Box>
        </Box>
    );
};

export default ReportTable; 