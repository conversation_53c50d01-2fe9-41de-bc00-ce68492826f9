"use client";
import { DeleteIcon, EditIcon } from "@chakra-ui/icons";
import { Box, IconButton, Link, Divider, <PERSON>lt<PERSON> } from "@chakra-ui/react";
import { useState } from "react";
import { BiCheckCircle, BiEraser, <PERSON>iPrinter } from "react-icons/bi";
import { FiSave, FiChevronRight, FiChevronLeft } from "react-icons/fi";
import { ImCancelCircle, ImFirst, ImLast, ImNext2, ImPrevious2 } from "react-icons/im";
import { VscGoToFile } from "react-icons/vsc";

const Toolbar = ({
  save = () => { },
  edit = () => { },
  clear = () => { },
  remove = () => { },
  cancel = () => { },
  check = () => { },
  first = () => { },
  previous = () => { },
  next = () => { },
  goto = () => { },
  last = () => { },
  print = () => { },
  isNavigation = false,
  isEdit = false,
  hide = ["Goto", "Cancel"]
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const toggleToolbar = () => setIsOpen(!isOpen);

  const buttons = [
    { icon: <FiSave />, label: "Save", action: save, disabled: !isEdit && isNavigation },
    { icon: <EditIcon />, label: "Edit", action: edit, disabled: !isNavigation },
    { icon: <BiEraser />, label: "Clear", action: clear, disabled: false },
    { icon: <DeleteIcon />, label: "Delete", action: remove, disabled: !isNavigation },
    { icon: <ImCancelCircle />, label: "Cancel", action: cancel, disabled: isEdit },
    { icon: <BiCheckCircle />, label: "Check", action: check, disabled: isEdit },
    { icon: <ImFirst />, label: "First", action: first, disabled: isEdit },
    { icon: <ImPrevious2 />, label: "Previous", action: previous, disabled: isEdit },
    { icon: <ImNext2 />, label: "Next", action: next, disabled: isEdit },
    { icon: <ImLast />, label: "Last", action: last, disabled: isEdit },
    { icon: <VscGoToFile />, label: "Goto", action: goto, disabled: isEdit },
    { icon: <BiPrinter />, label: "Print", action: print, disabled: !isNavigation },
  ];

  const iconButtonStyle = {
    background: "#3182CE",
    color: "white",
    fontSize: "20px",
    mt: 2,
    _hover: { background: "#3182CE" },
  };

  return (
    <Box
      position="fixed"
      top="50%"
      right="0"
      transform="translateY(-50%)"
      zIndex="2"
      w={isOpen ? "60px" : "40px"}
      textAlign="center"
      backgroundColor="#10275a"
      className="toolbar"
    >
      <IconButton
        style={{ width: "100%" }}
        aria-label={isOpen ? "Close toolbar" : "Open toolbar"}
        icon={isOpen ? <FiChevronRight /> : <FiChevronLeft />}
        onClick={toggleToolbar}
        background="#10275a"
        borderRadius="0"
        color="white"
        size="lg"
        _hover={{ background: "#071533" }}
      />
      {isOpen && (
        <Box display="flex" flexDirection="column" alignItems="center">
          {buttons.map((btn, index) => {
            if (hide.includes(btn.label)) return <></>;
            return (<Box key={index} style={{ width: "100%" }} className="toolbarMenu">
              <Divider style={{ borderBottomColor: "white !important", margin: "0" }} />
              <Tooltip label={btn.label} placement="left" hasArrow>
                <IconButton
                  icon={btn.icon}
                  isDisabled={btn.disabled}
                  onClick={btn.disabled ? undefined : btn.action}
                  aria-label={btn.label}
                  {...iconButtonStyle}
                />
              </Tooltip>
            </Box>)
          })}
        </Box>
      )}
    </Box>
  );
};

export default Toolbar;
