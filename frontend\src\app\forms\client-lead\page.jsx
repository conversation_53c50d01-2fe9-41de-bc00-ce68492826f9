"use client";
import React, { useEffect, useState, useRef } from "react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import TableComboBox from "@components/Custom/TableComboBox/TableComboBox";
import AanzaDataTable from "@components/Custom/AanzaDataTable/AanzaDataTable";
import ConfirmDialog from "@components/Custom/ConfirmDialog/ConfirmDialog";
import NumberInput  from "@components/Custom/NumberInput/NumberInput";
import axiosInstance from "../../axios";
import Toolbar from "@components/Toolbar/Toolbar";
import { Button, Select, useToast } from "@chakra-ui/react";
import { Box, FormControl, FormLabel, Input, Textarea } from "@chakra-ui/react";
import {
  formatDate,
  getData,
  validateArrayFields,
  validateObjectFields,
} from "@utils/functions";
import Loader from "@components/Loader/Loader";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import dayjs from "dayjs";
import AddClientModal from '@src/components/Custom/Modal/AddClientModal';
import ComponentToPrint from "./ComponentToPrint";
import PrintModal from "@components/PrintModal";
import { useUser } from "@src/app/provider/UserContext";

const QuotationFollowupRequiredFields = [
  // "Dated",
  // "VTP",
  // "Mnth",
  // "Location",
  // "vno",
  // "Currency_ID",
  // "ExchRate",
  // "Party_ref",
  // "AttentionPerson",
  // "AttentionPerson_Desig",
  // "Subject",
  // "GrdTotalPSTAmt",
  // "Freight",
  // "NetAmount",
  // "Terms",
  // "StartingComments",
  // "client_id",
];

const QuotationFollowupItemsRequiredFields = [
  // "Dated",
  // "VTP",
  // "Mnth",
  // "Location",
  // "vno",
  // "srno",
  // "item_id",
  // "Rate",
  // "Discount",
  // "Total",
  // "VisitDate",
  // "details",
  // "Qty",
];

const QuotationFollowupHeaders = [
  {
    label: "Item ID",
    key: "Item_ID",
    width: "200px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Item Description",
    key: "Item_Title",
    width: "350px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Qty",
    key: "Qty",
    width: "100px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Rate",
    key: "Rate",
    width: "100px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Disc %",
    key: "Disc",
    width: "100px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Installation Charges",
    key: "InstallationCharges",
    width: "200px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Total",
    key: "Total",
    width: "100px",
    isReadOnly: true,
    type: "number",
  },
  {
    label: "Delivery Date",
    key: "Date",
    width: "200px",
    isReadOnly: false,
    type: "date",
  },
  {
    label: "Details",
    key: "Details",
    width: "350px",
    isReadOnly: false,
    type: "text",
  },
];
const createQuotationFollowupEmptyTableRow = () => [
  {
    Item_ID: "",
    Item_Title: "",
    Qty: 0,
    Rate: 0,
    InstallationCharges: 0,
    Disc: 0,
    Total: 0,
    Date: "",
    Details: "",
  },
];

const createQuotationFollowupInitialFormData = () => ({
  location: "",
  vno: "",
  vtp: "",
  voucherNo: "",
  date: "",
  mnth: "",
  tenderNo: "",
  exchangeRate: 0,
  partyRef: "",
  attentionPerson: "",
  designation: "",
  contactPerson: "",
  email: "",
  phoneNumber: "",
  landline: "",
  address: "",
  lead: "",
  clientId: "",
  clientTitle: "",
  currency: "",
  subject: "",
  quotation: "",
  totalAmount: 0,
  freight: 0,
  netAmount: 0,
  validityDays: 0,
  paymentTerms: "",
  narration: "",
  salesTaxR: "",
  salesTaxA: "",
  discountPercent: "",
  discountAmount: 0,
  netPayableAmt: 0,
  sTaxAmount: 0,
  assignerId: "",
  assignerTitle: "",
  assessmentTime: "",
  assignerLocation: "",
});

const QuotationFollowupCalculationFields = [
  "salesTaxR",
  "salesTaxA",
  "netAmount",
  "discountPercent",
  "discountAmount",
  "netPayableAmt",
  "sTaxAmount",
  "totalAmount",
  "freight",
];

const questions = [
  "What is the Lead Generator Company Name?",
  "I gave the consumer all other information they needed to make an informed decision. This includes making sure they knew any estimated prices were not final quotes.",
  "I gave the consumer the required information about the upgrade This includes the name of the accredited person creating certificates for the upgrade.",
  "I gave the consumer the required information about the VEU program. This includes making sure the consumer knew I did not work for the Victorian Government and would not supply products on behalf of the Victorian Government.",
  "I did not use high pressure tactics e.g bullying, pressuring or manipulating people; acting in aggressive manner; asking irrelevant or unreasonable questions; etc.",
  "I wore appropriate identification that was clearly displayed at all times."
];

const QuotationOffer = () => {
  const [isAddClientModalOpen, setIsAddClientModalOpen] = useState(false);
  const toast = useToast();
  const { user } = useUser();
  const searchParams = useSearchParams();
  const [clients, setClients] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [items, setItems] = useState([]);
  const [salesMan, setSalesMan] = useState([]);
  const [isNavigation, setIsNavigation] = useState(false);
  const [tableData, setTableData] = useState(
    createQuotationFollowupEmptyTableRow()
  );
  const [formData, setFormData] = useState(
    createQuotationFollowupInitialFormData()
  );
  const [loading, setLoading] = useState(false);
  const [printData, setPrintData] = useState({});
  const [isDialogOpen, setDialogOpen] = useState(false);
  const [isPrintModalOpen, setPrintModalOpen] = useState(false);

  const handlePrint = async () => {
    try {
      const data = {
        ...formData,
        items: tableData,
      };
      setPrintData(data);
      setPrintModalOpen(true);
    } catch (error) {
      console.error(error);
    }
  };

  const handleCreatePdf = async (pdfBlob) => {
    const newFormData = new FormData();
    newFormData.append("pdf", pdfBlob);
    newFormData.append("VTP", formData.vtp);
    newFormData.append("Mnth", formData.mnth);
    newFormData.append("Location", formData.location);
    newFormData.append("vno", formData.vno);

    try {
      await axiosInstance.post("offer/create-pdf", newFormData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      toast({
        title: "PDF created and uploaded successfully.",
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      console.error("Error uploading PDF:", error);
      toast({
        title: "Error uploading PDF.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;

      if (QuotationFollowupCalculationFields.includes(name)) {
        setFormData((prev) => {
          const salesTaxR =
            name === "salesTaxR" ? Number(value) : Number(prev.salesTaxR);
          const salesTaxA =
            name === "salesTaxA" ? Number(value) : Number(prev.salesTaxA);
          const freight =
            name === "freight" ? Number(value) : Number(prev.freight);
          const discountPercent =
            name === "discountPercent" ? value : prev.discountPercent;
          let discountAmount =
            name === "discountAmount" ? value : prev.discountAmount;
          const totalAmount = prev.totalAmount;
          let sTaxAmount = prev.sTaxAmount;
          let netAmount = prev.netAmount;

          if (salesTaxR + salesTaxA > 100) {
            sTaxAmount = 0;
          } else {
            const totalPercentage = (salesTaxR + salesTaxA) / 100;
            sTaxAmount = totalAmount * totalPercentage;
          }
          if (name !== "netAmount") {
            netAmount = totalAmount + sTaxAmount;
          }

          discountAmount = (discountPercent / 100) * netAmount;
          const netPayableAmt =
            netAmount + freight - discountAmount;

          return {
            ...prev,
            [name]: value,
            salesTaxR,
            salesTaxA,
            sTaxAmount,
            discountAmount,
            totalAmount,
            netAmount,
            netPayableAmt,
          };
        });
      } else {
        setFormData((prev) => ({ ...prev, [name]: value }));
      }
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const transformData = (
    orderData = {},
    itemsArray,
    isNavigationdata = false
  ) => {
    if (isNavigationdata) {
      return itemsArray.map((item) => {
        return {
          Item_ID: item?.item_id,
          Item_Title: item?.itemTitle,
          Rate: Number(item?.Rate),
          InstallationCharges: Number(item?.InstallationCharges),
          Disc: Number(item?.Discount),
          Total: Number(item?.Total),
          Date: formatDate(new Date(item?.VisitDate), true),
          Details: item?.details,
          Qty: Number(item?.Qty),
        };
      });
    } else {
      return itemsArray.map((item, index) => {
        return {
          Dated: orderData.Dated,
          VTP: orderData.VTP,
          Mnth: orderData.Mnth,
          Location: orderData.Location,
          vno: orderData.vno,
          srno: index + 1,
          item_id: item.Item_ID,
          Rate: Number(item.Rate),
          InstallationCharges: Number(item.InstallationCharges),
          Discount: Number(item.Disc),
          Total: Number(item.Total),
          VisitDate: item.Date,
          details: item.Details,
          Qty: Number(item.Qty),
        };
      });
    }
  };
  const rowClickHandler = (data, rowIndex, colIndex) => {
    const isExist = tableData.find((modal) => modal.Item_ID === data.Item_ID);
    if (isExist) {
      setTableData((prev) => {
        const updatedTableData = prev.map((item) => {
          if (item.Item_ID === isExist.Item_ID) {
            return {
              ...item,
              qty: item.qty ? Number(item.qty) + 1 : 1,
            };
          }
          return item;
        });
        return updatedTableData;
      });
    } else {
      setTableData((prev) => {
        const updatedTableData = [...prev];
        updatedTableData[rowIndex] = {
          ...updatedTableData[rowIndex],
          Item_ID: data.Item_ID ? data.Item_ID : "",
          Item_Title: data.Item_Title ? data.Item_Title : "",
          unit: data.unit ? data.unit : "",
        };
        return updatedTableData;
      });
    }
  };

  const cellRender = (
    value,
    key,
    rowIndex,
    colIndex,
    cellData,
    handleInputChange
  ) => {
    if (["Item_ID", "Item_Title", "unit"].includes(key)) {
      return (
        <TableComboBox
          rowIndex={rowIndex}
          colIndex={colIndex}
          inputWidth={cellData.width}
          value={value}
          onChange={(val) => handleInputChange(val)}
          modalData={items}
          modalHeaders={["ID", "Title"]}
          isDisabled={isDisabled}
          rowClickHandler={rowClickHandler}
        />
      );
    }
    if (cellData.type === "number") {
      return (
        <NumberInput
          width={cellData.width}
          value={value}
          onChange={(e) => handleInputChange(e.target.value)}
          size="sm"
          isReadOnly={cellData.isReadOnly}
          isDisabled={isDisabled}
        />
      );
    }
    return (
      <Input
        width={cellData.width}
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        size="sm"
        type={cellData.type}
        isReadOnly={cellData.isReadOnly}
        isDisabled={isDisabled}
      />
    );
  };

  const calculation = (header, value, rowIndex) => {
    setTableData((prevData) => {
      return prevData.map((r, i) => {
        if (i === rowIndex) {
          const updatedRow = { ...r, [header.key]: value };
          const qty = header.key === "Qty" ? value : r.Qty;
          const rate = header.key === "Rate" ? value : r.Rate;
          const installationCharges = header.key === "InstallationCharges" ? value : r.InstallationCharges;
          const discountPercent = header.key === "Disc" ? value : r.Disc;
          const total = (Number(qty) || 0) * (Number(rate) || 0);
          const discountAmount = (discountPercent / 100) * total;
          updatedRow.Total = total - discountAmount + (Number(installationCharges) || 0);
          return updatedRow;
        }
        return r;
      });
    });
  };

  useEffect(() => {
    const lead = searchParams.get("lead");
    if (lead) {
      setFormData((prev) => ({ ...prev, lead }));
    }
  }, [searchParams]);

  const fetchClientData = async () => {
    if (formData.clientId) {
      const { data } = await getData(`client/${formData.clientId}`);
      setFormData((prev) => ({
        ...prev,
        // contactPerson: data?.title,
        email: data?.email,
        phoneNumber: data?.Mobile,
        landline: data?.tel,
        address: data?.add1,
      }));
    }
  };

  useEffect(() => {
    fetchClientData();
  }, [formData.clientId]);

  useEffect(() => {
    let totalQty = 0;
    let totalAmount = 0;

    tableData.forEach((data) => {
      totalAmount += Number(data.Total) || 0;
    });

    const salesTaxR = formData.salesTaxR;
    const salesTaxA = formData.salesTaxA;
    const freight = formData.freight;
    const discountPercent = formData.discountPercent;
    let discountAmount = formData.discountAmount;
    let sTaxAmount = formData.sTaxAmount;
    let netAmount = formData.netAmount;

    if (salesTaxR + salesTaxA > 100) {
      sTaxAmount = 0;
    } else {
      const totalPercentage = (salesTaxR + salesTaxA) / 100;
      sTaxAmount = totalAmount * totalPercentage;
    }

    if (name !== "netAmount") {
      netAmount = totalAmount + sTaxAmount;
    }

    discountAmount = (discountPercent / 100) * netAmount;
    const netPayableAmt = netAmount + freight - discountAmount;

    setFormData((prev) => ({
      ...prev,
      totalQty,
      totalAmount,
      netAmount,
      netPayableAmt,
      sTaxAmount,
      discountAmount,
    }));
  }, [tableData]);

  const getVoucherNo = async (date) => {
    if (date) {
      setLoading(true);
      const year = new Date(date).getFullYear();
      try {
        const response = await axiosInstance.post("offer/getVoucherNo", {
          Mnth: year.toString(),
        });
        const { location, vno, vtp, Mnth, voucherNo } = response.data || {};
        if ((location, vno, vtp, Mnth, voucherNo)) {
          setFormData((prevFormData) => ({
            ...prevFormData,
            location,
            vno,
            vtp,
            Mnth,
            voucherNo,
            mnth: Mnth,
          }));
          setLoading(false);
          toast({
            title: "Voucher No. Generated Successfully !",
            status: "success",
            variant: "left-accent",
            position: "top-right",
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("Error fetching voucher number:", error);
        setLoading(false);
      }
    } else {
      toast({
        title: "Please Select a Date First.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const loadInitialData = async () => {
    const clientsData = await getData("getRecords/clients");
    const currenciesData = await getData("getRecords/currencies");
    const items = await getData("getRecords/items");
    const salesManData = await getData("getRecords/assessors");
    const itemData = [];
    items.map((item) => {
      itemData.push({
        Item_ID: item.id,
        Item_Title: item.Title,
      });
    });
    setSalesMan(salesManData);
    setItems(itemData);
    setClients(clientsData);
    setCurrencies(currenciesData);
  };

  // Toolbar funtions starts here

  const handleSave = () => {
    const data = {
      Dated: formData.date ? formatDate(formData.date) : null,
      VTP: formData.vtp,
      Mnth: formData.mnth,
      Location: formData.location,
      vno: formData.vno,
      Currency_ID: formData.currency,
      Validity: formData.validityDays,
      // ExchRate: formData.exchangeRate,
      // Party_ref: formData.partyRef,
      Lead: formData.lead,
      EmployeeID: formData.assignerId,
      AssessmentTime: formData.assessmentTime
        ? formatDate(formData.assessmentTime)
        : null,
      AssessmentLocation: formData.assignerLocation,
      TenderNo: formData.tenderNo,
      SalesTaxA: formData.salesTaxA,
      SalesTaxR: formData.salesTaxR,
      DiscountPercent: formData.discountPercent,
      Discount: formData.discountAmount,
      GrossAmount: formData.netPayableAmt,
      ContactPerson: formData.contactPerson,
      // AttentionPerson: formData.attentionPerson,
      // AttentionPerson_Desig: formData.designation,
      Subject: formData.subject,
      Quotation: formData.quotation,
      GrdTotalPSTAmt: formData.totalAmount,
      prp_id: user.userName,
      Freight: formData.freight,
      NetAmount: formData.netAmount,
      Terms: formData.paymentTerms,
      StartingComments: formData.narration,
      client_id: formData.clientId,
      CreationDate: formatDate(new Date()),
      items: transformData(
        {
          Dated: formatDate(formData.date),
          VTP: formData.vtp,
          Mnth: formData.mnth,
          Location: formData.location,
          vno: formData.vno,
        },
        tableData
      ),
    };
    const isValidateObjectFields = validateObjectFields(
      data,
      QuotationFollowupRequiredFields
    );
    const isValidateArrayFields = validateArrayFields(
      data.items,
      QuotationFollowupItemsRequiredFields
    );
    if (isValidateObjectFields.error) {
      toast({
        title: isValidateObjectFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateArrayFields.error) {
      toast({
        title: isValidateArrayFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {
      setLoading(true);
      if (isEdit) {
        const voucherNo = formData.voucherNo;
        axiosInstance({
          method: "put",
          url: `offer/update?voucherNo=${voucherNo}`,
          data: data,
        })
          .then((response) => {
            editForm();
            toast({
              title: "Quotation Followup Form modified successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error modifing Quotation Followup Form:", error);
            toast({
              title: "Error modifing Quotation Followup Form :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          });
      } else {
        axiosInstance({
          method: "post",
          url: "offer/create",
          data: data,
        })
          .then((response) => {
            clearForm();
            toast({
              title: "Quotation Followup Form saved successfully :)",
              status: "success",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          })
          .catch((error) => {
            console.error("Error saving Quotation Followup Form:", error);
            toast({
              title: "Error saving Quotation Followup Form :(",
              status: "error",
              variant: "left-accent",
              position: "top-right",
              isClosable: true,
            });
            setLoading(false);
          });
      }
    } else {
      toast({
        title:
          "Please fill out all required fields and ensure all items have valid fields",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const handleCheck = () => {
    const voucherNo = formData.voucherNo;
    axiosInstance({
      method: "put",
      url: `offer/update?voucherNo=${voucherNo}`,
      data: {
        chk_id: user.userName,
        Chk_Date: formatDate(new Date()),
      },
    })
      .then(() => {
        toast({
          title: "Quotation Followup Checked successfully :)",
          status: "success",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error while checking Followup Form:", error);
        toast({
          title: "Error while checking Quotation Followup Form :(",
          status: "error",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        setLoading(false);
      });
  };

  const handleDelete = async () => {
    const voucherNo = formData.voucherNo;
    if (!voucherNo) return;
    setLoading(true);
    try {
      await axiosInstance.delete(`offer/delete?voucherNo=${voucherNo}`);
      setLoading(false);
      clearForm();
      toast({
        title: formData.voucherNo + " voucher deleted successfully",
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: error.response.data || "Quotation Followup Error",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      console.error("Error navigating to voucher form:", error);
      setLoading(false);
    }
  };

  const clearForm = () => {
    setIsNavigation(false);
    setIsDisabled(false);
    setIsEdit(false);
    setFormData(createQuotationFollowupInitialFormData);
    setTableData(createQuotationFollowupEmptyTableRow);
    toast({
      title: `Form Cleared`,
      status: "success",
      variant: "left-accent",
      position: "top-right",
      isClosable: true,
    });
  };

  const editForm = () => {
    setIsDisabled((p) => !p);
    setIsEdit((p) => !p);
  };

  const navigateVoucherForm = async (navigate, voucherNo) => {
    setLoading(true);
    setIsNavigation(true);
    setIsDisabled(true);
    try {
      const response = await axiosInstance.post("offer/navigate", {
        [navigate]: true,
        voucher_no: voucherNo,
      });
      const resData = response.data;
      const { items } = response.data;
      const form = {
        date: resData?.Dated
          ? formatDate(new Date(resData?.Dated), true)
          : null,
        vtp: resData?.VTP,
        mnth: resData?.Mnth,
        location: resData?.Location,
        vno: resData?.vno,
        voucherNo: resData?.Voucher_No,
        clientId: resData?.client_id ?? "",
        clientTitle: resData?.ClientName ?? "",
        assignerId: resData?.EmployeeID ?? "",
        assignerTitle: resData?.EmployeeName ?? "",
        assessmentTime: resData?.AssessmentTime
          ? dayjs(resData?.AssessmentTime).format("YYYY-MM-DDTHH:mm")
          : "",
        assignerLocation: resData?.AssessmentLocation ?? "",
        salesTaxA: resData?.SalesTaxA ?? 0,
        salesTaxR: resData?.SalesTaxR ?? 0,
        discountAmount: resData?.Discount ?? 0,
        discountPercent: resData?.DiscountPercent ?? 0,
        netPayableAmt: resData?.GrossAmount ?? 0,
        lead: resData?.Lead ?? "",
        tenderNo: resData?.TenderNo ?? "",
        // exchangeRate: resData?.ExchRate ?? 0,
        // partyRef: resData?.Party_ref ?? "",
        attentionPerson: resData?.AttentionPerson ?? "",
        designation: resData?.AttentionPerson_Desig ?? "",
        contactPerson: resData?.ContactPerson ?? "",
        currency: resData?.Currency_ID ?? "",
        subject: resData?.Subject ?? "",
        quotation: resData?.Quotation ?? "",
        totalAmount: resData?.GrdTotalPSTAmt ?? 0,
        freight: resData?.Freight ?? 0,
        validityDays: resData?.Validity ?? 0,
        paymentTerms: resData?.Terms ?? "",
        netAmount: resData?.NetAmount ?? 0,
        narration: resData?.StartingComments ?? "",
      };
      setTableData(() => transformData([], items, true));
      setFormData(form);
      setLoading(false);
      fetchClientData();
      toast({
        title: `${navigate.toUpperCase()} Voucher Fetched !`,
        status: "success",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } catch (error) {
      console.error(`Error fetching ${navigate} voucher:`, error);
      setLoading(false);
      toast({
        title: `${navigate} Voucher Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  // Toolbar funtions ends here

  useEffect(() => {
    loadInitialData();
  }, []);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div>
              <div>
                <div className="page-inner">
                  <div className="row">
                    <div className="bgWhite">
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "24px",
                          fontWeight: "bold",
                          padding: "10px",
                        }}
                      >
                        Client Lead
                      </h1>
                    </div>
                  </div>
                  <div
                    className="row"
                    style={{ gap: "10px", paddingTop: "8px" }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "20px",
                          fontWeight: "bold",
                        }}
                      >
                        SOP&apos;s
                      </h1>
                      {questions.map((question, index) => (
                        <div className="question" key={index}>
                          <p>{index + 1}. {question}</p>
                          {question == 'What is the Lead Generator Company Name?' ?
                            <Input
                              type={"text"}
                              placeholder="Lead Generator Company Name"
                              _placeholder={{ color: "gray.500" }}
                            /> : <Select placeholder="Select">
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                            </Select>}
                        </div>
                      ))}
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(40% - 5px) !important",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <form>
                        <FormControl
                          key={"date"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              md: "row",
                            },
                            marginTop: "10px",
                          }}
                          isRequired={true}
                        >
                          <FormLabel
                            htmlFor={"date"}
                            sx={{
                              marginBottom: "0",
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "20%",
                                lg: "35%",
                              },
                            }}
                          >
                            Date
                          </FormLabel>
                          <Input
                            id={"date"}
                            name={"date"}
                            type={"date"}
                            value={formData.date}
                            onChange={handleInputChange}
                            _placeholder={{ color: "gray.500" }}
                            disabled={isEdit || isDisabled}
                            sx={{
                              marginLeft: { base: "0", sm: "0", lg: "4px" },
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "80%",
                              },
                            }}
                          />
                        </FormControl>

                        <FormControl
                          key={"voucherNo"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              md: "row",
                            },
                            marginTop: "10px",
                          }}
                          isRequired={true}
                        >
                          <FormLabel
                            htmlFor={"voucherNo"}
                            sx={{
                              marginBottom: "0",
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "20%",
                                lg: "35%",
                              },
                            }}
                          >
                            Quotation No.
                          </FormLabel>
                          <Input
                            id={"voucherNo"}
                            name={"voucherNo"}
                            type={"text"}
                            value={formData.voucherNo}
                            onChange={handleInputChange}
                            readOnly={true}
                            disabled={isEdit || isDisabled}
                            sx={{
                              marginLeft: { base: "0", sm: "0", lg: "4px" },
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "80%",
                              },
                            }}
                          />
                        </FormControl>

                        <FormControl
                          key={"currency"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              md: "row",
                            },
                            marginTop: "10px",
                          }}
                          isRequired={false}
                        >
                          <FormLabel
                            htmlFor={"currency"}
                            sx={{
                              marginBottom: "0",
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "20%",
                                lg: "35%",
                              },
                            }}
                          >
                            Currency
                          </FormLabel>
                          <Box
                            sx={{
                              marginLeft: { base: "0", sm: "0", lg: "4px" },
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "80%",
                              },
                            }}
                          >
                            <ComboBox
                              key={"currency"}
                              target={true}
                              onChange={handleInputChange}
                              name={"currency"}
                              inputWidths={["100%"]}
                              buttonWidth={"20px"}
                              styleButton={{ padding: "3px !important" }}
                              tableData={currencies}
                              tableHeaders={["ID"]}
                              nameFields={["currency"]}
                              placeholders={[]}
                              keys={["id"]}
                              form={formData}
                              isDisabled={isDisabled}
                            />
                          </Box>
                        </FormControl>

                        {/* <FormControl
                          key={"exchangeRate"}
                          sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            flexDirection: {
                              base: "column",
                              sm: "column",
                              md: "row",
                            },
                            marginTop: "10px",
                          }}
                          isRequired={false}
                        >
                          <FormLabel
                            htmlFor={"exchangeRate"}
                            sx={{
                              marginBottom: "0",
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "20%",
                                lg: "35%",
                              },
                            }}
                          >
                            Exchange Rate
                          </FormLabel>
                          <Input
                            id={"exchangeRate"}
                            name={"exchangeRate"}
                            type={"text"}
                            value={formData.exchangeRate}
                            onChange={handleInputChange}
                            readOnly={false}
                            disabled={isDisabled}
                            sx={{
                              marginLeft: { base: "0", sm: "0", lg: "4px" },
                              width: {
                                base: "100%",
                                sm: "100%",
                                md: "80%",
                              },
                            }}
                          />
                        </FormControl> */}

                        <FormControl>
                          <Button
                            style={{ width: "100%", marginTop: "5px" }}
                            colorScheme="blue"
                            onClick={() => getVoucherNo(formData.date)}
                            isDisabled={isEdit || isDisabled}
                          >
                            Generate Quotation No
                          </Button>
                        </FormControl>
                      </form>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(60% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"lead"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Lead
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Select
                            onChange={handleInputChange}
                            name={"lead"}
                            value={formData.lead}
                            placeholder="Please Select"
                            disabled={isDisabled}
                          >
                            <option value="social_media">Social Media</option>
                            <option value="front_desk">Front-Desk</option>
                            <option value="other">Other</option>
                          </Select>
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={true}
                      >
                        <FormLabel
                          htmlFor={"client"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Client
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <ComboBox
                            target={true}
                            onChange={handleInputChange}
                            name={"client"}
                            buttonWidth={"20px"}
                            styleButton={{ padding: "3px !important" }}
                            tableData={clients}
                            tableHeaders={["ID", "Title"]}
                            nameFields={["clientId", "clientTitle"]}
                            placeholders={["ID", "Name"]}
                            keys={["id", "title"]}
                            form={formData}
                            isDisabled={isDisabled}
                          />
                          <Button
                            colorScheme="orange"
                            style={{ marginLeft: "5px" }}
                            className="addClient"
                            onClick={() => setIsAddClientModalOpen(true)}
                          >
                            +
                          </Button>

                          <AddClientModal
                            isOpen={isAddClientModalOpen}
                            onClose={() => setIsAddClientModalOpen(false)}
                            onSave={() => loadInitialData()}
                          />
                        </Box>
                      </FormControl>

                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"contactPerson"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Contact Person
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"contactPerson"}
                            placeholder={""}
                            value={formData.contactPerson}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"email"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Email
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"email"}
                            placeholder={"Select Client"}
                            value={formData.email}
                            _placeholder={{ color: "gray.500" }}
                            type={"email"}
                            disabled={isDisabled}
                            isReadOnly={true}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"phoneNumber"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Mobile No.
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"phoneNumber"}
                            placeholder={"Select Client"}
                            value={formData.phoneNumber}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                            isReadOnly={true}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"landline"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Landline
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"landline"}
                            placeholder={"Select Client"}
                            value={formData.landline}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                            isReadOnly={true}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"address"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Address
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"address"}
                            placeholder={"Select Client"}
                            value={formData.address}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                            isReadOnly={true}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"assigner"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Assessor
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <ComboBox
                            target={true}
                            onChange={handleInputChange}
                            name={"assigner"}
                            buttonWidth={"20px"}
                            styleButton={{ padding: "3px !important" }}
                            tableData={salesMan}
                            tableHeaders={["ID", "Title"]}
                            nameFields={["assignerId", "assignerTitle"]}
                            placeholders={["ID", "Name"]}
                            keys={["ID", "Title"]}
                            form={formData}
                            isDisabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"assessmentTime"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Assessment Time
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"assessmentTime"}
                            placeholder={""}
                            value={formData.assessmentTime}
                            _placeholder={{ color: "gray.500" }}
                            type={"datetime-local"}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>

                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"subject"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Subject
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"subject"}
                            placeholder={""}
                            value={formData.subject}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                        }}
                        isRequired={false}
                      >
                        <FormLabel
                          htmlFor={"quotation"}
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "20%" },
                          }}
                        >
                          Quotation
                        </FormLabel>
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "80%" },
                            display: "flex",
                            gap: "0",
                          }}
                        >
                          <Input
                            onChange={handleInputChange}
                            name={"quotation"}
                            placeholder={""}
                            value={formData.quotation}
                            _placeholder={{ color: "gray.500" }}
                            type={"text"}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                  </div>
                  <div className="row">
                    <div
                      style={{ padding: "0" }}
                      className="bgWhite col-md-12 mt-2"
                    >
                      <AanzaDataTable
                        tableData={tableData}
                        setTableData={setTableData}
                        headers={QuotationFollowupHeaders}
                        tableWidth="100%"
                        tableHeight="400px"
                        fontSize="lg"
                        cellRender={cellRender}
                        onSave={handleSave}
                        styleHead={{
                          background: "#3275bb",
                          color: "white !important",
                        }}
                        styleBody={{ background: "white !important" }}
                        calculation={calculation}
                        isDisabled={isDisabled}
                      />
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2">
                      <div
                        className="pt-4 pb-2"
                        style={{
                          display: "grid",
                          gridTemplateColumns:
                            "repeat(auto-fit,minmax(300px,1fr))",
                          gap: "5px",
                        }}
                      >
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Total Amount
                            </FormLabel>
                            <Input
                              onChange={handleInputChange}
                              name={"totalAmount"}
                              value={formData.totalAmount}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              type="number"
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              S.Tax%
                            </FormLabel>
                            <Box
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                                display: "flex",
                                gap: 1.5,
                              }}
                            >
                              <NumberInput
                                onChange={handleInputChange}
                                name={"salesTaxR"}
                                value={formData.salesTaxR}
                                placeholder="R"
                                _placeholder={{ color: "gray.500" }}
                                sx={{
                                  width: { base: "30%", sm: "30%", lg: "30%" },
                                }}
                                isReadOnly={false}
                                disabled={isDisabled}
                              />
                              <NumberInput
                                onChange={handleInputChange}
                                name={"salesTaxA"}
                                value={formData.salesTaxA}
                                placeholder="A"
                                _placeholder={{ color: "gray.500" }}
                                sx={{
                                  width: { base: "70%", sm: "70%", lg: "70%" },
                                }}
                                isReadOnly={false}
                                disabled={isDisabled}
                              />
                            </Box>
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              S.Tax Amt.
                            </FormLabel>
                            <Input
                              onChange={handleInputChange}
                              name={"sTaxAmount"}
                              value={formData.sTaxAmount}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              type="number"
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Net Amount
                            </FormLabel>
                            <NumberInput
                              onChange={handleInputChange}
                              name={"netAmount"}
                              value={formData.netAmount}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Discount
                            </FormLabel>
                            <Box
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                                display: "flex",
                                gap: 1.5,
                              }}
                            >
                              <Input
                                onChange={handleInputChange}
                                name={"discountPercent"}
                                value={formData.discountPercent}
                                placeholder="%"
                                _placeholder={{ color: "gray.500" }}
                                sx={{
                                  width: { base: "30%", sm: "30%", lg: "30%" },
                                }}
                                type="number"
                                isReadOnly={false}
                                disabled={isDisabled}
                              />
                              <Input
                                onChange={handleInputChange}
                                name={"discountAmount"}
                                value={formData.discountAmount}
                                placeholder="A"
                                _placeholder={{ color: "gray.500" }}
                                type="number"
                                sx={{
                                  width: { base: "70%", sm: "70%", lg: "70%" },
                                }}
                                isReadOnly={true}
                                disabled={isDisabled}
                              />
                            </Box>
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Freight
                            </FormLabel>
                            <Input
                              onChange={handleInputChange}
                              name={"freight"}
                              value={formData.freight}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              type="number"
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                              }}
                              isReadOnly={false}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Net Payable Amt
                            </FormLabel>
                            <Input
                              onChange={handleInputChange}
                              name={"netPayableAmt"}
                              value={formData.netPayableAmt}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              type="number"
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Validity Days
                            </FormLabel>
                            <Input
                              onChange={handleInputChange}
                              name={"validityDays"}
                              value={formData.validityDays}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              type="number"
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "70%" },
                              }}
                              isReadOnly={false}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl
                          gridColumn="1 / -1"
                          style={{ display: "flex", gap: "4px" }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "10%",
                                },
                              }}
                            >
                              Payment Terms
                            </FormLabel>
                            <Input
                              onChange={handleInputChange}
                              name={"paymentTerms"}
                              value={formData.paymentTerms}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              type="text"
                              sx={{
                                width: { base: "100%", sm: "100%", lg: "90%" },
                              }}
                              isReadOnly={false}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                      </div>
                      {/* <Box sx={{paddingBottom: '20px'}}>
                      </Box> */}
                    </div>
                  </div>
                  <div className="row">
                    <div className="bgWhite mt-2 pt-4 pb-4">
                      <FormControl
                        sx={{
                          display: "flex",
                          marginTop: "10px",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                        }}
                      >
                        <FormLabel
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "10%" },
                          }}
                        >
                          Remarks
                        </FormLabel>
                        <Textarea
                          _placeholder={{ color: "gray.500" }}
                          resize="vertical"
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "90%" },
                          }}
                          onChange={handleInputChange}
                          name={"narration"}
                          value={formData.narration}
                          disabled={isDisabled}
                        />
                      </FormControl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Toolbar
            save={(isEdit || !isNavigation) ? handlePrint : handleSave}
            clear={clearForm}
            edit={editForm}
            first={() => navigateVoucherForm("first")}
            last={() => navigateVoucherForm("last")}
            previous={() => navigateVoucherForm("prev", formData.voucherNo)}
            next={() => navigateVoucherForm("next", formData.voucherNo)}
            remove={() => setDialogOpen(true)}
            isNavigation={isNavigation}
            isEdit={isEdit}
            print={handlePrint}
            check={handleCheck}
          />
        </>
      )}
      <ConfirmDialog
        isOpen={isDialogOpen}
        onClose={() => setDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Voucher"
        message={
          <>
            Are you sure you want to delete voucher no{" "}
            <b>{formData.voucherNo}</b> ?
          </>
        }
        confirmText="Delete"
        cancelText="Cancel"
        confirmColorScheme="red"
      />
      <PrintModal
        isOpen={isPrintModalOpen}
        onClose={() => setPrintModalOpen(false)}
        showHeader={false}
        formName="Client Order"
        buttonText={(!isEdit && isNavigation) ? "Print" : "Save"}
        callback={(!isEdit && isNavigation) ? null : (pdfBlob) => {
          handleCreatePdf(pdfBlob);
          handleSave();
          setPrintModalOpen(false);
        }}
      >
        <ComponentToPrint data={printData} />
      </PrintModal>
    </>
  );
};

const ClientLeadPage = () => (
  <Suspense fallback={<Loader />}>
    <QuotationOffer />
  </Suspense>
);

export default ClientLeadPage;
