-- Notification System Database Schema
-- This schema is designed to be scalable and handle various types of notifications

-- 1. Notification Types Table
-- Defines different types of notifications that can be sent
CREATE TABLE NotificationTypes (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    TypeName NVARCHAR(50) NOT NULL UNIQUE, -- e.g., 'TIME_UPDATE', 'FORM_SUBMISSION', 'STATUS_CHANGE'
    DisplayName NVARCHAR(100) NOT NULL, -- Human readable name
    Description NVARCHAR(255),
    IconName NVARCHAR(50), -- Icon identifier for frontend
    ColorScheme NVARCHAR(20), -- Color scheme for UI (e.g., 'blue', 'orange', 'red')
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE()
);

-- 2. Notification Templates Table
-- Stores templates for different notification types
CREATE TABLE NotificationTemplates (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    TypeID INT NOT NULL,
    TemplateName NVARCHAR(100) NOT NULL,
    TitleTemplate NVARCHAR(255) NOT NULL, -- Template with placeholders like {userName}, {taskId}
    MessageTemplate NVARCHAR(1000) NOT NULL, -- Template with placeholders
    ActionButtonText NVARCHAR(50), -- Optional action button text
    ActionUrl NVARCHAR(255), -- Optional action URL
    Priority TINYINT DEFAULT 1, -- 1=Low, 2=Medium, 3=High, 4=Critical
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (TypeID) REFERENCES NotificationTypes(ID)
);

-- 3. Notifications Table
-- Stores individual notification instances
CREATE TABLE Notifications (
    ID BIGINT IDENTITY(1,1) PRIMARY KEY,
    TypeID INT NOT NULL,
    TemplateID INT,
    Title NVARCHAR(255) NOT NULL,
    Message NVARCHAR(1000) NOT NULL,
    ActionButtonText NVARCHAR(50),
    ActionUrl NVARCHAR(255),
    Priority TINYINT DEFAULT 1,
    
    -- Sender Information
    SenderUserID NVARCHAR(32), -- User who triggered the notification
    SenderEmployeeID NVARCHAR(32), -- Employee who triggered the notification
    
    -- Context Information (flexible JSON-like storage)
    ContextData NVARCHAR(MAX), -- JSON string with context data
    ReferenceID NVARCHAR(50), -- Reference to related entity (e.g., purposeID, voucherNo)
    ReferenceType NVARCHAR(50), -- Type of reference (e.g., 'PURPOSE', 'VOUCHER', 'FORM')
    
    -- Metadata
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    ExpiresAt DATETIME2, -- Optional expiration date
    
    FOREIGN KEY (TypeID) REFERENCES NotificationTypes(ID),
    FOREIGN KEY (TemplateID) REFERENCES NotificationTemplates(ID),
    FOREIGN KEY (SenderUserID) REFERENCES users(id),
    FOREIGN KEY (SenderEmployeeID) REFERENCES EmployeeDetails(ID)
);

-- 4. Notification Recipients Table
-- Defines who should receive notifications (many-to-many relationship)
CREATE TABLE NotificationRecipients (
    ID BIGINT IDENTITY(1,1) PRIMARY KEY,
    NotificationID BIGINT NOT NULL,
    RecipientUserID NVARCHAR(32), -- Specific user
    RecipientRoleID INT, -- All users with this role
    RecipientEmployeeID NVARCHAR(32), -- Specific employee
    
    -- Status tracking
    IsRead BIT DEFAULT 0,
    ReadAt DATETIME2,
    IsArchived BIT DEFAULT 0,
    ArchivedAt DATETIME2,
    
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    
    FOREIGN KEY (NotificationID) REFERENCES Notifications(ID) ON DELETE CASCADE,
    FOREIGN KEY (RecipientUserID) REFERENCES users(id),
    FOREIGN KEY (RecipientRoleID) REFERENCES User_Role(RoleID),
    FOREIGN KEY (RecipientEmployeeID) REFERENCES EmployeeDetails(ID)
);

-- 5. Notification Settings Table
-- User preferences for notifications
CREATE TABLE NotificationSettings (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    UserID NVARCHAR(32) NOT NULL,
    TypeID INT NOT NULL,
    IsEnabled BIT DEFAULT 1,
    EmailNotification BIT DEFAULT 0, -- Future: email notifications
    PushNotification BIT DEFAULT 1, -- Future: push notifications
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE(),
    
    FOREIGN KEY (UserID) REFERENCES users(id),
    FOREIGN KEY (TypeID) REFERENCES NotificationTypes(ID),
    UNIQUE(UserID, TypeID)
);

-- Create indexes for better performance
CREATE INDEX IX_Notifications_TypeID ON Notifications(TypeID);
CREATE INDEX IX_Notifications_CreatedAt ON Notifications(CreatedAt DESC);
CREATE INDEX IX_Notifications_ReferenceID ON Notifications(ReferenceID);
CREATE INDEX IX_NotificationRecipients_NotificationID ON NotificationRecipients(NotificationID);
CREATE INDEX IX_NotificationRecipients_RecipientUserID ON NotificationRecipients(RecipientUserID);
CREATE INDEX IX_NotificationRecipients_RecipientRoleID ON NotificationRecipients(RecipientRoleID);
CREATE INDEX IX_NotificationRecipients_IsRead ON NotificationRecipients(IsRead);
CREATE INDEX IX_NotificationSettings_UserID ON NotificationSettings(UserID);
