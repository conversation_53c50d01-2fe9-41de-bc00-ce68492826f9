import axios from "axios";
import Cookies from "js-cookie";

const axiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_BASE_URL,
});

axiosInstance.interceptors.request.use(
    (config) => {
        if (config.url.includes('login')) {
            delete config.headers['authorization'];
        } else {
            config.headers['authorization'] = 'Basic ' + Cookies.get('auth-token');
        }

        // console.log('Request:', config);
        return config;
    },
    (error) => {
        // console.error('Request Error:', error);
        return Promise.reject(error);
    }
);

axiosInstance.interceptors.response.use(
    (response) => {
        // console.log('Response:', response);
        return response;
    },
    (error) => {
        // console.error('Response Error:', error);
        return Promise.reject(error);
    }
);

export default axiosInstance;