const sql = require('mssql');

const coItemColumns = [
    { name: 'Dated', type: sql.DateTime, editable: true },
    { name: 'VTP', type: sql.<PERSON>ar<PERSON><PERSON>(20), editable: false },
    { name: 'Mnth', type: sql.<PERSON>(8), editable: false },
    { name: 'Location', type: sql.<PERSON>ar<PERSON>har(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'srno', type: sql.Int, editable: false },
    { name: 'item_ID', type: sql.Var<PERSON><PERSON>(32), editable: true },
    { name: 'ItemName', type: sql.Var<PERSON>har(256), editable: true },
    { name: 'Qty', type: sql.Float, editable: true },
    { name: 'GrossRate', type: sql.Float, editable: true },
    { name: 'Rate', type: sql.Float, editable: true },
    { name: 'Total', type: sql.Float, editable: true },
];

const coColumns = [
    { name: 'Dated', type: sql.<PERSON><PERSON><PERSON>(128) },
    { name: 'VTP', type: sql.<PERSON>ar<PERSON><PERSON>(3) },
    { name: 'Mnth', type: sql.VarChar(4) },
    { name: 'Location', type: sql.VarChar(8) },
    { name: 'vno', type: sql.Int },
    { name: 'client_id', type: sql.VarChar(16) },
    { name: 'TenderNo', type: sql.VarChar(16) },
    { name: 'ContractNo', type: sql.VarChar(16) },
    { name: 'SalesMan_ID', type: sql.VarChar(32) },
    { name: 'SalesMan_ID2', type: sql.VarChar(32) },
    { name: 'SalesMan_ID3', type: sql.VarChar(32) },
    { name: 'SalesMan_ID4', type: sql.VarChar(32) },
    { name: 'Delivery_Person', type: sql.VarChar(32) },
    { name: 'Installer_Person', type: sql.VarChar(32) },
    { name: 'Delivery_Time', type: sql.DateTime },
    { name: 'Installer_Time', type: sql.DateTime },
    { name: 'Cash_id', type: sql.VarChar(16) },
    { name: 'po', type: sql.VarChar(30) },
    { name: 'po_date', type: sql.VarChar(128) },
    { name: 'GrossAmount', type: sql.Float },
    { name: 'SalesTaxPercentage', type: sql.Float },
    { name: 'ASalesTaxPercentage', type: sql.Float },
    { name: 'SalesTaxAmount', type: sql.Float },
    { name: 'STaxAmt', type: sql.Float },
    { name: 'Discount', type: sql.Float },
    { name: 'Freight', type: sql.Float },
    { name: 'NetAmount', type: sql.Float },
    { name: 'Advance', type: sql.Float },
    { name: 'AdvanceCCard', type: sql.Float },
    { name: 'Balance', type: sql.Float },
    { name: 'Narration', type: sql.VarChar(256) },
    { name: 'CreationDate', type: sql.VarChar(128) },
    { name: 'TotalQty', type: sql.Float },
    { name: "RefVoucherNo", type: sql.VarChar(70), editable: true },
    { name: "RefVTP", type: sql.VarChar(20), editable: true },
    { name: "RefMnth", type: sql.VarChar(8), editable: true },
    { name: "RefLocation", type: sql.VarChar(8), editable: true },
    { name: "RefVNo", type: sql.Int, editable: true },
    { name: 'prp_id', type: sql.VarChar(8), editable: true },
    { name: 'chk_id', type: sql.VarChar(8), editable: true },
    { name: 'app_id', type: sql.VarChar(8), editable: true },
];

const cashItemColumns = [
    { name: 'Dated', type: sql.DateTime, editable: true },
    { name: 'VTP', type: sql.VarChar(20), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'srno', type: sql.Int, editable: false },
    { name: 'acc_id', type: sql.VarChar(32), editable: true },
    { name: 'adj_VoucherNo', type: sql.VarChar(32), editable: true },
    { name: 'Emp_ID', type: sql.VarChar(16), editable: true },
    { name: 'Currency_ID', type: sql.Float, editable: true },
    { name: 'ln_rem', type: sql.VarChar(240), editable: true },
    { name: 'Amt', type: sql.Float, editable: true },
    { name: 'TotalAmt', type: sql.Float, editable: true },
];

const cashColumns = [
    { name: 'Dated', type: sql.VarChar(128), editable: true },
    { name: 'vtp', type: sql.VarChar(3), editable: false },
    { name: 'Mnth', type: sql.VarChar(4), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'bc_id', type: sql.VarChar(16), editable: true },
    { name: 'nara', type: sql.VarChar(256), editable: true },
    { name: 'amt', type: sql.Int, editable: true },
    { name: 'Advance', type: sql.Int, editable: true },
    { name: 'CostCenter_ID', type: sql.VarChar(16), editable: true },
    { name: 'CreationDate', type: sql.VarChar(128), editable: true },
];

const bankItemColumns = [
    { name: 'Dated', type: sql.DateTime, editable: true },
    { name: 'VTP', type: sql.VarChar(20), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'srno', type: sql.Int, editable: false },
    { name: 'acc_id', type: sql.VarChar(32), editable: true },
    { name: 'adj_VoucherNo', type: sql.VarChar(32), editable: true },
    { name: 'Emp_ID', type: sql.VarChar(16) },
    { name: 'Currency_ID', type: sql.Float, editable: true },
    { name: 'ln_rem', type: sql.VarChar(240), editable: true },
    { name: 'Amt', type: sql.Float, editable: true },
    { name: 'TotalAmt', type: sql.Float, editable: true },
    { name: 'TaxPercent', type: sql.Int, editable: true },
    { name: 'TaxAmt', type: sql.Int, editable: true },
    { name: 'taxacc_id', type: sql.VarChar(32), editable: true },
];

const bankColumns = [
    { name: 'Dated', type: sql.VarChar(128), editable: true },
    { name: 'RealizationDate', type: sql.VarChar(128), editable: true },
    { name: 'refdate', type: sql.VarChar(128), editable: true },
    { name: 'vtp', type: sql.VarChar(3), editable: false },
    { name: 'Mnth', type: sql.VarChar(4), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'refno', type: sql.Int, editable: true },
    { name: 'bc_id', type: sql.VarChar(16), editable: true },
    { name: 'nara', type: sql.VarChar(256), editable: true },
    { name: 'ChqTitle', type: sql.VarChar(256), editable: true },
    { name: 'amt', type: sql.Int, editable: true },
    { name: 'CreationDate', type: sql.VarChar(128), editable: true },
];

const goodReceiptNoteItemColumns = [
    { name: 'Dated', type: sql.DateTime, editable: true },
    { name: 'VTP', type: sql.VarChar(20), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'srno', type: sql.Int, editable: false },
    { name: 'Item_ID', type: sql.VarChar(32), editable: true },
    { name: 'Unit', type: sql.VarChar(10), editable: true },
    { name: 'Qty', type: sql.Float, editable: true },
];

const goodReceiptNoteColumns = [
    { name: 'Dated', type: sql.VarChar(128), editable: false },
    { name: 'VTP', type: sql.VarChar(3), editable: false },
    { name: 'mnth', type: sql.VarChar(4), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'Supp_id', type: sql.VarChar(32), editable: true },
    { name: 'Emp_ID', type: sql.VarChar(16), editable: true },
    { name: 'remarks', type: sql.VarChar(256), editable: true },
    { name: 'CreationDate', type: sql.VarChar(128), editable: false },
];

const residentRegistrationFormColumns = [
    { name: 'dated', type: sql.DateTime, editable: false },
    { name: 'vtp', type: sql.VarChar(20), editable: false },
    { name: 'AorB', type: sql.TinyInt, editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'StudentName', type: sql.VarChar(120), editable: true },
    { name: 'StudentID', type: sql.VarChar(32), editable: true },
    { name: 'Class_ID', type: sql.VarChar(32), editable: true },
    { name: 'EdGroup_ID', type: sql.VarChar(32), editable: true },
    { name: 'DateOfBirth', type: sql.DateTime, editable: true },
    { name: 'Age', type: sql.Float, editable: true },
    { name: 'Session', type: sql.VarChar(32), editable: true },
    { name: 'Sex', type: sql.VarChar(10), editable: true },
    { name: 'MailingAddress', type: sql.VarChar(120), editable: true },
    { name: 'EmailAddress', type: sql.VarChar(120), editable: true },
    { name: 'TelHome', type: sql.VarChar(20), editable: true },
    { name: 'CellNo', type: sql.VarChar(20), editable: true },
    { name: 'AmtReceived', type: sql.Float, editable: true },
    { name: 'Cash_ID', type: sql.VarChar(32), editable: true },
    { name: 'Narration', type: sql.Text, editable: true },
    { name: 'TSSRegNo', type: sql.VarChar(40), editable: true },
    { name: 'FatherName', type: sql.VarChar(40), editable: true },
    { name: 'FatherOccupation', type: sql.VarChar(40), editable: true },
    { name: 'FatherQualification', type: sql.VarChar(40), editable: true },
    { name: 'BusinessAddress', type: sql.VarChar(256), editable: true },
    { name: 'PermanantAddress', type: sql.VarChar(256), editable: true },
    { name: 'TelNo', type: sql.VarChar(256), editable: true },
    { name: 'TelBusiness', type: sql.VarChar(40), editable: true },
    { name: 'MotherName', type: sql.VarChar(40), editable: true },
    { name: 'MotherQualification', type: sql.VarChar(40), editable: true },
    { name: 'EmergencyContactNo', type: sql.VarChar(256), editable: true },
    { name: 'NoOfBrotherSister', type: sql.VarChar(40), editable: true },
    { name: 'chkStudying', type: sql.SmallInt, editable: true },
    { name: 'NoOfCopies', type: sql.SmallInt, editable: true },
    { name: 'Cancelled', type: sql.SmallInt, editable: true },
    { name: 'CancellDate', type: sql.DateTime, editable: true },
    { name: 'CancelReason', type: sql.VarChar(256), editable: true },
    { name: 'prp_id', type: sql.VarChar(8), editable: true },
    { name: 'chk_id', type: sql.VarChar(8), editable: true },
    { name: 'app_id', type: sql.VarChar(8), editable: true },
    { name: 'CreationDate', type: sql.DateTime, editable: false },
    { name: 'CancelBy_ID', type: sql.VarChar(8), editable: true },
    { name: 'Chk_Date', type: sql.DateTime, editable: true },
    { name: 'App_Date', type: sql.DateTime, editable: true }
];

const storeIssuanceNoteItemColumns = [
    { name: 'Dated', type: sql.DateTime, editable: true },
    { name: 'VTP', type: sql.VarChar(20), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'Vno', type: sql.Int, editable: false },
    { name: 'Srno', type: sql.Int, editable: false },
    { name: 'Item_Id', type: sql.VarChar(32), editable: true },
    { name: 'Qty', type: sql.Float, editable: true },
    { name: 'IssueQty', type: sql.Float, editable: true },
];

const storeIssuanceNoteColumns = [
    { name: 'Dated', type: sql.VarChar(128), editable: false },
    { name: 'VTP', type: sql.VarChar(3), editable: false },
    { name: 'Mnth', type: sql.VarChar(4), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'Vno', type: sql.Int, editable: false },
    { name: 'Emp_ID', type: sql.VarChar(16), editable: true },
    { name: 'Narration', type: sql.VarChar(256), editable: true },
    { name: 'CreationDate', type: sql.VarChar(128), editable: false },
];

const offerQuotationColumns = [
    { name: 'Dated', type: sql.DateTime, editable: false },
    { name: 'AorB', type: sql.TinyInt, editable: false },
    { name: 'VTP', type: sql.VarChar(20), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'Offer_No', type: sql.VarChar(20), editable: true },
    { name: 'client_id', type: sql.VarChar(32), editable: true },
    { name: 'Party_ref', type: sql.VarChar(40), editable: true },
    { name: 'AttentionPerson', type: sql.VarChar(50), editable: true },
    { name: 'AttentionPerson_Desig', type: sql.VarChar(50), editable: true },
    { name: 'Emp_id', type: sql.VarChar(32), editable: true },
    { name: 'EmployeeID', type: sql.VarChar(32), editable: true },
    { name: 'Subject', type: sql.VarChar(256), editable: true },
    { name: 'Quotation', type: sql.VarChar(256), editable: true },
    { name: 'Height', type: sql.Float, editable: true },
    { name: 'Width', type: sql.Float, editable: true },
    { name: 'Terms', type: sql.VarChar(50), editable: true },
    { name: 'Origin', type: sql.VarChar(30), editable: true },
    { name: 'Validity', type: sql.SmallInt, editable: true },
    { name: 'StartingComments', type: sql.VarChar(256), editable: true },
    { name: 'Paragraph', type: sql.Text, editable: true },
    { name: 'EndNarration', type: sql.Text, editable: true },
    { name: 'GrossAmount', type: sql.Float, editable: true },
    { name: 'SalesTaxPercentage', type: sql.Float, editable: true },
    { name: 'SalesTaxAmount', type: sql.Float, editable: true },
    { name: 'DiscountPercent', type: sql.Float, editable: true },
    { name: 'Discount', type: sql.Float, editable: true },
    { name: 'Freight', type: sql.Float, editable: true },
    { name: 'NetAmount', type: sql.Float, editable: true },
    { name: 'RevOfferNo', type: sql.VarChar(40), editable: true },
    { name: 'RevNo', type: sql.TinyInt, editable: true },
    { name: 'NoOfCopies', type: sql.SmallInt, editable: true },
    { name: 'cancelled', type: sql.SmallInt, editable: true },
    { name: 'CancellDate', type: sql.DateTime, editable: true },
    { name: 'CancelReason', type: sql.VarChar(256), editable: true },
    { name: 'prp_id', type: sql.VarChar(8), editable: true },
    { name: 'chk_id', type: sql.VarChar(8), editable: true },
    { name: 'app_id', type: sql.VarChar(8), editable: true },
    { name: 'CostCenter_ID', type: sql.VarChar(5), editable: true },
    { name: 'Project_ID', type: sql.VarChar(5), editable: true },
    { name: 'Currency_ID', type: sql.VarChar(10), editable: true },
    { name: 'ExchRate', type: sql.VarChar(10), editable: true },
    { name: 'Site_ID', type: sql.VarChar(32), editable: true },
    { name: 'StDate', type: sql.DateTime, editable: true },
    { name: 'EnDate', type: sql.DateTime, editable: true },
    { name: 'CreationDate', type: sql.DateTime, editable: true },
    { name: 'ReserveTill', type: sql.DateTime, editable: true },
    { name: 'CancelBy_ID', type: sql.VarChar(8), editable: true },
    { name: 'Chk_Date', type: sql.DateTime, editable: true },
    { name: 'App_Date', type: sql.DateTime, editable: true },
    { name: 'EDPercent', type: sql.Float, editable: true },
    { name: 'EDAmount', type: sql.Float, editable: true },
    { name: 'Client_Title', type: sql.VarChar(70), editable: true },
    { name: 'GridSalesTaxAmt', type: sql.Float, editable: true },
    { name: 'Confirmed', type: sql.SmallInt, editable: true },
    { name: 'Party_ID', type: sql.VarChar(5), editable: true },
    { name: 'VoucherStatus_ID', type: sql.VarChar(40), editable: true },
    { name: 'VoucherStatusRemarks', type: sql.Text, editable: true },
    { name: 'RefNo', type: sql.VarChar(40), editable: true },
    { name: 'Foundation', type: sql.Float, editable: true },
    { name: 'Locading', type: sql.Float, editable: true },
    { name: 'Earthing', type: sql.Float, editable: true },
    { name: 'Installation', type: sql.Float, editable: true },
    { name: 'Kacha', type: sql.Float, editable: true },
    { name: 'PSTPerc', type: sql.Float, editable: true },
    { name: 'PSTAmt', type: sql.Float, editable: true },
    { name: 'GrdTotalPSTAmt', type: sql.Float, editable: true },
    { name: 'BuyMonth', type: sql.VarChar(2), editable: true },
    { name: 'BuyYear', type: sql.VarChar(4), editable: true },
    { name: 'RefDate', type: sql.DateTime, editable: true },
    { name: 'Cash', type: sql.TinyInt, editable: true },
    { name: 'ComputerName', type: sql.VarChar(120), editable: true },
    { name: 'Campaign', type: sql.VarChar(70), editable: true },
    { name: 'Warranty', type: sql.Text, editable: true },
    { name: 'TenderNo', type: sql.VarChar(16), editable: true },
    { name: 'ContactPerson', type: sql.VarChar(40), editable: true },
    { name: 'AssessmentTime', type: sql.DateTime, editable: true },
    { name: 'AssessmentLocation', type: sql.VarChar, editable: true },
    { name: 'Lead', type: sql.VarChar(64), editable: true },
    { name: 'SalesTaxR', type: sql.Float, editable: true },
    { name: 'SalesTaxA', type: sql.Float, editable: true },
    { name: 'ServiceCharges', type: sql.Float, editable: true },
    { name: "RefVoucherNo", type: sql.VarChar(70), editable: true },
    { name: "RefVTP", type: sql.VarChar(20), editable: true },
    { name: "RefMnth", type: sql.VarChar(8), editable: true },
    { name: "RefLocation", type: sql.VarChar(8), editable: true },
    { name: "RefVNo", type: sql.Int, editable: true },
];

const offerQuotationItemColumns = [
    { name: 'Dated', type: sql.DateTime, editable: false },
    { name: 'VTP', type: sql.VarChar(20), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'srno', type: sql.Int, editable: false },
    { name: 'Category', type: sql.VarChar(70), editable: true },
    { name: 'item_id', type: sql.VarChar(32), editable: true },
    { name: 'Cartons', type: sql.Float, editable: true },
    { name: 'QtyPerCTN', type: sql.Float, editable: true },
    { name: 'Qty', type: sql.Float, editable: true },
    { name: 'Rate', type: sql.Float, editable: true },
    { name: 'InstallationCharges', type: sql.Float, editable: true },
    { name: 'Discount', type: sql.Float, editable: true },
    { name: 'Total', type: sql.Float, editable: true },
    { name: 'STPerc', type: sql.Float, editable: true },
    { name: 'STAmt', type: sql.Float, editable: true },
    { name: 'delivery', type: sql.VarChar(120), editable: true },
    { name: 'details', type: sql.Text, editable: true },
    { name: 'Site_ID', type: sql.VarChar(32), editable: true },
    { name: 'Project_ID', type: sql.VarChar(5), editable: true },
    { name: 'CostCenter_ID', type: sql.VarChar(5), editable: true },
    { name: 'StDate', type: sql.DateTime, editable: true },
    { name: 'EnDate', type: sql.DateTime, editable: true },
    { name: 'VisitDate', type: sql.DateTime, editable: true },
    { name: 'FSRNo', type: sql.VarChar(40), editable: true },
    { name: 'KVA', type: sql.VarChar(40), editable: true },
    { name: 'Hrs', type: sql.VarChar(20), editable: true },
    { name: 'Vendor_ID', type: sql.VarChar(32), editable: true },
    { name: 'City', type: sql.VarChar(70), editable: true },
    { name: 'Media', type: sql.VarChar(70), editable: true },
    { name: 'VAmt', type: sql.Float, editable: true },
    { name: 'Height', type: sql.Float, editable: true },
    { name: 'Width', type: sql.Float, editable: true },
    { name: 'Godown_ID', type: sql.VarChar(8), editable: true },
    { name: 'Carton', type: sql.Float, editable: true },
    { name: 'QtyInCTN', type: sql.Float, editable: true },
    { name: 'Ref_VoucherNo', type: sql.VarChar(120), editable: true },
    { name: 'Ref_Vtp', type: sql.VarChar(20), editable: true },
    { name: 'Ref_SubType', type: sql.VarChar(8), editable: true },
    { name: 'Ref_FinancialYear', type: sql.VarChar(8), editable: true },
    { name: 'Ref_Mnth', type: sql.VarChar(8), editable: true },
    { name: 'Ref_vYear', type: sql.VarChar(8), editable: true },
    { name: 'Ref_Location', type: sql.VarChar(8), editable: true },
    { name: 'Ref_ForLocation', type: sql.VarChar(8), editable: true },
    { name: 'Ref_AorB', type: sql.VarChar(1), editable: true },
    { name: 'Ref_vno', type: sql.Int, editable: true },
    { name: 'Ref_SrNo', type: sql.Int, editable: true },
    { name: 'Item_Title', type: sql.VarChar(70), editable: true },
    { name: 'ClientItem_Code', type: sql.VarChar(32), editable: true },
    { name: 'ClientItem_Title', type: sql.VarChar(200), editable: true },
    { name: 'CI_Vtp', type: sql.VarChar(20), editable: true },
    { name: 'CI_SubType', type: sql.VarChar(8), editable: true },
    { name: 'CI_FinancialYear', type: sql.VarChar(8), editable: true },
    { name: 'CI_Mnth', type: sql.VarChar(8), editable: true },
    { name: 'CI_vYear', type: sql.VarChar(8), editable: true },
    { name: 'CI_Location', type: sql.VarChar(8), editable: true },
    { name: 'CI_ForLocation', type: sql.VarChar(8), editable: true },
    { name: 'CI_AorB', type: sql.VarChar(1), editable: true },
    { name: 'CI_vno', type: sql.Int, editable: true },
    { name: 'CI_SrNo', type: sql.Int, editable: true },
    { name: 'CID_Vtp', type: sql.VarChar(20), editable: true },
    { name: 'CID_SubType', type: sql.VarChar(8), editable: true },
    { name: 'CID_FinancialYear', type: sql.VarChar(8), editable: true },
    { name: 'CID_Mnth', type: sql.VarChar(8), editable: true },
    { name: 'CID_vYear', type: sql.VarChar(8), editable: true },
    { name: 'CID_Location', type: sql.VarChar(8), editable: true },
    { name: 'CID_ForLocation', type: sql.VarChar(8), editable: true },
    { name: 'CID_AorB', type: sql.VarChar(1), editable: true },
    { name: 'CID_vno', type: sql.Int, editable: true },
    { name: 'CID_SrNo', type: sql.Int, editable: true },
    { name: 'Job_ID', type: sql.VarChar(32), editable: true },
    { name: 'SalesTaxPerc', type: sql.Float, editable: true },
    { name: 'SalesTaxAmt', type: sql.Float, editable: true },
    { name: 'GroupName', type: sql.VarChar(120), editable: true },
    { name: 'QtyMF', type: sql.Float, editable: true },
    { name: 'OtherForLoc', type: sql.VarChar(8), editable: true },
    { name: 'ClientItemCategory_ID', type: sql.VarChar(32), editable: true },
    { name: 'ClientItemType_ID', type: sql.VarChar(32), editable: true },
    { name: 'EngineAcMake', type: sql.VarChar(50), editable: true },
    { name: 'EngineRating', type: sql.VarChar(20), editable: true },
    { name: 'FSRDate', type: sql.DateTime, editable: true },
    { name: 'VisitType', type: sql.VarChar(20), editable: true },
    { name: 'ClientRefNo', type: sql.VarChar(20), editable: true },
    { name: 'GensetHours', type: sql.VarChar(20), editable: true },
    { name: 'PSTPerc', type: sql.Float, editable: true },
    { name: 'PSTAmt', type: sql.Float, editable: true },
    { name: 'CI_VoucherNo', type: sql.VarChar(80), editable: true },
    { name: 'QtyDiscP', type: sql.Float, editable: true },
    { name: 'QtyDiscount', type: sql.Float, editable: true },
    { name: 'DeliveredItems', type: sql.Int, editable: true },
    { name: 'InstalledItems', type: sql.Int, editable: true },
    { name: 'DeliveredRemainingItems', type: sql.Int, editable: true },
    { name: 'InstalledRemainingItems', type: sql.Int, editable: true },
];

const recoveryFollowUpsColumns = [
    { name: 'Dated', type: sql.DateTime, editable: false },
    { name: 'vtp', type: sql.VarChar(20), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'Client_ID', type: sql.VarChar(32), editable: true },
    { name: 'ContactPerson', type: sql.VarChar(40), editable: true },
    { name: 'Designation', type: sql.VarChar(40), editable: true },
    { name: 'ClientDepartment', type: sql.VarChar(250), editable: true },
    { name: 'Narration', type: sql.Text, editable: true },
    { name: 'NoOfCopies', type: sql.SmallInt, editable: true },
    { name: 'Cancelled', type: sql.SmallInt, editable: true },
    { name: 'CancellDate', type: sql.DateTime, editable: true },
    { name: 'CancelReason', type: sql.VarChar(256), editable: true },
    { name: 'prp_id', type: sql.VarChar(8), editable: true },
    { name: 'chk_id', type: sql.VarChar(8), editable: true },
    { name: 'app_id', type: sql.VarChar(8), editable: true },
    { name: 'phoneNo', type: sql.VarChar(40), editable: true },
    { name: 'mobileNo', type: sql.VarChar(40), editable: true },
    { name: 'faxNo', type: sql.VarChar(40), editable: true },
    { name: "RefVoucherNo", type: sql.VarChar(70), editable: true },
    { name: "RefVTP", type: sql.VarChar(20), editable: true },
    { name: "RefMnth", type: sql.VarChar(8), editable: true },
    { name: "RefLocation", type: sql.VarChar(8), editable: true },
    { name: "RefVNo", type: sql.Int, editable: true },
    { name: "InitialPayment", type: sql.Int, editable: true },
    { name: "BalanceAmount", type: sql.Int, editable: true },
    { name: "TotalOutstandings", type: sql.Int, editable: true },
    { name: "LastCommitmentAmount", type: sql.Int, editable: true },
    { name: "Remarks", type: sql.VarChar(256), editable: true },
    { name: 'OverDue', type: sql.DateTime, editable: true },
    { name: 'LastCommitmentDate', type: sql.DateTime, editable: true },
];

const recoveryFollowUpsItemColumns = [
    { name: 'Dated', type: sql.DateTime, editable: false },
    { name: 'vtp', type: sql.VarChar(20), editable: false },
    { name: 'Location', type: sql.VarChar(8), editable: false },
    { name: 'Mnth', type: sql.VarChar(8), editable: false },
    { name: 'vno', type: sql.Int, editable: false },
    { name: 'srno', type: sql.Int, editable: false },
    { name: 'CommitmentDate', type: sql.DateTime, editable: true },
    { name: 'Amount', type: sql.Float, editable: true },
    { name: 'Remarks', type: sql.VarChar(250), editable: true },
    { name: 'totalOutstandings', type: sql.Int, editable: true },
    { name: 'DeliveredItems', type: sql.Int, editable: true },
    { name: 'InstalledItems', type: sql.Int, editable: true },
    { name: 'DeliveredRemainingItems', type: sql.Int, editable: true },
    { name: 'InstalledRemainingItems', type: sql.Int, editable: true },
    { name: 'overDue', type: sql.DateTime, editable: true }
];

const deliveryChallanColumns = [
    { name: "Dated", type: sql.DateTime, editable: false },
    { name: "AorB", type: sql.TinyInt, editable: false },
    { name: "VTP", type: sql.VarChar(20), editable: false },
    { name: "Mnth", type: sql.VarChar(8), editable: false },
    { name: "Location", type: sql.VarChar(8), editable: false },
    { name: "VNo", type: sql.Int, editable: false },
    { name: "CO_Vtp", type: sql.VarChar(20), editable: true },
    { name: "CO_Mnth", type: sql.VarChar(8), editable: true },
    { name: "CO_Location", type: sql.VarChar(8), editable: true },
    { name: "CO_VNo", type: sql.Int, editable: true },
    { name: "Client_ID", type: sql.VarChar(32), editable: true },
    { name: "Project_ID", type: sql.VarChar(5), editable: true },
    { name: "CostCenter_ID", type: sql.VarChar(5), editable: true },
    { name: "Rep", type: sql.VarChar(20), editable: true },
    { name: "PONo", type: sql.VarChar(25), editable: true },
    { name: "PODate", type: sql.DateTime, editable: true },
    { name: "Attn", type: sql.VarChar(35), editable: true },
    { name: "RefNo", type: sql.VarChar(25), editable: true },
    { name: "RefDate", type: sql.DateTime, editable: true },
    { name: "Givento", type: sql.VarChar(100), editable: true },
    { name: "SalesMan_ID", type: sql.VarChar(32), editable: true },
    { name: "Godown_ID", type: sql.VarChar(8), editable: true },
    { name: "Narration", type: sql.VarChar(256), editable: true },
    { name: "DriverName", type: sql.VarChar(250), editable: true },
    { name: "VehicleNo", type: sql.VarChar(250), editable: true },
    { name: "prp_ID", type: sql.VarChar(8), editable: true },
    { name: "chk_ID", type: sql.VarChar(8), editable: true },
    { name: "app_ID", type: sql.VarChar(8), editable: true },
    { name: "NoOfCopies", type: sql.SmallInt, editable: true },
    { name: "cancelled", type: sql.SmallInt, editable: true },
    { name: "CancellDate", type: sql.DateTime, editable: true },
    { name: "CancelReason", type: sql.VarChar(256), editable: true },
    { name: "CreationDate", type: sql.DateTime, editable: true },
    { name: "CancelBy_ID", type: sql.VarChar(8), editable: true },
    { name: "Chk_Date", type: sql.DateTime, editable: true },
    { name: "App_Date", type: sql.DateTime, editable: true },
];

const deliveryChallanItemColumns = [
    { name: "Dated", type: sql.DateTime, editable: true },
    { name: "VTP", type: sql.VarChar(20), editable: true },
    { name: "Mnth", type: sql.VarChar(8), editable: true },
    { name: "Location", type: sql.VarChar(8), editable: true },
    { name: "VNo", type: sql.Int, editable: true },
    { name: "SrNo", type: sql.Int, editable: true },
    { name: "Item_ID", type: sql.VarChar(32), editable: true },
    { name: "ExpiryDate", type: sql.DateTime, editable: true },
    { name: "BatchNo", type: sql.VarChar(40), editable: true },
    { name: "Type", type: sql.VarChar(70), editable: true },
    { name: "Meters", type: sql.Float, editable: true },
    { name: "Than", type: sql.Float, editable: true },
    { name: "Cartons", type: sql.Float, editable: true },
    { name: "QtyPerCTN", type: sql.Float, editable: true },
    { name: "Qty", type: sql.Float, editable: true },
    { name: "Unit", type: sql.VarChar(10), editable: true },
    { name: "Remarks", type: sql.Text, editable: true },
    { name: "CO_VoucherNo", type: sql.VarChar(30), editable: true },
    { name: "CO_VTP", type: sql.VarChar(20), editable: true },
    { name: "CO_Mnth", type: sql.VarChar(8), editable: true },
    { name: "CO_Location", type: sql.VarChar(8), editable: true },
    { name: "CO_VNo", type: sql.Int, editable: true },
    { name: "CO_SrNo", type: sql.Int, editable: true },
    { name: "Godown_ID", type: sql.VarChar(8), editable: true },
    { name: "Details", type: sql.Text, editable: true },
    { name: "Rate", type: sql.Float, editable: true },
    { name: "Total", type: sql.Float, editable: true },
    { name: "RefVQty", type: sql.Float, editable: true },
    { name: "SubUnit", type: sql.VarChar(10), editable: true },
    { name: "Weights", type: sql.Float, editable: true },
    { name: "RefVoucherNo", type: sql.VarChar(70), editable: true },
    { name: "RefVTP", type: sql.VarChar(20), editable: true },
    { name: "RefMnth", type: sql.VarChar(8), editable: true },
    { name: "RefLocation", type: sql.VarChar(8), editable: true },
    { name: "RefVNo", type: sql.Int, editable: true },
    { name: "RefSrNo", type: sql.Int, editable: true },
    { name: "EngineNo", type: sql.VarChar(40), editable: true },
    { name: "AlternatorNo", type: sql.VarChar(40), editable: true },
];

const salesTaxInvoiceColumns = [
    { name: "Dated", type: sql.DateTime, editable: false },
    { name: "AorB", type: sql.TinyInt, editable: false },
    { name: "VTP", type: sql.VarChar(20), editable: false },
    { name: "Mnth", type: sql.VarChar(8), editable: false },
    { name: "Location", type: sql.VarChar(8), editable: false },
    { name: "vno", type: sql.Int, editable: false },
    { name: "Client_id", type: sql.VarChar(32), editable: true },
    { name: "SalesMan_id", type: sql.VarChar(32), editable: true },
    { name: "SalesMan_id2", type: sql.VarChar(32), editable: true },
    { name: "SalesMan_id3", type: sql.VarChar(32), editable: true },
    { name: "SalesMan_id4", type: sql.VarChar(32), editable: true },
    { name: "SalesMan_id5", type: sql.VarChar(32), editable: true },
    { name: "Attn", type: sql.VarChar(90), editable: true },
    { name: "Rep", type: sql.VarChar(40), editable: true },
    { name: "PONo", type: sql.VarChar(40), editable: true },
    { name: "PODate", type: sql.DateTime, editable: true },
    { name: "RefNo", type: sql.VarChar(25), editable: true },
    { name: "RefDate", type: sql.DateTime, editable: true },
    { name: "Givento", type: sql.VarChar(100), editable: true },
    { name: "cash_id", type: sql.VarChar(32), editable: true },
    { name: "CCard_id", type: sql.VarChar(32), editable: true },
    { name: "GrossAmount", type: sql.Float, editable: true },
    { name: "SalesTaxPercentage", type: sql.Float, editable: true },
    { name: "ASalesTaxPercentage", type: sql.Float, editable: true },
    { name: "GridST", type: sql.Float, editable: true },
    { name: "SalesTaxAmount", type: sql.Float, editable: true },
    { name: "STaxAmt", type: sql.Float, editable: true },
    { name: "EDPerc", type: sql.Float, editable: true },
    { name: "GridED", type: sql.Float, editable: true },
    { name: "EDAmt", type: sql.Float, editable: true },
    { name: "DiscountPercent", type: sql.Float, editable: true },
    { name: "Discount", type: sql.Float, editable: true },
    { name: "AdvITaxPerc", type: sql.Float, editable: true },
    { name: "AdvITaxAmt", type: sql.Float, editable: true },
    { name: "Freight", type: sql.Float, editable: true },
    { name: "NetAmount", type: sql.Float, editable: true },
    { name: "Advance", type: sql.Float, editable: true },
    { name: "AdvanceCCard", type: sql.Float, editable: true },
    { name: "Balance", type: sql.Float, editable: true },
    { name: "SMTotal", type: sql.Float, editable: true },
    { name: "CommissionPAage", type: sql.Float, editable: true },
    { name: "CommissionAmount", type: sql.Float, editable: true },
    { name: "SMCPAge2", type: sql.Float, editable: true },
    { name: "SMCPAge3", type: sql.Float, editable: true },
    { name: "SMCPAge4", type: sql.Float, editable: true },
    { name: "SMCPAge5", type: sql.Float, editable: true },
    { name: "SMCAmt2", type: sql.Float, editable: true },
    { name: "SMCAmt3", type: sql.Float, editable: true },
    { name: "SMCAmt4", type: sql.Float, editable: true },
    { name: "SMCAmt5", type: sql.Float, editable: true },
    { name: "CCardPAge", type: sql.Float, editable: true },
    { name: "CCardCharges", type: sql.Float, editable: true },
    { name: "CCardAmount", type: sql.Float, editable: true },
    { name: "Narration", type: sql.VarChar(256), editable: true },
    { name: "Address", type: sql.VarChar(120), editable: true },
    { name: "Phone", type: sql.VarChar(40), editable: true },
    { name: "BookNo", type: sql.VarChar(40), editable: true },
    { name: "FreightSM", type: sql.Float, editable: true },
    { name: "DeliveryDate", type: sql.DateTime, editable: true },
    { name: "CostCenter_ID", type: sql.VarChar(5), editable: true },
    { name: "Project_ID", type: sql.VarChar(5), editable: true },
    { name: "FirstSrNo", type: sql.Int, editable: true },
    { name: "SaleAcc_ID", type: sql.VarChar(32), editable: true },
    { name: "Vendor_ID", type: sql.VarChar(32), editable: true },
    { name: "VendorAmt", type: sql.Float, editable: true },
    { name: "prp_id", type: sql.VarChar(8), editable: true },
    { name: "chk_id", type: sql.VarChar(8), editable: true },
    { name: "app_id", type: sql.VarChar(8), editable: true },
    { name: "NoOfCopies", type: sql.SmallInt, editable: true },
    { name: "cancelled", type: sql.SmallInt, editable: true },
    { name: "CancellDate", type: sql.DateTime, editable: true },
    { name: "CancelReason", type: sql.VarChar(256), editable: true },
    { name: "CreationDate", type: sql.DateTime, editable: true },
    { name: "DriverName", type: sql.VarChar(240), editable: true },
    { name: "VehicleNo", type: sql.VarChar(240), editable: true },
    { name: "OrderTime", type: sql.VarChar(10), editable: true },
    { name: "KitchenTime", type: sql.VarChar(5), editable: true },
    { name: "ServingTime", type: sql.VarChar(5), editable: true },
    { name: "TypeOfKOT", type: sql.VarChar(40), editable: true },
    { name: "ShopNo", type: sql.VarChar(70), editable: true },
    { name: "CashDate", type: sql.DateTime, editable: true },
    { name: "CashTime", type: sql.VarChar(20), editable: true },
    { name: "RNR", type: sql.SmallInt, editable: true },
    { name: "CancelBy_ID", type: sql.VarChar(8), editable: true },
    { name: "Chk_Date", type: sql.DateTime, editable: true },
    { name: "App_Date", type: sql.DateTime, editable: true },
    { name: "ItemWDisc", type: sql.Float, editable: true },
    { name: "COAdvance", type: sql.Float, editable: true },
    { name: "BalanceToReturn", type: sql.Float, editable: true },
    { name: "Credit", type: sql.SmallInt, editable: true },
    { name: "WarranterName", type: sql.VarChar(70), editable: true },
    { name: "WarranterDesig", type: sql.VarChar(70), editable: true },
    { name: "SaleBasis", type: sql.SmallInt, editable: true },
    { name: "TP", type: sql.Float, editable: true },
    { name: "ComputerName", type: sql.VarChar(120), editable: true },
    { name: "chln_rcvd", type: sql.SmallInt, editable: true },
    { name: "chln_date", type: sql.DateTime, editable: true },
];

const salesTaxInvoiceItemColumns = [
    { name: "Dated", type: sql.DateTime, editable: true },
    { name: "VTP", type: sql.VarChar(20), editable: true },
    { name: "Mnth", type: sql.VarChar(8), editable: true },
    { name: "Location", type: sql.VarChar(8), editable: true },
    { name: "vno", type: sql.Int, editable: true },
    { name: "srno", type: sql.Int, editable: true },
    { name: "Category", type: sql.VarChar(70), editable: true },
    { name: "item_id", type: sql.VarChar(32), editable: true },
    { name: "StockType", type: sql.VarChar(3), editable: true },
    { name: "Item_Title", type: sql.VarChar(70), editable: true },
    { name: "Model", type: sql.VarChar(200), editable: true },
    { name: "SampleQty", type: sql.Float, editable: true },
    { name: "QtyType", type: sql.VarChar(2), editable: true },
    { name: "Cartons", type: sql.Float, editable: true },
    { name: "QtyPerCTN", type: sql.Float, editable: true },
    { name: "Length", type: sql.Float, editable: true },
    { name: "Width", type: sql.Float, editable: true },
    { name: "Rolls", type: sql.Float, editable: true },
    { name: "Qty", type: sql.Float, editable: true },
    { name: "Unit", type: sql.VarChar(10), editable: true },
    { name: "Color", type: sql.VarChar(10), editable: true },
    { name: "GrossRate", type: sql.Float, editable: true },
    { name: "DiscPerc", type: sql.Float, editable: true },
    { name: "Discount", type: sql.Float, editable: true },
    { name: "Rate", type: sql.Float, editable: true },
    { name: "Total", type: sql.Float, editable: true },
    { name: "STPerc", type: sql.Float, editable: true },
    { name: "STAmt", type: sql.Float, editable: true },
    { name: "EDPerc", type: sql.Float, editable: true },
    { name: "EDAmt", type: sql.Float, editable: true },
    { name: "SMRate", type: sql.Float, editable: true },
    { name: "SMTotal", type: sql.Float, editable: true },
    { name: "CostRate", type: sql.Float, editable: true },
    { name: "CostTotal", type: sql.Float, editable: true },
    { name: "CreditTo_ID", type: sql.VarChar(32), editable: true },
    { name: "Remarks", type: sql.Text, editable: true },
    { name: "CO_VoucherNo", type: sql.VarChar(40), editable: true },
    { name: "CO_VTP", type: sql.VarChar(20), editable: true },
    { name: "CO_Mnth", type: sql.VarChar(8), editable: true },
    { name: "CO_Location", type: sql.VarChar(8), editable: true },
    { name: "CO_VNo", type: sql.Int, editable: true },
    { name: "CO_SrNo", type: sql.Int, editable: true },
    { name: "BatchNo", type: sql.VarChar(40), editable: true },
    { name: "ExpiryDate", type: sql.DateTime, editable: true },
    { name: "SMCPAge", type: sql.Float, editable: true },
    { name: "SMCAmt", type: sql.Float, editable: true },
    { name: "GodownID", type: sql.VarChar(5), editable: true },
    { name: "Project_ID", type: sql.VarChar(5), editable: true },
    { name: "CostCenter_ID", type: sql.VarChar(5), editable: true },
    { name: "Vendor_ID", type: sql.VarChar(32), editable: true },
    { name: "VendorAmt", type: sql.Float, editable: true },
    { name: "VisitDate", type: sql.DateTime, editable: true },
    { name: "FSRNo", type: sql.VarChar(240), editable: true },
    { name: "KVA", type: sql.VarChar(40), editable: true },
    { name: "Hrs", type: sql.VarChar(20), editable: true },
    { name: "FromDate", type: sql.DateTime, editable: true },
    { name: "ToDate", type: sql.DateTime, editable: true },
    { name: "Duration", type: sql.Float, editable: true },
    { name: "RefVQty", type: sql.Float, editable: true },
    { name: "QtyDisc", type: sql.Float, editable: true },
    { name: "QtyS", type: sql.Float, editable: true },
    { name: "QtyD", type: sql.Float, editable: true },
    { name: "SubUnit", type: sql.VarChar(10), editable: true },
    { name: "SReturn", type: sql.SmallInt, editable: true },
    { name: "Size", type: sql.VarChar(32), editable: true },
    { name: "Blisters", type: sql.Float, editable: true },
    { name: "Tablets", type: sql.Float, editable: true },
    { name: "TotalBlisters", type: sql.Float, editable: true },
    { name: "TotalTablets", type: sql.Float, editable: true },
    { name: "BonusQty", type: sql.Float, editable: true },
    { name: "QtySize01", type: sql.Float, editable: true },
    { name: "QtySize02", type: sql.Float, editable: true },
    { name: "QtySize03", type: sql.Float, editable: true },
    { name: "QtySize04", type: sql.Float, editable: true },
    { name: "QtySize05", type: sql.Float, editable: true },
    { name: "QtySize06", type: sql.Float, editable: true },
    { name: "QtySize07", type: sql.Float, editable: true },
    { name: "QtySize08", type: sql.Float, editable: true },
    { name: "QtySize09", type: sql.Float, editable: true },
    { name: "QtySize10", type: sql.Float, editable: true },
    { name: "QtySize11", type: sql.Float, editable: true },
    { name: "QtySize12", type: sql.Float, editable: true },
    { name: "TPPerc", type: sql.Float, editable: true },
    { name: "CQty", type: sql.Float, editable: true },
    { name: "Height", type: sql.Float, editable: true },
    { name: "Depth", type: sql.Float, editable: true },
    { name: "NoOfPieces", type: sql.Float, editable: true },
    { name: "PieceRate", type: sql.Float, editable: true },
    { name: "PurItem_ID", type: sql.VarChar(32), editable: true },
    { name: "PurVoucherNo", type: sql.VarChar(40), editable: true },
    { name: "PurVTP", type: sql.VarChar(20), editable: true },
    { name: "PurMnth", type: sql.VarChar(8), editable: true },
    { name: "PurLocation", type: sql.VarChar(8), editable: true },
    { name: "PurVNo", type: sql.Int, editable: true },
    { name: "PurSrNo", type: sql.Int, editable: true },
    { name: "PurQty", type: sql.Float, editable: true },
];

const coa32Columns = [
    { name: 'id', type: sql.VarChar(32) },
    { name: 'title', type: sql.VarChar(90) },
    { name: 'Opn_Amt', type: sql.Float },
    { name: 'OpnCoa32LW', type: sql.Float },
    { name: 'SMR_Amt', type: sql.Float },
    { name: 'GroupID', type: sql.VarChar(250) },
    { name: 'GroupID2', type: sql.VarChar(250) },
    { name: 'Location', type: sql.VarChar(8) },
    { name: 'OpenForAllLocations', type: sql.TinyInt },
    { name: 'Deactive', type: sql.TinyInt },
    { name: 'LinkWithType_ID', type: sql.VarChar(20) },
    { name: 'chk_id', type: sql.VarChar(8) },
    { name: 'Chk_Date', type: sql.DateTime },
    { name: 'InterCompany', type: sql.TinyInt },
];

const coa3Columns = [
    { name: 'id', type: sql.VarChar(32) },
    { name: 'id1', type: sql.VarChar(2) },
    { name: 'id2', type: sql.VarChar(10) },
    { name: 'id3', type: sql.VarChar(4) },
    { name: 'ULID1', type: sql.VarChar(28) },
    { name: 'ULID2', type: sql.VarChar(5) },
    { name: 'prp_id', type: sql.VarChar(8) },
    { name: 'atp2_ID', type: sql.VarChar(2) },
    { name: 'CreationDate', type: sql.DateTime }
];

const coa321Columns = [
    { name: 'id', type: sql.VarChar(32) },
    { name: 'add1', type: sql.VarChar(100) },
    { name: 'add2', type: sql.VarChar(100) },
    { name: 'add3', type: sql.VarChar(100) },
    { name: 'tel', type: sql.VarChar(250) },
    { name: 'Mobile', type: sql.VarChar(250) },
    { name: 'fax', type: sql.VarChar(50) },
    { name: 'stn', type: sql.VarChar(17) },
    { name: 'ntn', type: sql.VarChar(20) },
    { name: 'email', type: sql.VarChar(250) },
    { name: 'EmailO', type: sql.VarChar(250) },
    { name: 'contact_person', type: sql.VarChar(40) },
    { name: 'url', type: sql.VarChar(50) },
    { name: 'City', type: sql.VarChar(70) },
    { name: 'cr_days', type: sql.SmallInt },
    { name: 'CreditLimit', type: sql.Float },
    { name: 'CrDaysManager', type: sql.SmallInt },
    { name: 'CreditLimitManager', type: sql.Float },
    { name: 'SalesDisc', type: sql.Float },
    { name: 'Emp_ID', type: sql.VarChar(32) },
    { name: 'SM2_ID', type: sql.VarChar(32) },
    { name: 'SM3_ID', type: sql.VarChar(32) },
    { name: 'Status', type: sql.VarChar(120) },
    { name: 'Region', type: sql.VarChar(120) },
    { name: 'CP1', type: sql.VarChar(120) },
    { name: 'CP2', type: sql.VarChar(120) },
    { name: 'CP3', type: sql.VarChar(120) },
    { name: 'Ph1', type: sql.VarChar(120) },
    { name: 'Ph2', type: sql.VarChar(120) },
    { name: 'Ph3', type: sql.VarChar(120) },
    { name: 'Party_ID', type: sql.VarChar(32) },
    { name: 'Account_ID', type: sql.VarChar(32) },
    { name: 'Location', type: sql.VarChar(8) },
    { name: 'ForeignParty', type: sql.TinyInt },
    { name: 'act', type: sql.SmallInt },
    { name: 'City_ID', type: sql.VarChar(32) },
    { name: 'Country_ID', type: sql.VarChar(32) },
    { name: 'webID', type: sql.VarChar(250) },
    { name: 'webPWD', type: sql.VarChar(250) },
    { name: 'TaxSectionDesc', type: sql.VarChar(40) },
    { name: 'TaxSectionCode01', type: sql.VarChar(40) },
    { name: 'TaxSectionCode02', type: sql.VarChar(40) },
    { name: 'TaxSection', type: sql.VarChar(40) },
    { name: 'RetainableLimit', type: sql.Float },
    { name: 'PartyLogo', type: sql.Image },
    { name: 'PartyLogoSize', type: sql.Int },
    { name: 'UrduTitle', type: sql.VarChar(70) },
    { name: 'UrduAdd1', type: sql.VarChar(100) },
    { name: 'UrduAdd2', type: sql.VarChar(100) },
    { name: 'SalesMan_ID', type: sql.VarChar(32) },
    { name: 'ClientPwd', type: sql.VarChar(20) },
    { name: 'CPCellNo1', type: sql.VarChar(50) },
    { name: 'CPCellNo2', type: sql.VarChar(50) },
    { name: 'CPCellNo3', type: sql.VarChar(50) },
    { name: 'CPCellNo4', type: sql.VarChar(50) },
    { name: 'CPCellNo5', type: sql.VarChar(50) },
    { name: 'CPCellNo6', type: sql.VarChar(50) },
    { name: 'CPEmail1', type: sql.VarChar(50) },
    { name: 'CPEmail2', type: sql.VarChar(50) },
    { name: 'CPEmail3', type: sql.VarChar(50) },
    { name: 'CPEmail4', type: sql.VarChar(50) },
    { name: 'CPEmail5', type: sql.VarChar(50) },
    { name: 'CPEmail6', type: sql.VarChar(50) },
    { name: 'BankACNo', type: sql.VarChar(40) },
    { name: 'BankName', type: sql.VarChar(100) },
    { name: 'ChqTitle', type: sql.VarChar(70) },
    { name: 'ClientPwd1', type: sql.VarChar(20) },
    { name: 'ClientPwd2', type: sql.VarChar(20) },
    { name: 'contact_person6', type: sql.VarChar(40) },
    { name: 'contact_person7', type: sql.VarChar(40) },
    { name: 'Designation6', type: sql.VarChar(40) },
    { name: 'Designation7', type: sql.VarChar(40) },
    { name: 'CPCellNo7', type: sql.VarChar(50) },
    { name: 'CPCellNo8', type: sql.VarChar(50) },
    { name: 'CPEmail7', type: sql.VarChar(50) },
    { name: 'CPEmail8', type: sql.VarChar(50) },
    { name: 'Terms', type: sql.VarChar(10) },
    { name: 'Mode', type: sql.VarChar(70) },
    { name: 'OTaxAcc_ID', type: sql.VarChar(32) },
    { name: 'LicenceNo', type: sql.VarChar(40) },
    { name: 'LicValidTill', type: sql.VarChar(40) },
    { name: 'ValueAddPerc', type: sql.Float },
    { name: 'SkillLevel_ID', type: sql.VarChar(3) },
    { name: 'SalesMan1_ID', type: sql.VarChar(32) },
    { name: 'SalesMan2_ID', type: sql.VarChar(32) },
    { name: 'SM1Location', type: sql.VarChar(8) },
    { name: 'SM2Location', type: sql.VarChar(8) },
    { name: 'L1CrDays', type: sql.SmallInt },
    { name: 'L2CrDays', type: sql.SmallInt },
    { name: 'L1CrLimit', type: sql.Float },
    { name: 'formalTitle', type: sql.VarChar(8) },
    { name: 'surName', type: sql.VarChar(32) },
    { name: 'firstName', type: sql.VarChar(32) },
]

const coa31Columns = [
    { name: "id", type: sql.VarChar, length: 32, isRequired: true },
    { name: "Category_ID", type: sql.VarChar, length: 3, isRequired: true },
    { name: "Manufacturer_ID", type: sql.VarChar, length: 3, isRequired: true },
    { name: "SrNo", type: sql.VarChar, length: 4, isRequired: false },
    { name: "Title", type: sql.VarChar, length: 254, isRequired: true },
    { name: "Model", type: sql.VarChar, length: 204, isRequired: true },
    { name: "Brand", type: sql.VarChar, length: 60, isRequired: false },
    { name: "PType", type: sql.VarChar, length: 2, isRequired: false },
    { name: "Unit", type: sql.VarChar, length: 10, isRequired: true },
    { name: "SubUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "AppUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "PackSize", type: sql.Float, isRequired: false },
    { name: "Category", type: sql.VarChar, length: 70, isRequired: false },
    { name: "Crop", type: sql.VarChar, length: 30, isRequired: false },
    { name: "opn_qty", type: sql.Float, isRequired: false },
    { name: "OpnCoa31LW", type: sql.Float, isRequired: false },
    { name: "Purc_Rate", type: sql.Float, isRequired: false },
    { name: "Sale_Rate", type: sql.Float, isRequired: true },
    { name: "MinLvl", type: sql.Float, isRequired: false },
    { name: "MaxLvl", type: sql.Float, isRequired: false },
    { name: "SMR_Qty", type: sql.Float, isRequired: false },
    { name: "RMaterial", type: sql.TinyInt, isRequired: false },
    { name: "PMaterial", type: sql.TinyInt, isRequired: false },
    { name: "FMaterial", type: sql.TinyInt, isRequired: false },
    { name: "OMaterial", type: sql.TinyInt, isRequired: true },
    { name: "Purchase_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "Sale_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "OpnAcc_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "Godown_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "prp_id", type: sql.VarChar, length: 8, isRequired: false },
    { name: "HSCode", type: sql.VarChar, length: 40, isRequired: false },
    { name: "ServiceItem", type: sql.TinyInt, isRequired: false },
    { name: "Imported", type: sql.TinyInt, isRequired: false },
    { name: "Party_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "RackNo", type: sql.VarChar, length: 64, isRequired: false },
    { name: "Manufacture", type: sql.VarChar, length: 70, isRequired: false },
    { name: "PartNo", type: sql.VarChar, length: 70, isRequired: false },
    { name: "Make", type: sql.VarChar, length: 70, isRequired: false },
    { name: "ModelN", type: sql.VarChar, length: 70, isRequired: false },
    { name: "Country", type: sql.VarChar, length: 70, isRequired: true },
    { name: "Weight", type: sql.Float, isRequired: false },
    { name: "WarpYarn", type: sql.Float, isRequired: false },
    { name: "WeftYarn", type: sql.Float, isRequired: false },
    { name: "WarpT", type: sql.VarChar, length: 40, isRequired: false },
    { name: "WeftT", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Warp", type: sql.Float, isRequired: false },
    { name: "Weft", type: sql.Float, isRequired: false },
    { name: "Width", type: sql.Float, isRequired: false },
    { name: "WidthUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Length", type: sql.Float, isRequired: false },
    { name: "LengthUnit", type: sql.TinyInt, isRequired: false },
    { name: "GSM", type: sql.Float, isRequired: false },
    { name: "GSMUnit", type: sql.TinyInt, isRequired: false },
    { name: "ShrinkPercent", type: sql.Float, isRequired: false },
    { name: "Colour", type: sql.VarChar, length: 35, isRequired: false },
    { name: "Design", type: sql.VarChar, length: 20, isRequired: false },
    { name: "Grade", type: sql.VarChar, length: 20, isRequired: false },
    { name: "HEM", type: sql.SmallInt, isRequired: false },
    { name: "HEMUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "LabelMark", type: sql.VarChar, length: 20, isRequired: false },
    { name: "CuttingSpecs", type: sql.VarChar, length: 20, isRequired: false },
    { name: "PackingPolyBag", type: sql.Float, isRequired: false },
    { name: "PackingCTN", type: sql.Float, isRequired: false },
    { name: "Selvedge", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Weave", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Fiber", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Twill", type: sql.VarChar, length: 10, isRequired: false },
    { name: "FabricComposition", type: sql.VarChar, length: 10, isRequired: false },
    { name: "ActualYarnCount", type: sql.VarChar, length: 10, isRequired: false },
    { name: "CountCV", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Strength", type: sql.VarChar, length: 10, isRequired: false },
    { name: "StrengthCV", type: sql.VarChar, length: 10, isRequired: false },
    { name: "CLSP", type: sql.VarChar, length: 10, isRequired: false },
    { name: "TPI", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Thin", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Thick", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Neps", type: sql.VarChar, length: 10, isRequired: false },
    { name: "IPI", type: sql.VarChar, length: 10, isRequired: false },
    { name: "RKM", type: sql.VarChar, length: 10, isRequired: false },
    { name: "ConeWt", type: sql.Float, isRequired: false },
    { name: "TotalWt", type: sql.Float, isRequired: false },
    { name: "ConesPerBag", type: sql.VarChar, length: 40, isRequired: false },
    { name: "TotalCones", type: sql.Float, isRequired: false },
    { name: "AllowNegativeBalances", type: sql.TinyInt, isRequired: false },
    { name: "CreationDate", type: sql.DateTime, isRequired: false },
    { name: "PictureFile", type: sql.VarChar, length: 250, isRequired: false },
    { name: "ButtonColor", type: sql.Float, isRequired: false },
    { name: "ForeColor", type: sql.Float, isRequired: false },
    { name: "DeActivate", type: sql.TinyInt, isRequired: false },
    { name: "CostCenter_ID", type: sql.VarChar, length: 5, isRequired: false },
    { name: "WeightUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "SecondName", type: sql.VarChar, length: 120, isRequired: false },
    { name: "Factor", type: sql.Float, isRequired: false },
    { name: "DoWhatWithFactor", type: sql.VarChar, length: 10, isRequired: false },
    { name: "WeightPerUnit", type: sql.Float, isRequired: false },
    { name: "opq_sample", type: sql.Float, isRequired: false },
    { name: "Sub_Qty", type: sql.Float, isRequired: false },
    { name: "SampleCost", type: sql.Float, isRequired: false },
    { name: "Sample_Rate", type: sql.Float, isRequired: false },
    { name: "Blisters", type: sql.Float, isRequired: false },
    { name: "BlisterUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Tablets", type: sql.Float, isRequired: false },
    { name: "SampleBlisters", type: sql.Float, isRequired: false },
    { name: "SampleTablets", type: sql.Float, isRequired: false },
    { name: "SamplePackSize", type: sql.VarChar, length: 40, isRequired: false },
    { name: "RegisterationRenewalDate", type: sql.DateTime, isRequired: false },
    { name: "Client_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "Closed", type: sql.TinyInt, isRequired: false },
    { name: "UrduTitle", type: sql.VarChar, length: 2000, isRequired: false },
    { name: "Design_ID", type: sql.VarChar, length: 3, isRequired: false },
    { name: "SizeGroup_ID", type: sql.VarChar, length: 5, isRequired: false },
    { name: "ID1", type: sql.VarChar, length: 28, isRequired: false },
    { name: "ID2", type: sql.VarChar, length: 4, isRequired: false },
    { name: "TypeOfID", type: sql.SmallInt, isRequired: false },
    { name: "LevelNo", type: sql.TinyInt, isRequired: false },
    { name: "Details", type: sql.VarChar, length: 250, isRequired: true },
    { name: "Location", type: sql.VarChar, length: 8, isRequired: false },
    { name: "HSCodeOther", type: sql.VarChar, length: 40, isRequired: false },
    { name: "chk_id", type: sql.VarChar, length: 8, isRequired: false },
    { name: "app_id", type: sql.VarChar, length: 8, isRequired: false },
    { name: "Cancelled", type: sql.SmallInt, isRequired: false },
    { name: "CancellDate", type: sql.DateTime, isRequired: false },
    { name: "CancelReason", type: sql.VarChar, length: 256, isRequired: false },
    { name: "CancelBy_ID", type: sql.VarChar, length: 8, isRequired: false },
    { name: "Chk_Date", type: sql.DateTime, isRequired: false },
    { name: "App_Date", type: sql.DateTime, isRequired: false },
    { name: "FG_BallSizeUnit", type: sql.VarChar, length: 40, isRequired: false },
    { name: "GodownLocation", type: sql.VarChar, length: 40, isRequired: false },
    { name: "CapacityName", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Govt_ID", type: sql.VarChar, length: 10, isRequired: false },
    { name: "FG_BallType", type: sql.VarChar, length: 10, isRequired: false },
    { name: "FG_BigPieces", type: sql.Float, isRequired: false },
    { name: "FG_SmallPieces", type: sql.Float, isRequired: false },
    { name: "FG_ConvertToNormal", type: sql.Float, isRequired: false },
    { name: "FAMaterial", type: sql.TinyInt, isRequired: false },
    { name: "SalvageValuePercent", type: sql.Float, isRequired: false },
    { name: "FADepType_ID", type: sql.VarChar, length: 1, isRequired: false },
    { name: "PackUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "UI_OpnQtyAU", type: sql.Float, isRequired: false },
    { name: "Opn_QtyAU", type: sql.Float, isRequired: false },
    { name: "RJ_OpnQtyAU", type: sql.Float, isRequired: false },
    { name: "ConversionFactor", type: sql.Float, isRequired: false },
    { name: "ProductType_ID", type: sql.VarChar, length: 12, isRequired: false },
    { name: "NoOfCopies", type: sql.SmallInt, isRequired: false },
    { name: "NoOfCopiesDraft", type: sql.SmallInt, isRequired: false },
    { name: "CancelledBy", type: sql.VarChar, length: 8, isRequired: false },
    { name: "FinAcc06_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "MinSale_Rate", type: sql.Float, isRequired: false },
    { name: "TotalNoOfImpressions", type: sql.Int, isRequired: false },
    { name: "SalesTaxPercent", type: sql.Float, isRequired: false },
    { name: "EDPercent", type: sql.Float, isRequired: false },
    { name: "StoreLocation_ID", type: sql.VarChar, length: 12, isRequired: false },
    { name: "GSTPerc", type: sql.Float, isRequired: false },
    { name: "GSTAmt", type: sql.Float, isRequired: false },
    { name: "HCPerc", type: sql.Float, isRequired: false },
    { name: "HCAmt", type: sql.Float, isRequired: false },
    { name: "GrossPRate", type: sql.Float, isRequired: false },
    { name: "FuelCons", type: sql.Float, isRequired: false },
    { name: "Depreciation", type: sql.Float, isRequired: false },
    { name: "LubeOilCons", type: sql.Float, isRequired: false },
    { name: "Rental", type: sql.Float, isRequired: false },
    { name: "EquipmentCost", type: sql.Float, isRequired: false },
    { name: "MobOilCons", type: sql.Float, isRequired: false },
    { name: "DemoBCons", type: sql.Float, isRequired: false },
    { name: "Currency_ID", type: sql.VarChar, length: 10, isRequired: false },
    { name: "RevisionNumber", type: sql.Float, isRequired: false },
    { name: "FuelUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "LubeOilUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "MobOilUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "FirstMaterialTypeSrNo", type: sql.Int, isRequired: false },
    { name: "DepreciationUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "RentalUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "PTMaterial", type: sql.TinyInt, isRequired: false },
    { name: "FG_CartonType", type: sql.VarChar, length: 10, isRequired: false },
    { name: "FG_BallWeight", type: sql.Float, isRequired: false },
    { name: "ReProcessItem", type: sql.TinyInt, isRequired: false },
    { name: "PartNature", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Project_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "DMLNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "DMLValidity", type: sql.DateTime, isRequired: false },
    { name: "RegNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "RegDate", type: sql.DateTime, isRequired: false },
    { name: "RegValidity", type: sql.DateTime, isRequired: false },
    { name: "ShelfLife", type: sql.VarChar, length: 40, isRequired: false },
    { name: "DosageForm", type: sql.VarChar, length: 40, isRequired: false },
    { name: "WeightTabs", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Controlled", type: sql.SmallInt, isRequired: false },
    { name: "PackingOf", type: sql.VarChar, length: 40, isRequired: false },
    { name: "MRP", type: sql.Float, isRequired: false },
    { name: "TP", type: sql.Float, isRequired: false },
    { name: "MinBatchSize", type: sql.Float, isRequired: false },
    { name: "MaxBatchSize", type: sql.Float, isRequired: false },
    { name: "QCSample", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Reference", type: sql.VarChar, length: 40, isRequired: false },
    { name: "WeightTabsUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "PackingOfUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "SubUnitQty", type: sql.SmallInt, isRequired: false },
    { name: "PackingSubUnitQty", type: sql.VarChar, length: 40, isRequired: false },
    { name: "ControlledReason", type: sql.VarChar, length: 250, isRequired: false },
    { name: "BatchPrefix", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Shape", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Height", type: sql.Float, isRequired: false },
    { name: "HeightUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "Depth", type: sql.Float, isRequired: false },
    { name: "DepthUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "MinLvlMultiplier", type: sql.Float, isRequired: false },
    { name: "Govt", type: sql.TinyInt, isRequired: false },
    { name: "BatchNoSize", type: sql.TinyInt, isRequired: false },
    { name: "CostingFactor01", type: sql.Float, isRequired: false },
    { name: "CostingFactor02", type: sql.Float, isRequired: false },
    { name: "CostingFactor03", type: sql.Float, isRequired: false },
    { name: "CostingFactor04", type: sql.Float, isRequired: false },
    { name: "CostingFactor05", type: sql.Float, isRequired: false },
    { name: "CostingFactor06", type: sql.Float, isRequired: false },
    { name: "CostingFactor07", type: sql.Float, isRequired: false },
    { name: "CostingFactor08", type: sql.Float, isRequired: false },
    { name: "CostingFactor09", type: sql.Float, isRequired: false },
    { name: "BMType_ID", type: sql.VarChar, length: 40, isRequired: false },
    { name: "PQT", type: sql.Float, isRequired: false },
    { name: "StdBatchSize", type: sql.Float, isRequired: false },
    { name: "CapacityUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "WorkSpeedUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "ManSpeedUnit", type: sql.VarChar, length: 10, isRequired: false },
    { name: "WOBatch", type: sql.TinyInt, isRequired: false },
    { name: "RBDMaintenance", type: sql.TinyInt, isRequired: false },
    { name: "WarrantyDays", type: sql.SmallInt, isRequired: false },
    { name: "ComputerName", type: sql.VarChar, length: 120, isRequired: false },
    { name: "SemiFinished", type: sql.TinyInt, isRequired: false },
    { name: "LeadTimeDays", type: sql.SmallInt, isRequired: false },
    { name: "Series", type: sql.VarChar, length: 200, isRequired: false },
    { name: "BaseArticleNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "StandardWeight", type: sql.Float, isRequired: false },
]

const employeeColumns = [
    { name: "ID", type: sql.VarChar, length: 32, isRequired: true },
    { name: "CardId", type: sql.VarChar, length: 28, isRequired: false },
    { name: "SrNo", type: sql.VarChar, length: 4, isRequired: false },
    { name: "Title", type: sql.VarChar, length: 70, isRequired: false },
    { name: "LinkCode", type: sql.VarChar, length: 5, isRequired: false },
    { name: "Religion", type: sql.VarChar, length: 70, isRequired: false },
    { name: "HotList", type: sql.SmallInt, isRequired: false },
    { name: "Male", type: sql.TinyInt, isRequired: false },
    { name: "Female", type: sql.TinyInt, isRequired: false },
    { name: "SalaryPaymentModeCash", type: sql.TinyInt, isRequired: false },
    { name: "SalaryPaymentModeChq", type: sql.TinyInt, isRequired: false },
    { name: "SalaryPaymentModeBankTransfer", type: sql.TinyInt, isRequired: false, },
    { name: "BankName", type: sql.VarChar, length: 70, isRequired: false },
    { name: "BankBranch", type: sql.VarChar, length: 70, isRequired: false },
    { name: "BankAccountNo", type: sql.VarChar, length: 70, isRequired: false },
    { name: "CompAccountNo", type: sql.VarChar, length: 70, isRequired: false },
    { name: "NICNew", type: sql.VarChar, length: 40, isRequired: false },
    { name: "NICOld", type: sql.VarChar, length: 40, isRequired: false },
    { name: "add1", type: sql.VarChar, length: 250, isRequired: false },
    { name: "add2", type: sql.VarChar, length: 250, isRequired: false },
    { name: "add3", type: sql.VarChar, length: 250, isRequired: false },
    { name: "PostalCode", type: sql.VarChar, length: 30, isRequired: false },
    { name: "City", type: sql.VarChar, length: 30, isRequired: false },
    { name: "Province", type: sql.VarChar, length: 30, isRequired: false },
    { name: "Tel", type: sql.VarChar, length: 30, isRequired: false },
    { name: "Mobile", type: sql.VarChar, length: 30, isRequired: false },
    { name: "EMail", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Desig_ID", type: sql.VarChar, length: 3, isRequired: false },
    { name: "Deptt_ID", type: sql.VarChar, length: 3, isRequired: false },
    { name: "Line_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "act", type: sql.SmallInt, isRequired: false },
    { name: "FatherName", type: sql.VarChar, length: 32, isRequired: false },
    { name: "FatherOccupation", type: sql.VarChar, length: 70, isRequired: false },
    { name: "MotherName", type: sql.VarChar, length: 32, isRequired: false },
    { name: "MotherOccupation", type: sql.VarChar, length: 70, isRequired: false },
    { name: "ReligionMuslim", type: sql.TinyInt, isRequired: false },
    { name: "ReligionChristian", type: sql.TinyInt, isRequired: false },
    { name: "ReligionJewish", type: sql.TinyInt, isRequired: false },
    { name: "ReligionHindu", type: sql.TinyInt, isRequired: false },
    { name: "ReligionSikh", type: sql.TinyInt, isRequired: false },
    { name: "ReligionBudhist", type: sql.TinyInt, isRequired: false },
    { name: "ReligionOther", type: sql.TinyInt, isRequired: false },
    { name: "ReligionOtherMention", type: sql.VarChar, length: 70, isRequired: false },
    { name: "MaritalStatusSingle", type: sql.TinyInt, isRequired: false },
    { name: "MaritalStatusMarried", type: sql.TinyInt, isRequired: false },
    { name: "MaritalStatusSeperated", type: sql.TinyInt, isRequired: false },
    { name: "MaritalStatusWidowed", type: sql.TinyInt, isRequired: false },
    { name: "MarriageDate", type: sql.DateTime, isRequired: false },
    { name: "DateOfBirth", type: sql.DateTime, isRequired: false },
    { name: "Age", type: sql.Int, isRequired: false },
    { name: "GrossSalaryGiven", type: sql.Float, isRequired: false },
    { name: "PercentBP", type: sql.Float, isRequired: false },
    { name: "B_Pay", type: sql.Float, isRequired: false },
    { name: "allow", type: sql.Float, isRequired: false },
    { name: "allow1", type: sql.Float, isRequired: false },
    { name: "allow2", type: sql.Float, isRequired: false },
    { name: "allow3", type: sql.Float, isRequired: false },
    { name: "allow4", type: sql.Float, isRequired: false },
    { name: "allow5", type: sql.Float, isRequired: false },
    { name: "allow6", type: sql.Float, isRequired: false },
    { name: "allow7", type: sql.Float, isRequired: false },
    { name: "allow8", type: sql.Float, isRequired: false },
    { name: "allow9", type: sql.Float, isRequired: false },
    { name: "PercentAllow", type: sql.Float, isRequired: false },
    { name: "PercentAllow1", type: sql.Float, isRequired: false },
    { name: "PercentAllow2", type: sql.Float, isRequired: false },
    { name: "PercentAllow3", type: sql.Float, isRequired: false },
    { name: "PercentAllow4", type: sql.Float, isRequired: false },
    { name: "PercentAllow5", type: sql.Float, isRequired: false },
    { name: "PercentAllow6", type: sql.Float, isRequired: false },
    { name: "PercentAllow7", type: sql.Float, isRequired: false },
    { name: "PercentAllow8", type: sql.Float, isRequired: false },
    { name: "PercentAllow9", type: sql.Float, isRequired: false },
    { name: "FuelLitre", type: sql.Float, isRequired: false },
    { name: "Ded00", type: sql.Float, isRequired: false },
    { name: "Ded01", type: sql.Float, isRequired: false },
    { name: "Ded02", type: sql.Float, isRequired: false },
    { name: "Ded03", type: sql.Float, isRequired: false },
    { name: "Ded04", type: sql.Float, isRequired: false },
    { name: "Ded05", type: sql.Float, isRequired: false },
    { name: "Ded06", type: sql.Float, isRequired: false },
    { name: "RestrictLateComingDed", type: sql.TinyInt, isRequired: false },
    { name: "SalaryPayDate", type: sql.TinyInt, isRequired: false },
    { name: "Joindate", type: sql.DateTime, isRequired: false },
    { name: "Probation", type: sql.SmallInt, isRequired: false },
    { name: "ResignDate", type: sql.DateTime, isRequired: false },
    { name: "Terminated", type: sql.TinyInt, isRequired: false },
    { name: "NoticePeriod", type: sql.TinyInt, isRequired: false },
    { name: "ResignReason", type: sql.VarChar, length: 256, isRequired: false },
    { name: "ResignUpdatedBy", type: sql.VarChar, length: 8, isRequired: false },
    { name: "ResignUpdatedOn", type: sql.DateTime, isRequired: false },
    { name: "FixTime", type: sql.TinyInt, isRequired: false },
    { name: "IncomingTime", type: sql.VarChar, length: 5, isRequired: false },
    { name: "IncomingTimeLast", type: sql.VarChar, length: 5, isRequired: false },
    { name: "IncomingAMPM", type: sql.VarChar, length: 2, isRequired: false },
    { name: "BreakTime_Start", type: sql.VarChar, length: 5, isRequired: false },
    { name: "BreakTime_End", type: sql.VarChar, length: 5, isRequired: false },
    { name: "OutgoingTime", type: sql.VarChar, length: 5, isRequired: false },
    { name: "OutgoingAMPM", type: sql.VarChar, length: 2, isRequired: false },
    { name: "TotalWorkingHours", type: sql.VarChar, length: 5, isRequired: false },
    { name: "TotalDutyHours", type: sql.VarChar, length: 5, isRequired: false },
    { name: "CalculateOverTimeOnHours", type: sql.VarChar, length: 5, isRequired: false },
    { name: "TotalLateMinutesAllowed", type: sql.SmallInt, isRequired: false },
    { name: "RestDay", type: sql.VarChar, length: 20, isRequired: false },
    { name: "RestDayOther", type: sql.VarChar, length: 20, isRequired: false },
    { name: "LeavesOBTill", type: sql.DateTime, isRequired: false },
    { name: "AnnualLeaves", type: sql.SmallInt, isRequired: false },
    { name: "AnnualLeavesOB", type: sql.SmallInt, isRequired: false },
    { name: "Casualleaves", type: sql.SmallInt, isRequired: false },
    { name: "CasualleavesOB", type: sql.SmallInt, isRequired: false },
    { name: "SickLeaves", type: sql.SmallInt, isRequired: false },
    { name: "SickLeavesOB", type: sql.SmallInt, isRequired: false },
    { name: "confirmed", type: sql.SmallInt, isRequired: false },
    { name: "PFund", type: sql.Float, isRequired: false },
    { name: "SingleContribution", type: sql.Int, isRequired: false },
    { name: "PFundEmployee", type: sql.Float, isRequired: false },
    { name: "PFundEmployer", type: sql.Float, isRequired: false },
    { name: "EOBI", type: sql.SmallInt, isRequired: false },
    { name: "EOBIMax", type: sql.SmallInt, isRequired: false },
    { name: "EOBIOnBasic", type: sql.SmallInt, isRequired: false },
    { name: "EOBIDedFromPay", type: sql.SmallInt, isRequired: false },
    { name: "EOBINo", type: sql.VarChar, length: 70, isRequired: false },
    { name: "S_Security", type: sql.SmallInt, isRequired: false },
    { name: "SSMax", type: sql.SmallInt, isRequired: false },
    { name: "SSOnBasic", type: sql.SmallInt, isRequired: false },
    { name: "SSDedFromPay", type: sql.SmallInt, isRequired: false },
    { name: "SSNo", type: sql.VarChar, length: 70, isRequired: false },
    { name: "OverTimeRate", type: sql.Float, isRequired: false },
    { name: "OTOnBasicPay", type: sql.TinyInt, isRequired: false },
    { name: "OTCalcAfterMin", type: sql.VarChar, length: 5, isRequired: false },
    { name: "Daily", type: sql.SmallInt, isRequired: false },
    { name: "Monthly", type: sql.SmallInt, isRequired: false },
    { name: "Contract", type: sql.SmallInt, isRequired: false },
    { name: "PieceRate", type: sql.SmallInt, isRequired: false },
    { name: "WageRate", type: sql.Float, isRequired: false },
    { name: "Location_ID", type: sql.VarChar, length: 3, isRequired: false },
    { name: "Shift_ID", type: sql.VarChar, length: 3, isRequired: false },
    { name: "Exp_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "Control_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "Liability_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "OTExpense_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "OTLiability_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "TaxLiability_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "CostCenter_ID", type: sql.VarChar, length: 5, isRequired: false },
    { name: "Skills", type: sql.VarChar, length: 70, isRequired: false },
    { name: "Project_ID", type: sql.VarChar, length: 5, isRequired: false },
    { name: "HobbiesInterests", type: sql.VarChar, length: 70, isRequired: false },
    { name: "LanguageProficiencySpoken", type: sql.VarChar, length: 70, isRequired: false, },
    { name: "LanguageProficiencyWritten", type: sql.VarChar, length: 70, isRequired: false, },
    { name: "PhysicalDisability", type: sql.VarChar, length: 70, isRequired: false },
    { name: "desig", type: sql.VarChar, length: 24, isRequired: false },
    { name: "prp_id", type: sql.VarChar, length: 8, isRequired: false },
    { name: "Loc", type: sql.VarChar, length: 8, isRequired: false },
    { name: "CardIssuanceDate", type: sql.DateTime, isRequired: false },
    { name: "CardExpiryDate", type: sql.DateTime, isRequired: false },
    { name: "AllowAttendanceEditing", type: sql.TinyInt, isRequired: false },
    { name: "JobDescription", type: sql.text, isRequired: false },
    { name: "CreationDate", type: sql.DateTime, isRequired: true },
    { name: "EOBIR", type: sql.SmallInt, isRequired: false },
    { name: "EOBIRMax", type: sql.SmallInt, isRequired: false },
    { name: "SSR", type: sql.SmallInt, isRequired: false },
    { name: "SSRMax", type: sql.SmallInt, isRequired: false },
    { name: "PFE_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "PFR_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "EOBIExp_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "PESSIExp_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "cancelled", type: sql.SmallInt, isRequired: false },
    { name: "CancellDate", type: sql.DateTime, isRequired: false },
    { name: "CancelReason", type: sql.VarChar, length: 256, isRequired: false },
    { name: "chk_id", type: sql.VarChar, length: 8, isRequired: false },
    { name: "app_id", type: sql.VarChar, length: 8, isRequired: false },
    { name: "CancelBy_ID", type: sql.VarChar, length: 8, isRequired: false },
    { name: "Chk_Date", type: sql.DateTime, isRequired: false },
    { name: "App_Date", type: sql.DateTime, isRequired: false },
    { name: "LateRule_ID", type: sql.TinyInt, isRequired: false },
    { name: "ProximityCard", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Classification", type: sql.VarChar, length: 3, isRequired: false },
    { name: "SkillLevel", type: sql.VarChar, length: 3, isRequired: false },
    { name: "TokenNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "TokenDate", type: sql.DateTime, isRequired: false },
    { name: "BirthCert", type: sql.SmallInt, isRequired: false },
    { name: "SchoolCert", type: sql.SmallInt, isRequired: false },
    { name: "DoctorCert", type: sql.SmallInt, isRequired: false },
    { name: "NoEvidence", type: sql.SmallInt, isRequired: false },
    { name: "FamilyCode", type: sql.VarChar, length: 40, isRequired: false },
    { name: "EmergContactPerson", type: sql.VarChar, length: 70, isRequired: false },
    { name: "EmergencyContactNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "EmergencyRelation", type: sql.VarChar, length: 40, isRequired: false },
    { name: "LicenseNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "LicenseType", type: sql.VarChar, length: 3, isRequired: false },
    { name: "LicenseDate", type: sql.DateTime, isRequired: false },
    { name: "PlaceOfIssue", type: sql.VarChar, length: 40, isRequired: false },
    { name: "EOBICoverage", type: sql.SmallInt, isRequired: false },
    { name: "EOBIStatus", type: sql.VarChar, length: 40, isRequired: false },
    { name: "EOBICardNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "SSCoverage", type: sql.SmallInt, isRequired: false },
    { name: "SSIssueDate", type: sql.DateTime, isRequired: false },
    { name: "SSDispensary", type: sql.VarChar, length: 40, isRequired: false },
    { name: "Maker_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "Interviewer_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A1District", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A1Tehsil", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A1City", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A1PoliceStation", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A2District", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A2Tehsil", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A2City", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A2PoliceStation", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A3PoliceStation", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A3District", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A3Tehsil", type: sql.VarChar, length: 32, isRequired: false },
    { name: "A3City", type: sql.VarChar, length: 32, isRequired: false },
    { name: "Relation", type: sql.VarChar, length: 40, isRequired: false },
    { name: "EliteBallMember", type: sql.SmallInt, isRequired: false },
    { name: "RateForOpt4ForEliteBallMember", type: sql.Float, isRequired: false },
    { name: "HToM_TotalDutyHours", type: sql.SmallInt, isRequired: false },
    { name: "AgeVerification", type: sql.VarChar, length: 70, isRequired: false },
    { name: "AgeVerifiedBy", type: sql.VarChar, length: 70, isRequired: false },
    { name: "MaritalStatus", type: sql.VarChar, length: 70, isRequired: false },
    { name: "EmployeeType", type: sql.VarChar, length: 70, isRequired: false },
    { name: "NoOfCopies", type: sql.SmallInt, isRequired: false },
    { name: "NoOfCopiesDraft", type: sql.SmallInt, isRequired: false },
    { name: "CancelledBy", type: sql.VarChar, length: 8, isRequired: false },
    { name: "Location", type: sql.VarChar, length: 8, isRequired: false },
    { name: "AnalysisCode_ID", type: sql.VarChar, length: 5, isRequired: false },
    { name: "Currency", type: sql.VarChar, length: 10, isRequired: false },
    { name: "ImmediateHead_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "NTN", type: sql.VarChar, length: 40, isRequired: false },
    { name: "InsuranceCompanyName", type: sql.VarChar, length: 40, isRequired: false },
    { name: "InsuranceNo", type: sql.VarChar, length: 40, isRequired: false },
    { name: "InsuranceAmount", type: sql.SmallInt, isRequired: false },
    { name: "InsuranceDate", type: sql.DateTime, isRequired: false },
    { name: "BlockedForStitching", type: sql.SmallInt, isRequired: false },
    { name: "ContractCompletionDate", type: sql.DateTime, isRequired: false },
    { name: "User_ID", type: sql.VarChar, length: 8, isRequired: false },
    { name: "AllocationCostCenter_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "RevisionNumber", type: sql.Int, isRequired: false },
    { name: "NICExpiryDate", type: sql.DateTime, isRequired: false },
    { name: "JDVoucherNo", type: sql.VarChar, length: 70, isRequired: false },
    { name: "JDVtp", type: sql.VarChar, length: 20, isRequired: false },
    { name: "JDSubType", type: sql.VarChar, length: 8, isRequired: false },
    { name: "JDFinancialYear", type: sql.VarChar, length: 8, isRequired: false },
    { name: "JDMnth", type: sql.VarChar, length: 8, isRequired: false },
    { name: "JDvYear", type: sql.VarChar, length: 8, isRequired: false },
    { name: "JDLocation", type: sql.VarChar, length: 8, isRequired: false },
    { name: "JDForLocation", type: sql.VarChar, length: 8, isRequired: false },
    { name: "JDAorB", type: sql.VarChar, length: 1, isRequired: false },
    { name: "JDVNo", type: sql.Int, isRequired: false },
    { name: "FuelLitres", type: sql.Float, isRequired: false },
    { name: "ReportingTO_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "SSExp_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "PFExp_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "ResignCheckedOn", type: sql.DateTime, isRequired: false },
    { name: "ResignCheckedBy", type: sql.VarChar, length: 8, isRequired: false },
    { name: "EBankBranch", type: sql.VarChar, length: 70, isRequired: false },
    { name: "PFundEmployeeProfit", type: sql.Float, isRequired: false },
    { name: "PFundEmployerProfit", type: sql.Float, isRequired: false },
    { name: "OpeningBalances_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "SalaryContributionsDr_ID", type: sql.VarChar, length: 32, isRequired: false, },
    { name: "SalaryContributionsCr_ID", type: sql.VarChar, length: 32, isRequired: false, },
    { name: "InstallmentDeductionAccount_ID", type: sql.VarChar, length: 32, isRequired: false, },
    { name: "PFundPayableAccount_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "ProfitReceivable_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "ExpensesPayable_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "PFTrust_ID", type: sql.VarChar, length: 2, isRequired: false },
    { name: "allow10", type: sql.Float, isRequired: false },
    { name: "allow11", type: sql.Float, isRequired: false },
    { name: "allow12", type: sql.Float, isRequired: false },
    { name: "allow13", type: sql.Float, isRequired: false },
    { name: "allow14", type: sql.Float, isRequired: false },
    { name: "allow15", type: sql.Float, isRequired: false },
    { name: "allow16", type: sql.Float, isRequired: false },
    { name: "allow17", type: sql.Float, isRequired: false },
    { name: "allow18", type: sql.Float, isRequired: false },
    { name: "allow19", type: sql.Float, isRequired: false },
    { name: "PercentAllow10", type: sql.Float, isRequired: false },
    { name: "PercentAllow11", type: sql.Float, isRequired: false },
    { name: "PercentAllow12", type: sql.Float, isRequired: false },
    { name: "PercentAllow13", type: sql.Float, isRequired: false },
    { name: "PercentAllow14", type: sql.Float, isRequired: false },
    { name: "PercentAllow15", type: sql.Float, isRequired: false },
    { name: "PercentAllow16", type: sql.Float, isRequired: false },
    { name: "PercentAllow17", type: sql.Float, isRequired: false },
    { name: "PercentAllow18", type: sql.Float, isRequired: false },
    { name: "PercentAllow19", type: sql.Float, isRequired: false },
    { name: "BankCode", type: sql.VarChar, length: 70, isRequired: false },
    { name: "BankBranchCode", type: sql.VarChar, length: 70, isRequired: false },
    { name: "ComputerName", type: sql.VarChar, length: 120, isRequired: false },
    { name: "AllowLateMinutes", type: sql.TinyInt, isRequired: false },
    { name: "RestrictGraceMinutes", type: sql.TinyInt, isRequired: false },
    { name: "SalaryProcessingChannel", type: sql.VarChar, length: 120, isRequired: false, },
    { name: "PassportNumber", type: sql.VarChar, length: 120, isRequired: false },
    { name: "PassportExpiryDate", type: sql.DateTime, isRequired: false },
    { name: "LastName", type: sql.VarChar, length: 120, isRequired: false },
    { name: "DesignationAsPerVisa", type: sql.VarChar, length: 120, isRequired: false },
    { name: "EmiratesId", type: sql.VarChar, length: 120, isRequired: false },
    { name: "EmiratesIDExpiryDate", type: sql.DateTime, isRequired: false },
    { name: "VisaExpiryDate", type: sql.DateTime, isRequired: false },
    { name: "WPSPersonID", type: sql.VarChar, length: 120, isRequired: false },
    { name: "LaborCardNumber", type: sql.VarChar, length: 120, isRequired: false },
    { name: "DrivingLicenseNumber", type: sql.VarChar, length: 120, isRequired: false },
    { name: "DrivingLicenseExpiryDate", type: sql.DateTime, isRequired: false },
    { name: "HealthInsuranceCompany_ID", type: sql.VarChar, length: 32, isRequired: false, },
    { name: "HealthInsurancePolicyNumber", type: sql.VarChar, length: 120, isRequired: false, },
    { name: "HealthInsuranceExpiryDate", type: sql.DateTime, isRequired: false },
    { name: "Company_ID", type: sql.VarChar, length: 16, isRequired: false },
    { name: "WPSEstablishmentID", type: sql.VarChar, length: 120, isRequired: false },
    { name: "IBANNumber", type: sql.VarChar, length: 120, isRequired: false },
    { name: "nBankName", type: sql.VarChar, length: 120, isRequired: false },
    { name: "MobileNumber", type: sql.VarChar, length: 120, isRequired: false },
    { name: "LeaveLapseDays", type: sql.Float, isRequired: false },
    { name: "GrantExp_ID", type: sql.VarChar, length: 32, isRequired: false },
    { name: "ALAllowed", type: sql.TinyInt, isRequired: false },
    { name: "CLAllowed", type: sql.TinyInt, isRequired: false },
    { name: "SLAllowed", type: sql.TinyInt, isRequired: false },
    { name: "MLAllowed", type: sql.TinyInt, isRequired: false },
    { name: "PFAllowed", type: sql.TinyInt, isRequired: false },
    { name: "Table_ID", type: sql.VarChar, length: 12, isRequired: false },
    { name: "SubClass_ID", type: sql.VarChar, length: 3, isRequired: false },
    { name: "PolicyGap", type: sql.Float, isRequired: false },
    { name: "Allowed", type: sql.Float, isRequired: false },
    { name: "Availed", type: sql.Float, isRequired: false },
    { name: "Encashed", type: sql.Float, isRequired: false },
    { name: "webID", type: sql.VarChar, length: 250, isRequired: false },
    { name: "webPWD", type: sql.VarChar, length: 250, isRequired: false },
    { name: "Increment", type: sql.Float, isRequired: false },
]


module.exports = {
    coColumns,
    coItemColumns,
    cashItemColumns,
    cashColumns,
    bankColumns,
    bankItemColumns,
    goodReceiptNoteColumns,
    goodReceiptNoteItemColumns,
    residentRegistrationFormColumns,
    storeIssuanceNoteColumns,
    storeIssuanceNoteItemColumns,
    offerQuotationColumns,
    offerQuotationItemColumns,
    recoveryFollowUpsColumns,
    recoveryFollowUpsItemColumns,
    deliveryChallanColumns,
    deliveryChallanItemColumns,
    salesTaxInvoiceColumns,
    salesTaxInvoiceItemColumns,
    coa32Columns,
    coa3Columns,
    coa321Columns,
    coa31Columns,
    employeeColumns
};