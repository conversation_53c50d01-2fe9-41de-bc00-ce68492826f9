const { body, param, validationResult } = require('express-validator');

const validate = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

const userValidationRules = {
    getById: [
        param('id').isLength({ min: 1 }).withMessage('User ID is required'),
        validate
    ],
    login: [
        body('userName').notEmpty().withMessage('Username is required'),
        body('password').notEmpty().withMessage('Password is required'),
        validate
    ],
    updateProfile: [
        body('userName')
        .optional()
        .trim()
        .isLength({ min: 3 })
        .withMessage('Username must be at least 3 characters long'),

        body('email')
        .optional()
        .trim()
        .isEmail()
        .withMessage('Please provide a valid email address'),

        body('phone')
        .optional()
        .trim()
        .matches(/^[0-9+\-\s()]*$/)
        .withMessage('Please provide a valid phone number'),
    ],
    updatePassword: [
        body('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),

        body('newPassword')
        .notEmpty()
        .isLength({ min: 6 })
        .withMessage('New password must be at least 6 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),

        body('confirmPassword')
        .notEmpty()
        .custom((value, { req }) => {
            if (value !== req.body.newPassword) {
                throw new Error('Password confirmation does not match new password');
            }
            return true;
        }),
        validate
    ]
};

module.exports = userValidationRules;