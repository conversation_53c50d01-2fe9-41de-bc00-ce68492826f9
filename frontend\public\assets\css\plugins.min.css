@charset "UTF-8";

.ui-draggable-handle {
    -ms-touch-action: none;
    touch-action: none
}

.ui-helper-hidden {
    display: none
}

.ui-helper-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.ui-helper-reset {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    line-height: 1.3;
    text-decoration: none;
    font-size: 100%;
    list-style: none
}

.ui-helper-clearfix:after,
.ui-helper-clearfix:before {
    content: "";
    display: table;
    border-collapse: collapse
}

.ui-helper-clearfix:after {
    clear: both
}

.ui-helper-zfix {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0;
    filter: Alpha(Opacity=0)
}

.ui-front {
    z-index: 100
}

.ui-state-disabled {
    cursor: default !important;
    pointer-events: none
}

.ui-icon {
    display: inline-block;
    vertical-align: middle;
    margin-top: -.25em;
    position: relative;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat
}

.ui-widget-icon-block {
    left: 50%;
    margin-left: -8px;
    display: block
}

.ui-widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.ui-resizable {
    position: relative
}

.ui-resizable-handle {
    position: absolute;
    font-size: .1px;
    display: block;
    -ms-touch-action: none;
    touch-action: none
}

.ui-resizable-autohide .ui-resizable-handle,
.ui-resizable-disabled .ui-resizable-handle {
    display: none
}

.ui-resizable-n {
    cursor: n-resize;
    height: 7px;
    width: 100%;
    top: -5px;
    left: 0
}

.ui-resizable-s {
    cursor: s-resize;
    height: 7px;
    width: 100%;
    bottom: -5px;
    left: 0
}

.ui-resizable-e {
    cursor: e-resize;
    width: 7px;
    right: -5px;
    top: 0;
    height: 100%
}

.ui-resizable-w {
    cursor: w-resize;
    width: 7px;
    left: -5px;
    top: 0;
    height: 100%
}

.ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px
}

.ui-resizable-sw {
    cursor: sw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    bottom: -5px
}

.ui-resizable-nw {
    cursor: nw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    top: -5px
}

.ui-resizable-ne {
    cursor: ne-resize;
    width: 9px;
    height: 9px;
    right: -5px;
    top: -5px
}

.ui-selectable {
    -ms-touch-action: none;
    touch-action: none
}

.ui-selectable-helper {
    position: absolute;
    z-index: 100;
    border: 1px dotted #000
}

.ui-sortable-handle {
    -ms-touch-action: none;
    touch-action: none
}

.ui-slider {
    position: relative;
    text-align: left;
    background: #ddd
}

.ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 1em;
    height: 1em;
    cursor: default;
    -ms-touch-action: none;
    touch-action: none
}

.ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0
}

.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
    filter: inherit
}

.ui-slider-horizontal {
    height: .4em
}

.ui-slider-horizontal .ui-slider-handle {
    top: -.4em;
    margin-left: -.6em
}

.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%
}

.ui-slider-horizontal .ui-slider-range-min {
    left: 0
}

.ui-slider-horizontal .ui-slider-range-max {
    right: 0
}

.ui-slider-vertical {
    width: .8em;
    height: 100px
}

.ui-slider-vertical .ui-slider-handle {
    left: -.3em;
    margin-left: 0;
    margin-bottom: -.6em
}

.ui-slider-vertical .ui-slider-range {
    left: 0;
    width: 100%
}

.ui-slider-vertical .ui-slider-range-min {
    bottom: 0
}

.ui-slider-vertical .ui-slider-range-max {
    top: 0
}

.ui-slider-handle {
    background: #fff;
    background: -moz-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(100%, #f7f7f7));
    background: -webkit-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: -o-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: -ms-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: linear-gradient(to bottom, #fff 0, #f7f7f7 100%);
    border-radius: 50px;
    box-shadow: 0 1px 4px 0 #9191ab !important
}

.ui-slider-handle:focus {
    background: #fff;
    background: -moz-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(100%, #f7f7f7));
    background: -webkit-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: -o-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: -ms-linear-gradient(top, #fff 0, #f7f7f7 100%);
    background: linear-gradient(to bottom, #fff 0, #f7f7f7 100%);
    border-radius: 50px;
    box-shadow: 0 1px 4px 0 #9191ab !important
}

.scroll-wrapper {
    overflow: hidden !important;
    padding: 0 !important;
    position: relative
}

.scroll-wrapper>.scroll-content {
    border: none !important;
    box-sizing: content-box !important;
    height: auto;
    left: 0;
    margin: 0;
    max-height: none;
    max-width: none !important;
    overflow: scroll !important;
    padding: 0;
    position: relative !important;
    top: 0;
    width: auto !important
}

.scroll-wrapper>.scroll-content::-webkit-scrollbar {
    height: 0;
    width: 0
}

.scroll-element {
    display: none;
    box-sizing: content-box
}

.scroll-element div {
    box-sizing: content-box
}

.scroll-element.scroll-x.scroll-scrollx_visible,
.scroll-element.scroll-y.scroll-scrolly_visible {
    display: block
}

.scroll-element .scroll-arrow,
.scroll-element .scroll-bar {
    cursor: default
}

.scroll-textarea {
    border: 1px solid #ccc;
    border-top-color: #999
}

.scroll-textarea>.scroll-content {
    overflow: hidden !important
}

.scroll-textarea>.scroll-content>textarea {
    border: none !important;
    box-sizing: border-box;
    height: 100% !important;
    margin: 0;
    max-height: none !important;
    max-width: none !important;
    overflow: scroll !important;
    outline: 0;
    padding: 2px;
    position: relative !important;
    top: 0;
    width: 100% !important
}

.scrollbar-inner>.scroll-element .scroll-element_outer,
.scrollbar-outer>.scroll-element .scroll-element_outer {
    overflow: hidden
}

.scroll-textarea>.scroll-content>textarea::-webkit-scrollbar {
    height: 0;
    width: 0
}

.scrollbar-inner>.scroll-element {
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10
}

.scrollbar-inner>.scroll-element div {
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10;
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.scrollbar-inner>.scroll-element.scroll-x {
    bottom: 2px;
    height: 7px;
    left: 0;
    width: 100%
}

.scrollbar-inner>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size,
.scrollbar-inner>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
    left: -12px
}

.scrollbar-inner>.scroll-element.scroll-y {
    height: 100%;
    right: 2px;
    top: 0;
    width: 7px
}

.scrollbar-inner>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size,
.scrollbar-inner>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
    top: -12px
}

.scrollbar-inner>.scroll-element .scroll-bar,
.scrollbar-inner>.scroll-element .scroll-element_outer,
.scrollbar-inner>.scroll-element .scroll-element_track {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px
}

.scrollbar-inner>.scroll-element .scroll-bar {
    opacity: .4
}

.scrollbar-inner>.scroll-element .scroll-element_track {
    opacity: .4;
    background-color: #e0e0e0
}

.scrollbar-inner>.scroll-element .scroll-bar {
    background-color: #c2c2c2
}

.scrollbar-inner>.scroll-element.scroll-draggable .scroll-bar,
.scrollbar-inner>.scroll-element:hover .scroll-bar {
    background-color: #919191
}

.scrollbar-outer>.scroll-element {
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10;
    background-color: #fff
}

.scrollbar-outer>.scroll-element div {
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10;
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.scrollbar-outer>.scroll-element.scroll-x {
    bottom: 0;
    height: 12px;
    left: 0;
    width: 100%
}

.scrollbar-outer>.scroll-element.scroll-y {
    height: 100%;
    right: 0;
    top: 0;
    width: 12px
}

.scrollbar-outer>.scroll-element.scroll-x .scroll-element_outer {
    height: 7px;
    top: 2px
}

.scrollbar-outer>.scroll-element.scroll-y .scroll-element_outer {
    left: 2px;
    width: 7px
}

.scrollbar-outer>.scroll-element .scroll-element_track {
    background-color: #eee
}

.scrollbar-outer>.scroll-element .scroll-bar,
.scrollbar-outer>.scroll-element .scroll-element_outer,
.scrollbar-outer>.scroll-element .scroll-element_track {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px
}

.scrollbar-outer>.scroll-element .scroll-bar {
    background-color: #d9d9d9
}

.scrollbar-outer>.scroll-element .scroll-bar:hover {
    background-color: #c2c2c2
}

.scrollbar-outer>.scroll-element.scroll-draggable .scroll-bar {
    background-color: #919191
}

.scrollbar-outer>.scroll-content.scroll-scrolly_visible {
    left: -12px;
    margin-left: 12px
}

.scrollbar-outer>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size,
.scrollbar-outer>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
    left: -14px
}

.scrollbar-outer>.scroll-content.scroll-scrollx_visible {
    top: -12px;
    margin-top: 12px
}

.scrollbar-outer>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size,
.scrollbar-outer>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
    top: -14px
}

.scrollbar-outer>.scroll-element.scroll-x .scroll-bar {
    min-width: 10px
}

.scrollbar-outer>.scroll-element.scroll-y .scroll-bar {
    min-height: 10px
}

.scrollbar-macosx>.scroll-element {
    background: 0 0;
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10
}

.scrollbar-macosx>.scroll-element div {
    background: 0 0;
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10;
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.scrollbar-macosx>.scroll-element .scroll-element_track {
    display: none
}

.scrollbar-macosx>.scroll-element .scroll-bar {
    background-color: #6c6e71;
    display: block;
    opacity: 0;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    border-radius: 7px;
    -webkit-transition: opacity .2s linear;
    -moz-transition: opacity .2s linear;
    -o-transition: opacity .2s linear;
    -ms-transition: opacity .2s linear;
    transition: opacity .2s linear
}

.scrollbar-macosx:hover>.scroll-element .scroll-bar {
    opacity: .7
}

.scrollbar-macosx>.scroll-element.scroll-draggable .scroll-bar {
    opacity: .7
}

.scrollbar-macosx>.scroll-element.scroll-x {
    bottom: 0;
    height: 0;
    left: 0;
    min-width: 100%;
    overflow: visible;
    width: 100%
}

.scrollbar-macosx>.scroll-element.scroll-y {
    height: 100%;
    min-height: 100%;
    right: 0;
    top: 0;
    width: 0
}

.scrollbar-macosx>.scroll-element.scroll-x .scroll-bar {
    height: 7px;
    min-width: 10px;
    top: -9px
}

.scrollbar-macosx>.scroll-element.scroll-y .scroll-bar {
    left: -9px;
    min-height: 10px;
    width: 7px
}

.scrollbar-macosx>.scroll-element.scroll-x .scroll-element_outer {
    left: 2px
}

.scrollbar-macosx>.scroll-element.scroll-x .scroll-element_size {
    left: -4px
}

.scrollbar-macosx>.scroll-element.scroll-y .scroll-element_outer {
    top: 2px
}

.scrollbar-macosx>.scroll-element.scroll-y .scroll-element_size {
    top: -4px
}

.scrollbar-macosx>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {
    left: -11px
}

.scrollbar-macosx>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {
    top: -11px
}

.scrollbar-light>.scroll-element {
    border: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    z-index: 10;
    background-color: #fff
}

.scrollbar-light>.scroll-element div {
    border: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    z-index: 10;
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.scrollbar-light>.scroll-element .scroll-element_outer {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px
}

.scrollbar-light>.scroll-element .scroll-element_size {
    background: -moz-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: -webkit-gradient(linear, left top, right top, color-stop(0, #dbdbdb), color-stop(100%, #e8e8e8));
    background: -webkit-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: -o-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: -ms-linear-gradient(left, #dbdbdb 0, #e8e8e8 100%);
    background: linear-gradient(to right, #dbdbdb 0, #e8e8e8 100%);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px
}

.scrollbar-light>.scroll-element.scroll-x {
    bottom: 0;
    height: 17px;
    left: 0;
    min-width: 100%;
    width: 100%
}

.scrollbar-light>.scroll-element.scroll-y {
    height: 100%;
    min-height: 100%;
    right: 0;
    top: 0;
    width: 17px
}

.scrollbar-light>.scroll-element .scroll-bar {
    background: -moz-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: -webkit-gradient(linear, left top, right top, color-stop(0, #fefefe), color-stop(100%, #f5f5f5));
    background: -webkit-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: -o-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: -ms-linear-gradient(left, #fefefe 0, #f5f5f5 100%);
    background: linear-gradient(to right, #fefefe 0, #f5f5f5 100%);
    border: 1px solid #dbdbdb;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px
}

.scrollbar-light>.scroll-content.scroll-scrolly_visible {
    left: -17px;
    margin-left: 17px
}

.scrollbar-light>.scroll-content.scroll-scrollx_visible {
    top: -17px;
    margin-top: 17px
}

.scrollbar-light>.scroll-element.scroll-x .scroll-bar {
    height: 10px;
    min-width: 10px;
    top: 0
}

.scrollbar-light>.scroll-element.scroll-y .scroll-bar {
    left: 0;
    min-height: 10px;
    width: 10px
}

.scrollbar-light>.scroll-element.scroll-x .scroll-element_outer {
    height: 12px;
    left: 2px;
    top: 2px
}

.scrollbar-light>.scroll-element.scroll-x .scroll-element_size {
    left: -4px
}

.scrollbar-light>.scroll-element.scroll-y .scroll-element_outer {
    left: 2px;
    top: 2px;
    width: 12px
}

.scrollbar-light>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size,
.scrollbar-light>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
    left: -19px
}

.scrollbar-light>.scroll-element.scroll-y .scroll-element_size {
    top: -4px
}

.scrollbar-light>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size,
.scrollbar-light>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
    top: -19px
}

.scrollbar-rail>.scroll-element {
    border: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    z-index: 10;
    background-color: #fff
}

.scrollbar-rail>.scroll-element div {
    border: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    z-index: 10;
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.scrollbar-rail>.scroll-element .scroll-element_size {
    background-color: #999;
    background-color: rgba(0, 0, 0, .3)
}

.scrollbar-rail>.scroll-element .scroll-element_outer:hover .scroll-element_size {
    background-color: #666;
    background-color: rgba(0, 0, 0, .5)
}

.scrollbar-rail>.scroll-element.scroll-x {
    bottom: 0;
    height: 12px;
    left: 0;
    min-width: 100%;
    padding: 3px 0 2px;
    width: 100%
}

.scrollbar-rail>.scroll-element.scroll-y {
    height: 100%;
    min-height: 100%;
    padding: 0 2px 0 3px;
    right: 0;
    top: 0;
    width: 12px
}

.scrollbar-rail>.scroll-element .scroll-bar {
    background-color: #d0b9a0;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .5)
}

.scrollbar-rail>.scroll-element .scroll-element_outer:hover .scroll-bar {
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .6)
}

.scrollbar-rail>.scroll-content.scroll-scrolly_visible {
    left: -17px;
    margin-left: 17px
}

.scrollbar-rail>.scroll-content.scroll-scrollx_visible {
    margin-top: 17px;
    top: -17px
}

.scrollbar-rail>.scroll-element.scroll-x .scroll-bar {
    height: 10px;
    min-width: 10px;
    top: 1px
}

.scrollbar-rail>.scroll-element.scroll-y .scroll-bar {
    left: 1px;
    min-height: 10px;
    width: 10px
}

.scrollbar-rail>.scroll-element.scroll-x .scroll-element_outer {
    height: 15px;
    left: 5px
}

.scrollbar-rail>.scroll-element.scroll-x .scroll-element_size {
    height: 2px;
    left: -10px;
    top: 5px
}

.scrollbar-rail>.scroll-element.scroll-y .scroll-element_outer {
    top: 5px;
    width: 15px
}

.scrollbar-rail>.scroll-element.scroll-y .scroll-element_size {
    left: 5px;
    top: -10px;
    width: 2px
}

.scrollbar-rail>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size,
.scrollbar-rail>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
    left: -25px
}

.scrollbar-rail>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size,
.scrollbar-rail>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
    top: -25px
}

.scrollbar-dynamic>.scroll-element {
    background: 0 0;
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10
}

.scrollbar-dynamic>.scroll-element div {
    background: 0 0;
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10;
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.scrollbar-dynamic>.scroll-element.scroll-x {
    bottom: 2px;
    height: 7px;
    left: 0;
    min-width: 100%;
    width: 100%
}

.scrollbar-dynamic>.scroll-element.scroll-y {
    height: 100%;
    min-height: 100%;
    right: 2px;
    top: 0;
    width: 7px
}

.scrollbar-dynamic>.scroll-element .scroll-element_outer {
    opacity: .3;
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    border-radius: 12px
}

.scrollbar-dynamic>.scroll-element .scroll-element_size {
    background-color: #ccc;
    opacity: 0;
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    border-radius: 12px;
    -webkit-transition: opacity .2s;
    -moz-transition: opacity .2s;
    -o-transition: opacity .2s;
    -ms-transition: opacity .2s;
    transition: opacity .2s
}

.scrollbar-dynamic>.scroll-element .scroll-bar {
    background-color: #6c6e71;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    border-radius: 7px
}

.scrollbar-dynamic>.scroll-element.scroll-x .scroll-bar {
    bottom: 0;
    height: 7px;
    min-width: 24px;
    top: auto
}

.scrollbar-dynamic>.scroll-element.scroll-y .scroll-bar {
    left: auto;
    min-height: 24px;
    right: 0;
    width: 7px
}

.scrollbar-dynamic>.scroll-element.scroll-x .scroll-element_outer {
    bottom: 0;
    top: auto;
    left: 2px;
    -webkit-transition: height .2s;
    -moz-transition: height .2s;
    -o-transition: height .2s;
    -ms-transition: height .2s;
    transition: height .2s
}

.scrollbar-dynamic>.scroll-element.scroll-y .scroll-element_outer {
    left: auto;
    right: 0;
    top: 2px;
    -webkit-transition: width .2s;
    -moz-transition: width .2s;
    -o-transition: width .2s;
    -ms-transition: width .2s;
    transition: width .2s
}

.scrollbar-dynamic>.scroll-element.scroll-x .scroll-element_size {
    left: -4px
}

.scrollbar-dynamic>.scroll-element.scroll-y .scroll-element_size {
    top: -4px
}

.scrollbar-dynamic>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {
    left: -11px
}

.scrollbar-dynamic>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {
    top: -11px
}

.scrollbar-dynamic>.scroll-element.scroll-draggable .scroll-element_outer,
.scrollbar-dynamic>.scroll-element:hover .scroll-element_outer {
    overflow: hidden;
    opacity: .7
}

.scrollbar-dynamic>.scroll-element.scroll-draggable .scroll-element_outer .scroll-element_size,
.scrollbar-dynamic>.scroll-element:hover .scroll-element_outer .scroll-element_size {
    opacity: 1
}

.scrollbar-dynamic>.scroll-element.scroll-draggable .scroll-element_outer .scroll-bar,
.scrollbar-dynamic>.scroll-element:hover .scroll-element_outer .scroll-bar {
    height: 100%;
    width: 100%;
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    border-radius: 12px
}

.scrollbar-dynamic>.scroll-element.scroll-x.scroll-draggable .scroll-element_outer,
.scrollbar-dynamic>.scroll-element.scroll-x:hover .scroll-element_outer {
    height: 20px;
    min-height: 7px
}

.scrollbar-dynamic>.scroll-element.scroll-y.scroll-draggable .scroll-element_outer,
.scrollbar-dynamic>.scroll-element.scroll-y:hover .scroll-element_outer {
    min-width: 7px;
    width: 20px
}

.scrollbar-chrome>.scroll-element {
    border: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    z-index: 10;
    background-color: #fff
}

.scrollbar-chrome>.scroll-element div {
    border: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    z-index: 10;
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.scrollbar-chrome>.scroll-element .scroll-element_track {
    background: #f1f1f1;
    border: 1px solid #dbdbdb
}

.scrollbar-chrome>.scroll-element.scroll-x {
    bottom: 0;
    height: 16px;
    left: 0;
    min-width: 100%;
    width: 100%
}

.scrollbar-chrome>.scroll-element.scroll-y {
    height: 100%;
    min-height: 100%;
    right: 0;
    top: 0;
    width: 16px
}

.scrollbar-chrome>.scroll-element .scroll-bar {
    background-color: #d9d9d9;
    border: 1px solid #bdbdbd;
    cursor: default;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px
}

.scrollbar-chrome>.scroll-element .scroll-bar:hover {
    background-color: #c2c2c2;
    border-color: #a9a9a9
}

.scrollbar-chrome>.scroll-element.scroll-draggable .scroll-bar {
    background-color: #919191;
    border-color: #7e7e7e
}

.scrollbar-chrome>.scroll-content.scroll-scrolly_visible {
    left: -16px;
    margin-left: 16px
}

.scrollbar-chrome>.scroll-content.scroll-scrollx_visible {
    top: -16px;
    margin-top: 16px
}

.scrollbar-chrome>.scroll-element.scroll-x .scroll-bar {
    height: 5px;
    min-width: 10px;
    top: 3px
}

.scrollbar-chrome>.scroll-element.scroll-y .scroll-bar {
    left: 3px;
    min-height: 10px;
    width: 5px
}

.scrollbar-chrome>.scroll-element.scroll-x .scroll-element_outer {
    border-left: 1px solid #dbdbdb
}

.scrollbar-chrome>.scroll-element.scroll-x .scroll-element_track {
    height: 14px;
    left: -3px
}

.scrollbar-chrome>.scroll-element.scroll-x .scroll-element_size {
    height: 14px;
    left: -4px
}

.scrollbar-chrome>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size,
.scrollbar-chrome>.scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
    left: -19px
}

.scrollbar-chrome>.scroll-element.scroll-y .scroll-element_outer {
    border-top: 1px solid #dbdbdb
}

.scrollbar-chrome>.scroll-element.scroll-y .scroll-element_track {
    top: -3px;
    width: 14px
}

.scrollbar-chrome>.scroll-element.scroll-y .scroll-element_size {
    top: -4px;
    width: 14px
}

.scrollbar-chrome>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size,
.scrollbar-chrome>.scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
    top: -19px
}

.checkbox label .toggle,
.checkbox-inline .toggle {
    margin-left: -20px;
    margin-right: 5px
}

.toggle {
    position: relative;
    overflow: hidden;
    padding: .6rem .5rem
}

.toggle-group,
.toggle-off,
.toggle-on {
    position: absolute;
    top: 0;
    bottom: 0
}

.toggle input[type=checkbox] {
    display: none
}

.toggle-group {
    width: 200%;
    left: 0;
    transition: left .35s;
    -webkit-transition: left .35s;
    -moz-user-select: none;
    -webkit-user-select: none
}

.toggle.off .toggle-group {
    left: -100%
}

.toggle-on {
    left: 0;
    right: 50%;
    margin: 0;
    border: 0;
    border-radius: 0;
    padding-left: 12px !important;
    padding-top: 6px !important;
    padding-bottom: 6px !important;
    font-size: 11px !important
}

.toggle-off {
    left: 50%;
    right: 0;
    margin: 0;
    border: 0;
    border-radius: 0;
    padding-top: 6px !important;
    padding-bottom: 6px !important;
    font-size: 11px !important;
    color: #fff !important
}

.toggle-handle {
    position: relative;
    margin: 0 auto;
    padding-top: 0;
    padding-bottom: 0;
    height: 100%;
    width: 0;
    border-width: 0 1px
}

.toggle.btn {
    min-width: 54px !important;
    height: 30px !important
}

.toggle-on.btn {
    padding-right: 24px
}

.toggle-off.btn {
    padding-left: 24px
}

.toggle.btn-lg {
    min-width: 79px;
    min-height: 45px
}

.toggle-on.btn-lg {
    padding-right: 31px
}

.toggle-off.btn-lg {
    padding-left: 31px
}

.toggle-handle.btn-lg {
    width: 40px
}

.toggle.btn-sm {
    min-width: 50px;
    min-height: 30px
}

.toggle-on.btn-sm {
    padding-right: 20px
}

.toggle-off.btn-sm {
    padding-left: 20px
}

.toggle.btn-xs {
    min-width: 35px;
    min-height: 22px
}

.toggle-on.btn-xs {
    padding-right: 12px
}

.toggle-off.btn-xs {
    padding-left: 12px
}

/*!
* animate.css -http://daneden.me/animate
* Version - 3.6.0
* Licensed under the MIT license - http://opensource.org/licenses/MIT
*
* Copyright (c) 2018 Daniel Eden
*/
.animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.animated.infinite {
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite
}

@-webkit-keyframes bounce {

    20%,
    53%,
    80%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    40%,
    43% {
        -webkit-animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        -webkit-transform: translate3d(0, -30px, 0);
        transform: translate3d(0, -30px, 0)
    }

    70% {
        -webkit-animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        -webkit-transform: translate3d(0, -15px, 0);
        transform: translate3d(0, -15px, 0)
    }

    90% {
        -webkit-transform: translate3d(0, -4px, 0);
        transform: translate3d(0, -4px, 0)
    }
}

@keyframes bounce {

    20%,
    53%,
    80%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    40%,
    43% {
        -webkit-animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        -webkit-transform: translate3d(0, -30px, 0);
        transform: translate3d(0, -30px, 0)
    }

    70% {
        -webkit-animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        animation-timing-function: cubic-bezier(.755, .05, .855, .06);
        -webkit-transform: translate3d(0, -15px, 0);
        transform: translate3d(0, -15px, 0)
    }

    90% {
        -webkit-transform: translate3d(0, -4px, 0);
        transform: translate3d(0, -4px, 0)
    }
}

.bounce {
    -webkit-animation-name: bounce;
    animation-name: bounce;
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

@-webkit-keyframes flash {

    50%,
    from,
    to {
        opacity: 1
    }

    25%,
    75% {
        opacity: 0
    }
}

@keyframes flash {

    50%,
    from,
    to {
        opacity: 1
    }

    25%,
    75% {
        opacity: 0
    }
}

.flash {
    -webkit-animation-name: flash;
    animation-name: flash
}

@-webkit-keyframes pulse {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    50% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05);
        transform: scale3d(1.05, 1.05, 1.05)
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

@keyframes pulse {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    50% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05);
        transform: scale3d(1.05, 1.05, 1.05)
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

.pulse {
    -webkit-animation-name: pulse;
    animation-name: pulse
}

@-webkit-keyframes rubberBand {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    30% {
        -webkit-transform: scale3d(1.25, .75, 1);
        transform: scale3d(1.25, .75, 1)
    }

    40% {
        -webkit-transform: scale3d(.75, 1.25, 1);
        transform: scale3d(.75, 1.25, 1)
    }

    50% {
        -webkit-transform: scale3d(1.15, .85, 1);
        transform: scale3d(1.15, .85, 1)
    }

    65% {
        -webkit-transform: scale3d(.95, 1.05, 1);
        transform: scale3d(.95, 1.05, 1)
    }

    75% {
        -webkit-transform: scale3d(1.05, .95, 1);
        transform: scale3d(1.05, .95, 1)
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

@keyframes rubberBand {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    30% {
        -webkit-transform: scale3d(1.25, .75, 1);
        transform: scale3d(1.25, .75, 1)
    }

    40% {
        -webkit-transform: scale3d(.75, 1.25, 1);
        transform: scale3d(.75, 1.25, 1)
    }

    50% {
        -webkit-transform: scale3d(1.15, .85, 1);
        transform: scale3d(1.15, .85, 1)
    }

    65% {
        -webkit-transform: scale3d(.95, 1.05, 1);
        transform: scale3d(.95, 1.05, 1)
    }

    75% {
        -webkit-transform: scale3d(1.05, .95, 1);
        transform: scale3d(1.05, .95, 1)
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

.rubberBand {
    -webkit-animation-name: rubberBand;
    animation-name: rubberBand
}

@-webkit-keyframes shake {

    from,
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0)
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0)
    }
}

@keyframes shake {

    from,
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0)
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0)
    }
}

.shake {
    -webkit-animation-name: shake;
    animation-name: shake
}

@-webkit-keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    6.5% {
        -webkit-transform: translateX(-6px) rotateY(-9deg);
        transform: translateX(-6px) rotateY(-9deg)
    }

    18.5% {
        -webkit-transform: translateX(5px) rotateY(7deg);
        transform: translateX(5px) rotateY(7deg)
    }

    31.5% {
        -webkit-transform: translateX(-3px) rotateY(-5deg);
        transform: translateX(-3px) rotateY(-5deg)
    }

    43.5% {
        -webkit-transform: translateX(2px) rotateY(3deg);
        transform: translateX(2px) rotateY(3deg)
    }

    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    6.5% {
        -webkit-transform: translateX(-6px) rotateY(-9deg);
        transform: translateX(-6px) rotateY(-9deg)
    }

    18.5% {
        -webkit-transform: translateX(5px) rotateY(7deg);
        transform: translateX(5px) rotateY(7deg)
    }

    31.5% {
        -webkit-transform: translateX(-3px) rotateY(-5deg);
        transform: translateX(-3px) rotateY(-5deg)
    }

    43.5% {
        -webkit-transform: translateX(2px) rotateY(3deg);
        transform: translateX(2px) rotateY(3deg)
    }

    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

.headShake {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-name: headShake;
    animation-name: headShake
}

@-webkit-keyframes swing {
    20% {
        -webkit-transform: rotate3d(0, 0, 1, 15deg);
        transform: rotate3d(0, 0, 1, 15deg)
    }

    40% {
        -webkit-transform: rotate3d(0, 0, 1, -10deg);
        transform: rotate3d(0, 0, 1, -10deg)
    }

    60% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
        transform: rotate3d(0, 0, 1, 5deg)
    }

    80% {
        -webkit-transform: rotate3d(0, 0, 1, -5deg);
        transform: rotate3d(0, 0, 1, -5deg)
    }

    to {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg)
    }
}

@keyframes swing {
    20% {
        -webkit-transform: rotate3d(0, 0, 1, 15deg);
        transform: rotate3d(0, 0, 1, 15deg)
    }

    40% {
        -webkit-transform: rotate3d(0, 0, 1, -10deg);
        transform: rotate3d(0, 0, 1, -10deg)
    }

    60% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
        transform: rotate3d(0, 0, 1, 5deg)
    }

    80% {
        -webkit-transform: rotate3d(0, 0, 1, -5deg);
        transform: rotate3d(0, 0, 1, -5deg)
    }

    to {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg)
    }
}

.swing {
    -webkit-transform-origin: top center;
    transform-origin: top center;
    -webkit-animation-name: swing;
    animation-name: swing
}

@-webkit-keyframes tada {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    10%,
    20% {
        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg)
    }

    30%,
    50%,
    70%,
    90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg)
    }

    40%,
    60%,
    80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg)
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

@keyframes tada {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    10%,
    20% {
        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg)
    }

    30%,
    50%,
    70%,
    90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg)
    }

    40%,
    60%,
    80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg)
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

.tada {
    -webkit-animation-name: tada;
    animation-name: tada
}

@-webkit-keyframes wobble {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    15% {
        -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
        transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg)
    }

    30% {
        -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
        transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg)
    }

    45% {
        -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
        transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg)
    }

    60% {
        -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
        transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg)
    }

    75% {
        -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
        transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes wobble {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    15% {
        -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
        transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg)
    }

    30% {
        -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
        transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg)
    }

    45% {
        -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
        transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg)
    }

    60% {
        -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
        transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg)
    }

    75% {
        -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
        transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.wobble {
    -webkit-animation-name: wobble;
    animation-name: wobble
}

@-webkit-keyframes jello {

    11.1%,
    from,
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    22.2% {
        -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
        transform: skewX(-12.5deg) skewY(-12.5deg)
    }

    33.3% {
        -webkit-transform: skewX(6.25deg) skewY(6.25deg);
        transform: skewX(6.25deg) skewY(6.25deg)
    }

    44.4% {
        -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
        transform: skewX(-3.125deg) skewY(-3.125deg)
    }

    55.5% {
        -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
        transform: skewX(1.5625deg) skewY(1.5625deg)
    }

    66.6% {
        -webkit-transform: skewX(-.78125deg) skewY(-.78125deg);
        transform: skewX(-.78125deg) skewY(-.78125deg)
    }

    77.7% {
        -webkit-transform: skewX(.39063deg) skewY(.39063deg);
        transform: skewX(.39063deg) skewY(.39063deg)
    }

    88.8% {
        -webkit-transform: skewX(-.19531deg) skewY(-.19531deg);
        transform: skewX(-.19531deg) skewY(-.19531deg)
    }
}

@keyframes jello {

    11.1%,
    from,
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    22.2% {
        -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
        transform: skewX(-12.5deg) skewY(-12.5deg)
    }

    33.3% {
        -webkit-transform: skewX(6.25deg) skewY(6.25deg);
        transform: skewX(6.25deg) skewY(6.25deg)
    }

    44.4% {
        -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
        transform: skewX(-3.125deg) skewY(-3.125deg)
    }

    55.5% {
        -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
        transform: skewX(1.5625deg) skewY(1.5625deg)
    }

    66.6% {
        -webkit-transform: skewX(-.78125deg) skewY(-.78125deg);
        transform: skewX(-.78125deg) skewY(-.78125deg)
    }

    77.7% {
        -webkit-transform: skewX(.39063deg) skewY(.39063deg);
        transform: skewX(.39063deg) skewY(.39063deg)
    }

    88.8% {
        -webkit-transform: skewX(-.19531deg) skewY(-.19531deg);
        transform: skewX(-.19531deg) skewY(-.19531deg)
    }
}

.jello {
    -webkit-animation-name: jello;
    animation-name: jello;
    -webkit-transform-origin: center;
    transform-origin: center
}

@-webkit-keyframes bounceIn {

    20%,
    40%,
    60%,
    80%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    20% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1)
    }

    40% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03, 1.03, 1.03);
        transform: scale3d(1.03, 1.03, 1.03)
    }

    80% {
        -webkit-transform: scale3d(.97, .97, .97);
        transform: scale3d(.97, .97, .97)
    }

    to {
        opacity: 1;
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

@keyframes bounceIn {

    20%,
    40%,
    60%,
    80%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    20% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1)
    }

    40% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03, 1.03, 1.03);
        transform: scale3d(1.03, 1.03, 1.03)
    }

    80% {
        -webkit-transform: scale3d(.97, .97, .97);
        transform: scale3d(.97, .97, .97)
    }

    to {
        opacity: 1;
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }
}

.bounceIn {
    -webkit-animation-duration: .75s;
    animation-duration: .75s;
    -webkit-animation-name: bounceIn;
    animation-name: bounceIn
}

@-webkit-keyframes bounceInDown {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -3000px, 0);
        transform: translate3d(0, -3000px, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, 25px, 0);
        transform: translate3d(0, 25px, 0)
    }

    75% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0)
    }

    90% {
        -webkit-transform: translate3d(0, 5px, 0);
        transform: translate3d(0, 5px, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes bounceInDown {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -3000px, 0);
        transform: translate3d(0, -3000px, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, 25px, 0);
        transform: translate3d(0, 25px, 0)
    }

    75% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0)
    }

    90% {
        -webkit-transform: translate3d(0, 5px, 0);
        transform: translate3d(0, 5px, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.bounceInDown {
    -webkit-animation-name: bounceInDown;
    animation-name: bounceInDown
}

@-webkit-keyframes bounceInLeft {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(-3000px, 0, 0);
        transform: translate3d(-3000px, 0, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(25px, 0, 0);
        transform: translate3d(25px, 0, 0)
    }

    75% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0)
    }

    90% {
        -webkit-transform: translate3d(5px, 0, 0);
        transform: translate3d(5px, 0, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes bounceInLeft {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(-3000px, 0, 0);
        transform: translate3d(-3000px, 0, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(25px, 0, 0);
        transform: translate3d(25px, 0, 0)
    }

    75% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0)
    }

    90% {
        -webkit-transform: translate3d(5px, 0, 0);
        transform: translate3d(5px, 0, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.bounceInLeft {
    -webkit-animation-name: bounceInLeft;
    animation-name: bounceInLeft
}

@-webkit-keyframes bounceInRight {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(3000px, 0, 0);
        transform: translate3d(3000px, 0, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(-25px, 0, 0);
        transform: translate3d(-25px, 0, 0)
    }

    75% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0)
    }

    90% {
        -webkit-transform: translate3d(-5px, 0, 0);
        transform: translate3d(-5px, 0, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes bounceInRight {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(3000px, 0, 0);
        transform: translate3d(3000px, 0, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(-25px, 0, 0);
        transform: translate3d(-25px, 0, 0)
    }

    75% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0)
    }

    90% {
        -webkit-transform: translate3d(-5px, 0, 0);
        transform: translate3d(-5px, 0, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.bounceInRight {
    -webkit-animation-name: bounceInRight;
    animation-name: bounceInRight
}

@-webkit-keyframes bounceInUp {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 3000px, 0);
        transform: translate3d(0, 3000px, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    75% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0)
    }

    90% {
        -webkit-transform: translate3d(0, -5px, 0);
        transform: translate3d(0, -5px, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes bounceInUp {

    60%,
    75%,
    90%,
    from,
    to {
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 3000px, 0);
        transform: translate3d(0, 3000px, 0)
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    75% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0)
    }

    90% {
        -webkit-transform: translate3d(0, -5px, 0);
        transform: translate3d(0, -5px, 0)
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.bounceInUp {
    -webkit-animation-name: bounceInUp;
    animation-name: bounceInUp
}

@-webkit-keyframes bounceOut {
    20% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9)
    }

    50%,
    55% {
        opacity: 1;
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
}

@keyframes bounceOut {
    20% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9)
    }

    50%,
    55% {
        opacity: 1;
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
}

.bounceOut {
    -webkit-animation-duration: .75s;
    animation-duration: .75s;
    -webkit-animation-name: bounceOut;
    animation-name: bounceOut
}

@-webkit-keyframes bounceOutDown {
    20% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0)
    }

    40%,
    45% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0)
    }
}

@keyframes bounceOutDown {
    20% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0)
    }

    40%,
    45% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0)
    }
}

.bounceOutDown {
    -webkit-animation-name: bounceOutDown;
    animation-name: bounceOutDown
}

@-webkit-keyframes bounceOutLeft {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0)
    }
}

@keyframes bounceOutLeft {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0)
    }
}

.bounceOutLeft {
    -webkit-animation-name: bounceOutLeft;
    animation-name: bounceOutLeft
}

@-webkit-keyframes bounceOutRight {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0)
    }
}

@keyframes bounceOutRight {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0)
    }
}

.bounceOutRight {
    -webkit-animation-name: bounceOutRight;
    animation-name: bounceOutRight
}

@-webkit-keyframes bounceOutUp {
    20% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0)
    }

    40%,
    45% {
        opacity: 1;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0)
    }
}

@keyframes bounceOutUp {
    20% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0)
    }

    40%,
    45% {
        opacity: 1;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0)
    }
}

.bounceOutUp {
    -webkit-animation-name: bounceOutUp;
    animation-name: bounceOutUp
}

@-webkit-keyframes fadeIn {
    from {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes fadeIn {
    from {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.fadeIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn
}

@-webkit-keyframes fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInDown {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown
}

@-webkit-keyframes fadeInDownBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInDownBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInDownBig {
    -webkit-animation-name: fadeInDownBig;
    animation-name: fadeInDownBig
}

@-webkit-keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft
}

@-webkit-keyframes fadeInLeftBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInLeftBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInLeftBig {
    -webkit-animation-name: fadeInLeftBig;
    animation-name: fadeInLeftBig
}

@-webkit-keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInRight {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight
}

@-webkit-keyframes fadeInRightBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInRightBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInRightBig {
    -webkit-animation-name: fadeInRightBig;
    animation-name: fadeInRightBig
}

@-webkit-keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInUp {
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp
}

@-webkit-keyframes fadeInUpBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fadeInUpBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.fadeInUpBig {
    -webkit-animation-name: fadeInUpBig;
    animation-name: fadeInUpBig
}

@-webkit-keyframes fadeOut {
    from {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes fadeOut {
    from {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

@-webkit-keyframes fadeOutDown {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }
}

.fadeOutDown {
    -webkit-animation-name: fadeOutDown;
    animation-name: fadeOutDown
}

@-webkit-keyframes fadeOutDownBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0)
    }
}

@keyframes fadeOutDownBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0)
    }
}

.fadeOutDownBig {
    -webkit-animation-name: fadeOutDownBig;
    animation-name: fadeOutDownBig
}

@-webkit-keyframes fadeOutLeft {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

@keyframes fadeOutLeft {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

.fadeOutLeft {
    -webkit-animation-name: fadeOutLeft;
    animation-name: fadeOutLeft
}

@-webkit-keyframes fadeOutLeftBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0)
    }
}

@keyframes fadeOutLeftBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0)
    }
}

.fadeOutLeftBig {
    -webkit-animation-name: fadeOutLeftBig;
    animation-name: fadeOutLeftBig
}

@-webkit-keyframes fadeOutRight {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
}

@keyframes fadeOutRight {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
}

.fadeOutRight {
    -webkit-animation-name: fadeOutRight;
    animation-name: fadeOutRight
}

@-webkit-keyframes fadeOutRightBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0)
    }
}

@keyframes fadeOutRightBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0)
    }
}

.fadeOutRightBig {
    -webkit-animation-name: fadeOutRightBig;
    animation-name: fadeOutRightBig
}

@-webkit-keyframes fadeOutUp {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
}

@keyframes fadeOutUp {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
}

.fadeOutUp {
    -webkit-animation-name: fadeOutUp;
    animation-name: fadeOutUp
}

@-webkit-keyframes fadeOutUpBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0)
    }
}

@keyframes fadeOutUpBig {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0)
    }
}

.fadeOutUpBig {
    -webkit-animation-name: fadeOutUpBig;
    animation-name: fadeOutUpBig
}

@-webkit-keyframes flip {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }

    40% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }

    50% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    80% {
        -webkit-transform: perspective(400px) scale3d(.95, .95, .95);
        transform: perspective(400px) scale3d(.95, .95, .95);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }
}

@keyframes flip {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }

    40% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }

    50% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    80% {
        -webkit-transform: perspective(400px) scale3d(.95, .95, .95);
        transform: perspective(400px) scale3d(.95, .95, .95);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }
}

.animated.flip {
    -webkit-backface-visibility: visible;
    backface-visibility: visible;
    -webkit-animation-name: flip;
    animation-name: flip
}

@-webkit-keyframes flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg)
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }
}

@keyframes flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg)
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }
}

.flipInX {
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation-name: flipInX;
    animation-name: flipInX
}

@-webkit-keyframes flipInY {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        opacity: 1
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -5deg)
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }
}

@keyframes flipInY {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        opacity: 1
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -5deg)
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }
}

.flipInY {
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation-name: flipInY;
    animation-name: flipInY
}

@-webkit-keyframes flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0
    }
}

@keyframes flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0
    }
}

.flipOutX {
    -webkit-animation-duration: .75s;
    animation-duration: .75s;
    -webkit-animation-name: flipOutX;
    animation-name: flipOutX;
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important
}

@-webkit-keyframes flipOutY {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        opacity: 1
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        opacity: 0
    }
}

@keyframes flipOutY {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        opacity: 1
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        opacity: 0
    }
}

.flipOutY {
    -webkit-animation-duration: .75s;
    animation-duration: .75s;
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation-name: flipOutY;
    animation-name: flipOutY
}

@-webkit-keyframes lightSpeedIn {
    from {
        -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
        transform: translate3d(100%, 0, 0) skewX(-30deg);
        opacity: 0
    }

    60% {
        -webkit-transform: skewX(20deg);
        transform: skewX(20deg);
        opacity: 1
    }

    80% {
        -webkit-transform: skewX(-5deg);
        transform: skewX(-5deg);
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes lightSpeedIn {
    from {
        -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
        transform: translate3d(100%, 0, 0) skewX(-30deg);
        opacity: 0
    }

    60% {
        -webkit-transform: skewX(20deg);
        transform: skewX(20deg);
        opacity: 1
    }

    80% {
        -webkit-transform: skewX(-5deg);
        transform: skewX(-5deg);
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.lightSpeedIn {
    -webkit-animation-name: lightSpeedIn;
    animation-name: lightSpeedIn;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out
}

@-webkit-keyframes lightSpeedOut {
    from {
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
        transform: translate3d(100%, 0, 0) skewX(30deg);
        opacity: 0
    }
}

@keyframes lightSpeedOut {
    from {
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
        transform: translate3d(100%, 0, 0) skewX(30deg);
        opacity: 0
    }
}

.lightSpeedOut {
    -webkit-animation-name: lightSpeedOut;
    animation-name: lightSpeedOut;
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in
}

@-webkit-keyframes rotateIn {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, -200deg);
        transform: rotate3d(0, 0, 1, -200deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes rotateIn {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, -200deg);
        transform: rotate3d(0, 0, 1, -200deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.rotateIn {
    -webkit-animation-name: rotateIn;
    animation-name: rotateIn
}

@-webkit-keyframes rotateInDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes rotateInDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.rotateInDownLeft {
    -webkit-animation-name: rotateInDownLeft;
    animation-name: rotateInDownLeft
}

@-webkit-keyframes rotateInDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes rotateInDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.rotateInDownRight {
    -webkit-animation-name: rotateInDownRight;
    animation-name: rotateInDownRight
}

@-webkit-keyframes rotateInUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes rotateInUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.rotateInUpLeft {
    -webkit-animation-name: rotateInUpLeft;
    animation-name: rotateInUpLeft
}

@-webkit-keyframes rotateInUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -90deg);
        transform: rotate3d(0, 0, 1, -90deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes rotateInUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -90deg);
        transform: rotate3d(0, 0, 1, -90deg);
        opacity: 0
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.rotateInUpRight {
    -webkit-animation-name: rotateInUpRight;
    animation-name: rotateInUpRight
}

@-webkit-keyframes rotateOut {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        opacity: 1
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, 200deg);
        transform: rotate3d(0, 0, 1, 200deg);
        opacity: 0
    }
}

@keyframes rotateOut {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        opacity: 1
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, 200deg);
        transform: rotate3d(0, 0, 1, 200deg);
        opacity: 0
    }
}

.rotateOut {
    -webkit-animation-name: rotateOut;
    animation-name: rotateOut
}

@-webkit-keyframes rotateOutDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0
    }
}

@keyframes rotateOutDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0
    }
}

.rotateOutDownLeft {
    -webkit-animation-name: rotateOutDownLeft;
    animation-name: rotateOutDownLeft
}

@-webkit-keyframes rotateOutDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0
    }
}

@keyframes rotateOutDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0
    }
}

.rotateOutDownRight {
    -webkit-animation-name: rotateOutDownRight;
    animation-name: rotateOutDownRight
}

@-webkit-keyframes rotateOutUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0
    }
}

@keyframes rotateOutUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0
    }
}

.rotateOutUpLeft {
    -webkit-animation-name: rotateOutUpLeft;
    animation-name: rotateOutUpLeft
}

@-webkit-keyframes rotateOutUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 90deg);
        transform: rotate3d(0, 0, 1, 90deg);
        opacity: 0
    }
}

@keyframes rotateOutUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 90deg);
        transform: rotate3d(0, 0, 1, 90deg);
        opacity: 0
    }
}

.rotateOutUpRight {
    -webkit-animation-name: rotateOutUpRight;
    animation-name: rotateOutUpRight
}

@-webkit-keyframes hinge {
    0% {
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    20%,
    60% {
        -webkit-transform: rotate3d(0, 0, 1, 80deg);
        transform: rotate3d(0, 0, 1, 80deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    40%,
    80% {
        -webkit-transform: rotate3d(0, 0, 1, 60deg);
        transform: rotate3d(0, 0, 1, 60deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(0, 700px, 0);
        transform: translate3d(0, 700px, 0);
        opacity: 0
    }
}

@keyframes hinge {
    0% {
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    20%,
    60% {
        -webkit-transform: rotate3d(0, 0, 1, 80deg);
        transform: rotate3d(0, 0, 1, 80deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    40%,
    80% {
        -webkit-transform: rotate3d(0, 0, 1, 60deg);
        transform: rotate3d(0, 0, 1, 60deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(0, 700px, 0);
        transform: translate3d(0, 700px, 0);
        opacity: 0
    }
}

.hinge {
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-name: hinge;
    animation-name: hinge
}

@-webkit-keyframes jackInTheBox {
    from {
        opacity: 0;
        -webkit-transform: scale(.1) rotate(30deg);
        transform: scale(.1) rotate(30deg);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom
    }

    50% {
        -webkit-transform: rotate(-10deg);
        transform: rotate(-10deg)
    }

    70% {
        -webkit-transform: rotate(3deg);
        transform: rotate(3deg)
    }

    to {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes jackInTheBox {
    from {
        opacity: 0;
        -webkit-transform: scale(.1) rotate(30deg);
        transform: scale(.1) rotate(30deg);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom
    }

    50% {
        -webkit-transform: rotate(-10deg);
        transform: rotate(-10deg)
    }

    70% {
        -webkit-transform: rotate(3deg);
        transform: rotate(3deg)
    }

    to {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.jackInTheBox {
    -webkit-animation-name: jackInTheBox;
    animation-name: jackInTheBox
}

@-webkit-keyframes rollIn {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
        transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes rollIn {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
        transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.rollIn {
    -webkit-animation-name: rollIn;
    animation-name: rollIn
}

@-webkit-keyframes rollOut {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
        transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg)
    }
}

@keyframes rollOut {
    from {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
        transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg)
    }
}

.rollOut {
    -webkit-animation-name: rollOut;
    animation-name: rollOut
}

@-webkit-keyframes zoomIn {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    50% {
        opacity: 1
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    50% {
        opacity: 1
    }
}

.zoomIn {
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn
}

@-webkit-keyframes zoomInDown {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

@keyframes zoomInDown {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

.zoomInDown {
    -webkit-animation-name: zoomInDown;
    animation-name: zoomInDown
}

@-webkit-keyframes zoomInLeft {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

@keyframes zoomInLeft {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

.zoomInLeft {
    -webkit-animation-name: zoomInLeft;
    animation-name: zoomInLeft
}

@-webkit-keyframes zoomInRight {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

@keyframes zoomInRight {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

.zoomInRight {
    -webkit-animation-name: zoomInRight;
    animation-name: zoomInRight
}

@-webkit-keyframes zoomInUp {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

@keyframes zoomInUp {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

.zoomInUp {
    -webkit-animation-name: zoomInUp;
    animation-name: zoomInUp
}

@-webkit-keyframes zoomOut {
    from {
        opacity: 1
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    to {
        opacity: 0
    }
}

@keyframes zoomOut {
    from {
        opacity: 1
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    to {
        opacity: 0
    }
}

.zoomOut {
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut
}

@-webkit-keyframes zoomOutDown {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

@keyframes zoomOutDown {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

.zoomOutDown {
    -webkit-animation-name: zoomOutDown;
    animation-name: zoomOutDown
}

@-webkit-keyframes zoomOutLeft {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(-2000px, 0, 0);
        transform: scale(.1) translate3d(-2000px, 0, 0);
        -webkit-transform-origin: left center;
        transform-origin: left center
    }
}

@keyframes zoomOutLeft {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(-2000px, 0, 0);
        transform: scale(.1) translate3d(-2000px, 0, 0);
        -webkit-transform-origin: left center;
        transform-origin: left center
    }
}

.zoomOutLeft {
    -webkit-animation-name: zoomOutLeft;
    animation-name: zoomOutLeft
}

@-webkit-keyframes zoomOutRight {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(2000px, 0, 0);
        transform: scale(.1) translate3d(2000px, 0, 0);
        -webkit-transform-origin: right center;
        transform-origin: right center
    }
}

@keyframes zoomOutRight {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0)
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(2000px, 0, 0);
        transform: scale(.1) translate3d(2000px, 0, 0);
        -webkit-transform-origin: right center;
        transform-origin: right center
    }
}

.zoomOutRight {
    -webkit-animation-name: zoomOutRight;
    animation-name: zoomOutRight
}

@-webkit-keyframes zoomOutUp {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

@keyframes zoomOutUp {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(.175, .885, .32, 1);
        animation-timing-function: cubic-bezier(.175, .885, .32, 1)
    }
}

.zoomOutUp {
    -webkit-animation-name: zoomOutUp;
    animation-name: zoomOutUp
}

@-webkit-keyframes slideInDown {
    from {
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes slideInDown {
    from {
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.slideInDown {
    -webkit-animation-name: slideInDown;
    animation-name: slideInDown
}

@-webkit-keyframes slideInLeft {
    from {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes slideInLeft {
    from {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.slideInLeft {
    -webkit-animation-name: slideInLeft;
    animation-name: slideInLeft
}

@-webkit-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.slideInRight {
    -webkit-animation-name: slideInRight;
    animation-name: slideInRight
}

@-webkit-keyframes slideInUp {
    from {
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes slideInUp {
    from {
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        visibility: visible
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

.slideInUp {
    -webkit-animation-name: slideInUp;
    animation-name: slideInUp
}

@-webkit-keyframes slideOutDown {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }
}

@keyframes slideOutDown {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }
}

.slideOutDown {
    -webkit-animation-name: slideOutDown;
    animation-name: slideOutDown
}

@-webkit-keyframes slideOutLeft {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

@keyframes slideOutLeft {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

.slideOutLeft {
    -webkit-animation-name: slideOutLeft;
    animation-name: slideOutLeft
}

@-webkit-keyframes slideOutRight {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
}

@keyframes slideOutRight {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
}

.slideOutRight {
    -webkit-animation-name: slideOutRight;
    animation-name: slideOutRight
}

@-webkit-keyframes slideOutUp {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
}

@keyframes slideOutUp {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
}

.slideOutUp {
    -webkit-animation-name: slideOutUp;
    animation-name: slideOutUp
}

/*!
* FullCalendar v3.9.0
* Docs & License: https://fullcalendar.io/
* (c) 2018 Adam Shaw
*/
.fc {
    direction: ltr;
    text-align: left
}

.fc-rtl {
    text-align: right
}

body .fc {
    font-size: 1em
}

.fc-highlight {
    background: #bce8f1;
    opacity: .3
}

.fc-bgevent {
    background: #8fdf82;
    opacity: .3
}

.fc-nonbusiness {
    background: #d7d7d7
}

.fc button {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    height: 2.1em;
    padding: 0 .6em;
    font-size: 1em;
    white-space: nowrap;
    cursor: pointer
}

.fc button::-moz-focus-inner {
    margin: 0;
    padding: 0
}

.fc-state-default {
    border: 1px solid
}

.fc-state-default.fc-corner-left {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px
}

.fc-state-default.fc-corner-right {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px
}

.fc button .fc-icon {
    position: relative;
    top: -.05em;
    margin: 0 .2em;
    vertical-align: middle
}

.fc-state-default {
    background-color: #f5f5f5;
    background-image: -moz-linear-gradient(top, #fff, #e6e6e6);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fff), to(#e6e6e6));
    background-image: -webkit-linear-gradient(top, #fff, #e6e6e6);
    background-image: -o-linear-gradient(top, #fff, #e6e6e6);
    background-image: linear-gradient(to bottom, #fff, #e6e6e6);
    background-repeat: repeat-x;
    border-color: #e6e6e6 #e6e6e6 #bfbfbf;
    border-color: rgba(0, 0, 0, .1) rgba(0, 0, 0, .1) rgba(0, 0, 0, .25);
    color: #333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, .75);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05)
}

.fc-state-active,
.fc-state-disabled,
.fc-state-down,
.fc-state-hover {
    color: #333;
    background-color: #e6e6e6
}

.fc-state-hover {
    color: #333;
    text-decoration: none;
    background-position: 0 -15px;
    -webkit-transition: background-position .1s linear;
    -moz-transition: background-position .1s linear;
    -o-transition: background-position .1s linear;
    transition: background-position .1s linear
}

.fc-state-active,
.fc-state-down {
    background-color: #ccc;
    background-image: none;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, .15), 0 1px 2px rgba(0, 0, 0, .05)
}

.fc-state-disabled {
    cursor: default;
    background-image: none;
    opacity: .65;
    box-shadow: none
}

.fc-button-group {
    display: inline-block
}

.fc .fc-button-group>* {
    float: left;
    margin: 0 0 0 -1px
}

.fc .fc-button-group>:first-child {
    margin-left: 0
}

.fc-popover {
    position: absolute;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .15)
}

.fc-popover .fc-header {
    padding: 2px 4px
}

.fc-popover .fc-header .fc-title {
    margin: 0 2px
}

.fc-popover .fc-header .fc-close {
    cursor: pointer
}

.fc-ltr .fc-popover .fc-header .fc-title {
    float: left
}

.fc-rtl .fc-popover .fc-header .fc-close {
    float: left
}

.fc-rtl .fc-popover .fc-header .fc-title {
    float: right
}

.fc-ltr .fc-popover .fc-header .fc-close {
    float: right
}

.fc-divider {
    border-style: solid;
    border-width: 1px
}

hr.fc-divider {
    height: 0;
    margin: 0;
    padding: 0 0 2px;
    border-width: 1px 0
}

.fc-clear {
    clear: both
}

.fc-bg,
.fc-bgevent-skeleton,
.fc-helper-skeleton,
.fc-highlight-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    right: 0
}

.fc-bg {
    bottom: 0
}

.fc-bg table {
    height: 100%
}

.fc table {
    width: 100%;
    box-sizing: border-box;
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 1em
}

.fc th {
    text-align: center;
    border-style: solid;
    border-width: 1px;
    padding: 0;
    vertical-align: top
}

.fc td {
    border-style: solid;
    border-width: 1px;
    padding: 0;
    vertical-align: top
}

.fc td.fc-today {
    border-style: double
}

a[data-goto] {
    cursor: pointer
}

a[data-goto]:hover {
    text-decoration: underline
}

.fc .fc-row {
    border-style: solid;
    border-width: 0
}

.fc-row {
    position: relative
}

.fc-row table {
    border-left: 0 hidden transparent;
    border-right: 0 hidden transparent;
    border-bottom: 0 hidden transparent
}

.fc-row:first-child table {
    border-top: 0 hidden transparent
}

.fc-row .fc-bg {
    z-index: 1
}

.fc-row .fc-bgevent-skeleton,
.fc-row .fc-highlight-skeleton {
    bottom: 0
}

.fc-row .fc-bgevent-skeleton table {
    height: 100%
}

.fc-row .fc-highlight-skeleton table {
    height: 100%
}

.fc-row .fc-highlight-skeleton td {
    border-color: transparent
}

.fc-row .fc-bgevent-skeleton {
    z-index: 2
}

.fc-row .fc-bgevent-skeleton td {
    border-color: transparent
}

.fc-row .fc-highlight-skeleton {
    z-index: 3
}

.fc-row .fc-content-skeleton {
    position: relative;
    z-index: 4;
    padding-bottom: 2px
}

.fc-row .fc-helper-skeleton {
    z-index: 5
}

.fc .fc-row .fc-content-skeleton table,
.fc .fc-row .fc-content-skeleton td {
    background: 0 0;
    border-color: transparent
}

.fc .fc-row .fc-helper-skeleton td {
    background: 0 0;
    border-color: transparent
}

.fc-row .fc-content-skeleton td,
.fc-row .fc-helper-skeleton td {
    border-bottom: 0
}

.fc-row .fc-content-skeleton tbody td,
.fc-row .fc-helper-skeleton tbody td {
    border-top: 0
}

.fc-scroller {
    -webkit-overflow-scrolling: touch
}

.fc-scroller>.fc-day-grid,
.fc-scroller>.fc-time-grid {
    position: relative;
    width: 100%
}

.fc-event {
    position: relative;
    display: block;
    font-size: .85em;
    line-height: 1.3;
    border-radius: 3px;
    background: #fff;
    border: 1px solid #eee;
    -webkit-box-shadow: 0 1px 15px 1px rgba(69, 65, 78, .08);
    -moz-box-shadow: 0 1px 15px 1px rgba(69, 65, 78, .08);
    box-shadow: 0 1px 15px 1px rgba(69, 65, 78, .08)
}

.fc-event .fc-content {
    padding: .55rem .55rem .55rem 2rem;
    color: #333
}

.fc-event .fc-content:before {
    display: block;
    content: " ";
    position: absolute;
    height: 10px;
    width: 10px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    border-radius: 50%;
    top: .7rem;
    left: .75rem
}

.fc-event .fc-bg {
    background: 0 0 !important
}

.fc-event-dot {
    background-color: #3a87ad
}

.fc-event {
    color: #fff;
    text-decoration: none
}

.fc-event:hover {
    color: #fff;
    text-decoration: none
}

.fc-event.fc-draggable,
.fc-event[href] {
    cursor: pointer
}

.fc-event.fc-black .fc-content:before {
    background: #1a2035 !important;
    border-color: #1a2035 !important
}

.fc-event.fc-primary .fc-content:before {
    background: #1572e8 !important;
    border-color: #1572e8 !important
}

.fc-event.fc-secondary .fc-content:before {
    background: #6861ce !important;
    border-color: #6861ce !important
}

.fc-event.fc-info .fc-content:before {
    background: #48abf7 !important;
    border-color: #48abf7 !important
}

.fc-event.fc-success .fc-content:before {
    background: #31ce36 !important;
    border-color: #31ce36 !important
}

.fc-event.fc-warning .fc-content:before {
    background: #ffad46 !important;
    border-color: #ffad46 !important
}

.fc-event.fc-danger .fc-content:before {
    background: #f25961 !important;
    border-color: #f25961 !important
}

.fc-event.fc-black-solid .fc-content:before,
.fc-event.fc-danger-solid .fc-content:before,
.fc-event.fc-info-solid .fc-content:before,
.fc-event.fc-primary-solid .fc-content:before,
.fc-event.fc-secondary-solid .fc-content:before,
.fc-event.fc-success-solid .fc-content:before,
.fc-event.fc-warning-solid .fc-content:before {
    display: none
}

.fc-event.fc-black-solid .fc-content,
.fc-event.fc-danger-solid .fc-content,
.fc-event.fc-info-solid .fc-content,
.fc-event.fc-primary-solid .fc-content,
.fc-event.fc-secondary-solid .fc-content,
.fc-event.fc-success-solid .fc-content,
.fc-event.fc-warning-solid .fc-content {
    color: #fff;
    padding: .55rem .55rem
}

.fc-event.fc-black-solid {
    background: #1a2035 !important;
    border-color: #1a2035 !important
}

.fc-event.fc-primary-solid {
    background: #1572e8 !important;
    border-color: #1572e8 !important
}

.fc-event.fc-secondary-solid {
    background: #6861ce !important;
    border-color: #6861ce !important
}

.fc-event.fc-info-solid {
    background: #48abf7 !important;
    border-color: #48abf7 !important
}

.fc-event.fc-success-solid {
    background: #31ce36 !important;
    border-color: #31ce36 !important
}

.fc-event.fc-warning-solid {
    background: #ffad46 !important;
    border-color: #ffad46 !important
}

.fc-event.fc-danger-solid {
    background: #f25961 !important;
    border-color: #f25961 !important
}

.fc-list-item.fc-black .fc-event-dot,
.fc-list-item.fc-black-solid .fc-event-dot {
    background: #1a2035 !important
}

.fc-list-item.fc-primary .fc-event-dot,
.fc-list-item.fc-primary-solid .fc-event-dot {
    background: #1572e8 !important
}

.fc-list-item.fc-secondary .fc-event-dot,
.fc-list-item.fc-secondary-solid .fc-event-dot {
    background: #6861ce !important
}

.fc-list-item.fc-info .fc-event-dot,
.fc-list-item.fc-info-solid .fc-event-dot {
    background: #48abf7 !important
}

.fc-list-item.fc-success .fc-event-dot,
.fc-list-item.fc-success-solid .fc-event-dot {
    background: #31ce36 !important
}

.fc-list-item.danger-solid .fc-event-dot,
.fc-list-item.fc-danger .fc-event-dot {
    background: #f25961 !important
}

.fc-list-item.fc-danger .fc-event-dot,
.fc-list-item.fc-warning-solid .fc-event-dot {
    background: #ffad46 !important
}

.fc-widget-content {
    cursor: pointer
}

.fc-not-allowed {
    cursor: not-allowed
}

.fc-not-allowed .fc-event {
    cursor: not-allowed
}

.fc-event .fc-bg {
    z-index: 1;
    background: #fff;
    opacity: .25
}

.fc-event .fc-content {
    position: relative;
    z-index: 2
}

.fc-event .fc-resizer {
    position: absolute;
    z-index: 4;
    display: none
}

.fc-event.fc-allow-mouse-resize .fc-resizer {
    display: block
}

.fc-event.fc-selected {
    z-index: 9999 !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, .2)
}

.fc-event.fc-selected .fc-resizer {
    display: block
}

.fc-event.fc-selected .fc-resizer:before {
    content: "";
    position: absolute;
    z-index: 9999;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin-left: -20px;
    margin-top: -20px
}

.fc-event.fc-selected.fc-dragging {
    box-shadow: 0 2px 7px rgba(0, 0, 0, .3)
}

.fc-h-event.fc-selected:before {
    content: "";
    position: absolute;
    z-index: 3;
    top: -10px;
    bottom: -10px;
    left: 0;
    right: 0
}

.fc-ltr .fc-h-event.fc-not-start,
.fc-rtl .fc-h-event.fc-not-end {
    margin-left: 0;
    border-left-width: 0;
    padding-left: 1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.fc-ltr .fc-h-event.fc-not-end,
.fc-rtl .fc-h-event.fc-not-start {
    margin-right: 0;
    border-right-width: 0;
    padding-right: 1px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.fc-ltr .fc-h-event .fc-start-resizer,
.fc-rtl .fc-h-event .fc-end-resizer {
    cursor: w-resize;
    left: -1px
}

.fc-ltr .fc-h-event .fc-end-resizer,
.fc-rtl .fc-h-event .fc-start-resizer {
    cursor: e-resize;
    right: -1px
}

.fc-h-event.fc-allow-mouse-resize .fc-resizer {
    width: 7px;
    top: -1px;
    bottom: -1px
}

.fc-h-event.fc-selected .fc-resizer {
    border-radius: 4px;
    border-width: 1px;
    width: 6px;
    height: 6px;
    border-style: solid;
    border-color: inherit;
    background: #fff;
    top: 50%;
    margin-top: -4px
}

.fc-ltr .fc-h-event.fc-selected .fc-start-resizer,
.fc-rtl .fc-h-event.fc-selected .fc-end-resizer {
    margin-left: -4px
}

.fc-ltr .fc-h-event.fc-selected .fc-end-resizer,
.fc-rtl .fc-h-event.fc-selected .fc-start-resizer {
    margin-right: -4px
}

.fc-day-grid-event {
    margin: 1px 2px 0;
    padding: 0 1px
}

tr:first-child>td>.fc-day-grid-event {
    margin-top: 2px
}

.fc-day-grid-event.fc-selected:after {
    content: "";
    position: absolute;
    z-index: 1;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    background: #000;
    opacity: .25
}

.fc-day-grid-event .fc-content {
    white-space: nowrap;
    overflow: hidden
}

.fc-day-grid-event .fc-time {
    font-weight: 700
}

.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer,
.fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer {
    margin-left: -2px
}

.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer,
.fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer {
    margin-right: -2px
}

a.fc-more {
    margin: 1px 3px;
    font-size: .85em;
    cursor: pointer;
    text-decoration: none
}

a.fc-more:hover {
    text-decoration: underline
}

.fc-limited {
    display: none
}

.fc-day-grid .fc-row {
    z-index: 1
}

.fc-more-popover {
    z-index: 2;
    width: 220px
}

.fc-more-popover .fc-event-container {
    padding: 10px
}

.fc-now-indicator {
    position: absolute;
    border: 0 solid red
}

.fc-unselectable {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent
}

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
    border-color: #ddd
}

.fc-unthemed .fc-popover {
    background-color: #fff
}

.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-popover .fc-header {
    background: #eee
}

.fc-unthemed .fc-popover .fc-header .fc-close {
    color: #666
}

.fc-unthemed td.fc-today {
    background: #fcf8e3
}

.fc-unthemed .fc-disabled-day {
    background: #d7d7d7;
    opacity: .3
}

.fc-icon {
    display: inline-block;
    height: 1em;
    line-height: 1em;
    font-size: 1em;
    text-align: center;
    overflow: hidden;
    font-family: "Courier New", Courier, monospace;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.fc-icon:after {
    position: relative
}

.fc-icon-left-single-arrow:after {
    content: "\2039";
    font-weight: 700;
    font-size: 200%;
    top: -7%
}

.fc-icon-right-single-arrow:after {
    content: "\203A";
    font-weight: 700;
    font-size: 200%;
    top: -7%
}

.fc-icon-left-double-arrow:after {
    content: "\AB";
    font-size: 160%;
    top: -7%
}

.fc-icon-right-double-arrow:after {
    content: "\BB";
    font-size: 160%;
    top: -7%
}

.fc-icon-left-triangle:after {
    content: "\25C4";
    font-size: 125%;
    top: 3%
}

.fc-icon-right-triangle:after {
    content: "\25BA";
    font-size: 125%;
    top: 3%
}

.fc-icon-down-triangle:after {
    content: "\25BC";
    font-size: 125%;
    top: 2%
}

.fc-icon-x:after {
    content: "\D7";
    font-size: 200%;
    top: 6%
}

.fc-unthemed .fc-popover {
    border-width: 1px;
    border-style: solid
}

.fc-unthemed .fc-popover .fc-header .fc-close {
    font-size: .9em;
    margin-top: 2px
}

.fc-unthemed .fc-list-item:hover td {
    background-color: #f5f5f5
}

.ui-widget .fc-disabled-day {
    background-image: none
}

.fc-popover>.ui-widget-header+.ui-widget-content {
    border-top: 0
}

.ui-widget .fc-event {
    color: #fff;
    text-decoration: none;
    font-weight: 400
}

.ui-widget td.fc-axis {
    font-weight: 400
}

.fc-time-grid .fc-slats .ui-widget-content {
    background: 0 0
}

.fc.fc-bootstrap3 a {
    text-decoration: none
}

.fc.fc-bootstrap3 a[data-goto]:hover {
    text-decoration: underline
}

.fc-bootstrap3 hr.fc-divider {
    border-color: inherit
}

.fc-bootstrap3 .fc-today.alert {
    border-radius: 0
}

.fc-bootstrap3 .fc-popover .panel-body {
    padding: 0
}

.fc-bootstrap3 .fc-time-grid .fc-slats table {
    background: 0 0
}

.fc.fc-bootstrap4 a {
    text-decoration: none
}

.fc.fc-bootstrap4 a[data-goto]:hover {
    text-decoration: underline
}

.fc-bootstrap4 hr.fc-divider {
    border-color: inherit
}

.fc-bootstrap4 .fc-today.alert {
    border-radius: 0
}

.fc-bootstrap4 a.fc-event:not([href]):not([tabindex]) {
    color: #fff
}

.fc-bootstrap4 .fc-popover.card {
    position: absolute
}

.fc-bootstrap4 .fc-popover .card-body {
    padding: 0
}

.fc-bootstrap4 .fc-time-grid .fc-slats table {
    background: 0 0
}

.fc-toolbar {
    text-align: center
}

.fc-toolbar .fc-button {
    background: #f4f5f8;
    border: 0;
    text-shadow: none !important;
    padding: 8px 12px;
    height: auto;
    font-size: 1.04em
}

.fc-toolbar .fc-button:hover {
    background: #eee
}

.fc-toolbar h2 {
    font-size: 1.2rem;
    font-weight: 400;
    text-transform: uppercase;
    margin-top: .75rem
}

.fc-toolbar.fc-header-toolbar {
    margin-bottom: 1em
}

.fc-toolbar.fc-footer-toolbar {
    margin-top: 1em
}

.fc-toolbar .fc-left {
    float: left
}

.fc-toolbar .fc-right {
    float: right
}

.fc-toolbar .fc-center {
    display: inline-block
}

.fc-toolbar .fc-state-active {
    background: #1d7af3;
    color: #fff;
    box-shadow: none !important
}

.fc-toolbar .fc-state-active:hover {
    background: #1d7af3
}

.fc .fc-toolbar>*>* {
    float: left;
    margin-left: .75em
}

.fc .fc-toolbar>*>:first-child {
    margin-left: 0
}

.fc-toolbar h2 {
    margin: 0
}

.fc-toolbar button {
    position: relative
}

.fc-toolbar .fc-state-hover,
.fc-toolbar .ui-state-hover {
    z-index: 2
}

.fc-toolbar .fc-state-down {
    z-index: 3
}

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active {
    z-index: 4
}

.fc-toolbar button:focus {
    z-index: 5
}

.fc-view-container * {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box
}

.fc-view-container :after,
.fc-view-container :before {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box
}

.fc-view {
    position: relative;
    z-index: 1
}

.fc-view>table {
    position: relative;
    z-index: 1
}

.fc-basicDay-view .fc-content-skeleton,
.fc-basicWeek-view .fc-content-skeleton {
    padding-bottom: 1em
}

.fc-basic-view .fc-body .fc-row {
    min-height: 4em
}

.fc-row.fc-rigid {
    overflow: hidden
}

.fc-row.fc-rigid .fc-content-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    right: 0
}

.fc-day-top.fc-other-month {
    opacity: .3
}

.fc-basic-view .fc-day-number,
.fc-basic-view .fc-week-number {
    padding: 2px
}

.fc-basic-view th.fc-day-number,
.fc-basic-view th.fc-week-number {
    padding: 0 2px
}

.fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
    float: right
}

.fc-rtl .fc-basic-view .fc-day-top .fc-day-number {
    float: left
}

.fc-ltr .fc-basic-view .fc-day-top .fc-week-number {
    float: left;
    border-radius: 0 0 3px 0
}

.fc-rtl .fc-basic-view .fc-day-top .fc-week-number {
    float: right;
    border-radius: 0 0 0 3px
}

.fc-basic-view .fc-day-top .fc-week-number {
    min-width: 1.5em;
    text-align: center;
    background-color: #f2f2f2;
    color: grey
}

.fc-basic-view td.fc-week-number {
    text-align: center
}

.fc-basic-view td.fc-week-number>* {
    display: inline-block;
    min-width: 1.25em
}

.fc-agenda-view .fc-day-grid {
    position: relative;
    z-index: 2
}

.fc-agenda-view .fc-day-grid .fc-row {
    min-height: 3em
}

.fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
    padding-bottom: 1em
}

.fc .fc-axis {
    vertical-align: middle;
    padding: 0 4px;
    white-space: nowrap
}

.fc-ltr .fc-axis {
    text-align: right
}

.fc-rtl .fc-axis {
    text-align: left
}

.fc-time-grid-container {
    position: relative;
    z-index: 1
}

.fc-time-grid {
    position: relative;
    z-index: 1;
    min-height: 100%
}

.fc-time-grid table {
    border: 0 hidden transparent
}

.fc-time-grid>.fc-bg {
    z-index: 1
}

.fc-time-grid .fc-slats,
.fc-time-grid>hr {
    position: relative;
    z-index: 2
}

.fc-time-grid .fc-content-col {
    position: relative
}

.fc-time-grid .fc-content-skeleton {
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    right: 0
}

.fc-time-grid .fc-business-container {
    position: relative;
    z-index: 1
}

.fc-time-grid .fc-bgevent-container {
    position: relative;
    z-index: 2
}

.fc-time-grid .fc-highlight-container {
    position: relative;
    z-index: 3
}

.fc-time-grid .fc-event-container {
    position: relative;
    z-index: 4
}

.fc-time-grid .fc-now-indicator-line {
    z-index: 5
}

.fc-time-grid .fc-helper-container {
    position: relative;
    z-index: 6
}

.fc-time-grid .fc-slats td {
    height: 1.5em;
    border-bottom: 0
}

.fc-time-grid .fc-slats .fc-minor td {
    border-top-style: dotted
}

.fc-time-grid .fc-highlight-container {
    position: relative
}

.fc-time-grid .fc-highlight {
    position: absolute;
    left: 0;
    right: 0
}

.fc-ltr .fc-time-grid .fc-event-container {
    margin: 0 2.5% 0 2px
}

.fc-rtl .fc-time-grid .fc-event-container {
    margin: 0 2px 0 2.5%
}

.fc-time-grid .fc-event {
    position: absolute;
    z-index: 1
}

.fc-time-grid .fc-bgevent {
    position: absolute;
    z-index: 1;
    left: 0;
    right: 0
}

.fc-v-event.fc-not-start {
    border-top-width: 0;
    padding-top: 1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.fc-v-event.fc-not-end {
    border-bottom-width: 0;
    padding-bottom: 1px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.fc-time-grid-event {
    overflow: hidden
}

.fc-time-grid-event.fc-selected {
    overflow: visible
}

.fc-time-grid-event.fc-selected .fc-bg {
    display: none
}

.fc-time-grid-event .fc-content {
    overflow: hidden
}

.fc-time-grid-event .fc-time,
.fc-time-grid-event .fc-title {
    padding: 0 1px
}

.fc-time-grid-event .fc-time {
    font-size: .85em;
    white-space: nowrap
}

.fc-time-grid-event.fc-short .fc-content {
    white-space: nowrap
}

.fc-time-grid-event.fc-short .fc-time,
.fc-time-grid-event.fc-short .fc-title {
    display: inline-block;
    vertical-align: top
}

.fc-time-grid-event.fc-short .fc-time span {
    display: none
}

.fc-time-grid-event.fc-short .fc-time:before {
    content: attr(data-start)
}

.fc-time-grid-event.fc-short .fc-time:after {
    content: "\A0-\A0"
}

.fc-time-grid-event.fc-short .fc-title {
    font-size: .85em;
    padding: 0
}

.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer {
    left: 0;
    right: 0;
    bottom: 0;
    height: 8px;
    overflow: hidden;
    line-height: 8px;
    font-size: 11px;
    font-family: monospace;
    text-align: center;
    cursor: s-resize
}

.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer:after {
    content: "="
}

.fc-time-grid-event.fc-selected .fc-resizer {
    border-radius: 5px;
    border-width: 1px;
    width: 8px;
    height: 8px;
    border-style: solid;
    border-color: inherit;
    background: #fff;
    left: 50%;
    margin-left: -5px;
    bottom: -5px
}

.fc-time-grid .fc-now-indicator-line {
    border-top-width: 1px;
    left: 0;
    right: 0
}

.fc-time-grid .fc-now-indicator-arrow {
    margin-top: -5px
}

.fc-ltr .fc-time-grid .fc-now-indicator-arrow {
    left: 0;
    border-width: 5px 0 5px 6px;
    border-top-color: transparent;
    border-bottom-color: transparent
}

.fc-rtl .fc-time-grid .fc-now-indicator-arrow {
    right: 0;
    border-width: 5px 6px 5px 0;
    border-top-color: transparent;
    border-bottom-color: transparent
}

.fc-event-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 5px
}

.fc-rtl .fc-list-view {
    direction: rtl
}

.fc-list-view {
    border-width: 1px;
    border-style: solid
}

.fc .fc-list-table {
    table-layout: auto
}

.fc-list-table td {
    border-width: 1px 0 0;
    padding: 8px 14px
}

.fc-list-table tr:first-child td {
    border-top-width: 0
}

.fc-list-heading {
    border-bottom-width: 1px
}

.fc-list-heading td {
    font-weight: 700
}

.fc-ltr .fc-list-heading-main {
    float: left
}

.fc-ltr .fc-list-heading-alt {
    float: right
}

.fc-rtl .fc-list-heading-main {
    float: right
}

.fc-rtl .fc-list-heading-alt {
    float: left
}

.fc-list-item.fc-has-url {
    cursor: pointer
}

.fc-list-item-marker,
.fc-list-item-time {
    white-space: nowrap;
    width: 1px
}

.fc-ltr .fc-list-item-marker {
    padding-right: 0
}

.fc-rtl .fc-list-item-marker {
    padding-left: 0
}

.fc-list-item-title a {
    text-decoration: none;
    color: inherit
}

.fc-list-item-title a[href]:hover {
    text-decoration: underline
}

.fc-list-empty-wrap2 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.fc-list-empty-wrap1 {
    width: 100%;
    height: 100%;
    display: table
}

.fc-list-empty {
    display: table-cell;
    vertical-align: middle;
    text-align: center
}

.fc-unthemed .fc-list-empty {
    background-color: #eee
}

.swal-footer {
    text-align: center !important;
    margin-bottom: 20px !important
}

table.dataTable {
    clear: both;
    margin-top: 15px !important;
    margin-bottom: 15px !important;
    max-width: none !important;
    border-collapse: separate !important
}

table.dataTable td,
table.dataTable th {
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
    text-align: center
}

table.dataTable.nowrap td,
table.dataTable.nowrap th {
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length label {
    font-weight: 400;
    text-align: left;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length select {
    width: 75px;
    display: inline-block
}

div.dataTables_wrapper div.dataTables_filter {
    text-align: right
}

div.dataTables_wrapper div.dataTables_filter label {
    font-weight: 400;
    white-space: nowrap;
    text-align: left
}

div.dataTables_wrapper div.dataTables_filter input {
    margin-left: .5em;
    display: inline-block;
    width: auto
}

div.dataTables_wrapper div.dataTables_info {
    padding-top: .85em;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_paginate {
    margin: 0;
    white-space: nowrap;
    text-align: right
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    margin: 2px 0;
    white-space: nowrap;
    justify-content: flex-end
}

div.dataTables_wrapper div.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0
}

table.dataTable thead>tr>td.sorting,
table.dataTable thead>tr>td.sorting_asc,
table.dataTable thead>tr>td.sorting_desc,
table.dataTable thead>tr>th.sorting,
table.dataTable thead>tr>th.sorting_asc,
table.dataTable thead>tr>th.sorting_desc {
    padding-right: 30px
}

table.dataTable thead>tr>td:active,
table.dataTable thead>tr>th:active {
    outline: 0
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_desc_disabled {
    cursor: pointer;
    position: relative
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc_disabled:after,
table.dataTable thead .sorting_desc_disabled:before {
    position: absolute;
    bottom: .9em;
    display: block;
    opacity: .5
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc_disabled:before {
    right: 1em;
    content: "\2191";
    font-size: 15px
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_desc_disabled:after {
    right: .5em;
    content: "\2193";
    font-size: 15px
}

table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:after {
    opacity: 1
}

table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    opacity: 0
}

div.dataTables_scrollHead table.dataTable {
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table thead .sorting:after,
div.dataTables_scrollBody table thead .sorting_asc:after,
div.dataTables_scrollBody table thead .sorting_desc:after {
    display: none
}

div.dataTables_scrollBody table tbody tr:first-child td,
div.dataTables_scrollBody table tbody tr:first-child th {
    border-top: none
}

div.dataTables_scrollFoot>.dataTables_scrollFootInner {
    box-sizing: content-box
}

div.dataTables_scrollFoot>.dataTables_scrollFootInner>table {
    margin-top: 0 !important;
    border-top: none
}

@media screen and (max-width:767px) {

    div.dataTables_wrapper div.dataTables_filter,
    div.dataTables_wrapper div.dataTables_info,
    div.dataTables_wrapper div.dataTables_length,
    div.dataTables_wrapper div.dataTables_paginate {
        text-align: center;
        margin-top: 11px;
        margin-bottom: 10px
    }

    div.dataTables_wrapper div div.dataTables_paginate ul.pagination {
        flex-wrap: wrap !important;
        justify-content: center !important
    }

    div.dataTables_wrapper div div.dataTables_paginate ul.pagination li {
        margin-bottom: 10px
    }

    div.dataTables_wrapper div div.dataTables_paginate ul.pagination li a {
        font-size: 11px
    }
}

table.dataTable.table-sm>thead>tr>th {
    padding-right: 20px
}

table.dataTable.table-sm .sorting:before,
table.dataTable.table-sm .sorting_asc:before,
table.dataTable.table-sm .sorting_desc:before {
    top: 5px;
    right: .85em
}

table.dataTable.table-sm .sorting:after,
table.dataTable.table-sm .sorting_asc:after,
table.dataTable.table-sm .sorting_desc:after {
    top: 5px
}

table.table-bordered.dataTable td,
table.table-bordered.dataTable th {
    border-left-width: 0
}

table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable th:last-child {
    border-right-width: 0
}

table.table-bordered.dataTable tbody td,
table.table-bordered.dataTable tbody th {
    border-bottom-width: 0
}

div.dataTables_scrollHead table.table-bordered {
    border-bottom-width: 0
}

div.table-responsive>div.dataTables_wrapper>div.row {
    margin: 0
}

div.table-responsive>div.dataTables_wrapper>div.row>div[class^=col-]:first-child,
div.table-responsive>div.dataTables_wrapper>div.row>div[class^=col-]:last-child {
    padding-left: 0;
    padding-right: 0
}

/*!
 * Datetimepicker for Bootstrap 3
 * version : 4.17.47
 * https://github.com/Eonasdan/bootstrap-datetimepicker/
 */
.bootstrap-datetimepicker-widget {
    list-style: none;
    z-index: 100
}

.bootstrap-datetimepicker-widget.dropdown-menu {
    padding: 10px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    direction: ltr;
    border: 0 !important;
    -webkit-box-shadow: 0 0 15px 1px rgba(69, 65, 78, .2) !important;
    -moz-box-shadow: 0 0 15px 1px rgba(69, 65, 78, .2) !important;
    box-shadow: 0 0 15px 1px rgba(69, 65, 78, .2) !important;
    font-size: 12px;
    font-weight: 400;
    margin-top: 10px !important;
    margin-bottom: 10px !important
}

.bootstrap-datetimepicker-widget .list-unstyled {
    margin: 0
}

.bootstrap-datetimepicker-widget a[data-action] {
    padding: 6px 0;
    font-size: 16px;
    font-weight: 800;
    color: #1572e8
}

.bootstrap-datetimepicker-widget a[data-action]:active {
    box-shadow: none;
    color: #1572e8
}

.bootstrap-datetimepicker-widget .timepicker-hour,
.bootstrap-datetimepicker-widget .timepicker-minute,
.bootstrap-datetimepicker-widget .timepicker-second {
    width: 54px;
    font-weight: 700;
    font-size: 1.2em;
    margin: 0
}

.bootstrap-datetimepicker-widget button[data-action] {
    padding: 6px
}

.bootstrap-datetimepicker-widget .btn[data-action=incrementHours]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Increment Hours"
}

.bootstrap-datetimepicker-widget .btn[data-action=incrementMinutes]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Increment Minutes"
}

.bootstrap-datetimepicker-widget .btn[data-action=decrementHours]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Decrement Hours"
}

.bootstrap-datetimepicker-widget .btn[data-action=decrementMinutes]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Decrement Minutes"
}

.bootstrap-datetimepicker-widget .btn[data-action=showHours]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Show Hours"
}

.bootstrap-datetimepicker-widget .btn[data-action=showMinutes]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Show Minutes"
}

.bootstrap-datetimepicker-widget .btn[data-action=togglePeriod]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Toggle AM/PM"
}

.bootstrap-datetimepicker-widget .btn[data-action=clear]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Clear the picker"
}

.bootstrap-datetimepicker-widget .btn[data-action=today]::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Set the date to today"
}

.bootstrap-datetimepicker-widget .picker-switch {
    text-align: center
}

.bootstrap-datetimepicker-widget .picker-switch::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Toggle Date and Time Screens"
}

.bootstrap-datetimepicker-widget .picker-switch td {
    padding: 0;
    margin: 0;
    height: auto;
    width: auto;
    line-height: inherit
}

.bootstrap-datetimepicker-widget .picker-switch td span {
    font-size: 16px;
    line-height: 2.5;
    height: 2.5em;
    width: 100%;
    font-weight: 800
}

.bootstrap-datetimepicker-widget table {
    width: 100%;
    margin: 0
}

.bootstrap-datetimepicker-widget table td,
.bootstrap-datetimepicker-widget table th {
    width: 30px;
    height: 30px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    border: none
}

.bootstrap-datetimepicker-widget table td {
    text-align: center;
    border-radius: 3px
}

.bootstrap-datetimepicker-widget table th {
    text-align: center;
    border-radius: 2px;
    line-height: 20px
}

.bootstrap-datetimepicker-widget table th.picker-switch {
    width: 145px
}

.bootstrap-datetimepicker-widget table th.disabled {
    background: 0 0;
    color: #777;
    cursor: not-allowed
}

.bootstrap-datetimepicker-widget table th.disabled:hover {
    background: 0 0;
    color: #777;
    cursor: not-allowed
}

.bootstrap-datetimepicker-widget table th.prev::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Previous Month"
}

.bootstrap-datetimepicker-widget table th.next::after {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    content: "Next Month"
}

.bootstrap-datetimepicker-widget table thead tr:first-child th {
    cursor: pointer
}

.bootstrap-datetimepicker-widget table thead tr:first-child th:hover {
    background: #eee
}

.bootstrap-datetimepicker-widget table td {
    height: 54px;
    line-height: 54px;
    width: 54px
}

.bootstrap-datetimepicker-widget table td.cw {
    font-size: .8em;
    height: 20px;
    line-height: 20px;
    color: #777
}

.bootstrap-datetimepicker-widget table td.day {
    height: 25px;
    line-height: 25px;
    width: 25px
}

.bootstrap-datetimepicker-widget table td.day:hover {
    background: #eee;
    cursor: pointer
}

.bootstrap-datetimepicker-widget table td.hour:hover,
.bootstrap-datetimepicker-widget table td.minute:hover,
.bootstrap-datetimepicker-widget table td.second:hover {
    background: #eee;
    cursor: pointer
}

.bootstrap-datetimepicker-widget table td.new,
.bootstrap-datetimepicker-widget table td.old {
    color: #777
}

.bootstrap-datetimepicker-widget table td.today {
    position: relative
}

.bootstrap-datetimepicker-widget table td.today:before {
    content: '';
    display: inline-block;
    border: solid transparent;
    border-width: 0 0 7px 7px;
    border-bottom-color: #1572e8;
    border-top-color: rgba(0, 0, 0, .2);
    position: absolute;
    bottom: 4px;
    right: 4px
}

.bootstrap-datetimepicker-widget table td.active {
    background-color: #1572e8;
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25)
}

.bootstrap-datetimepicker-widget table td.active:hover {
    background-color: #1572e8;
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25)
}

.bootstrap-datetimepicker-widget table td.active.today:before {
    border-bottom-color: #fff
}

.bootstrap-datetimepicker-widget table td.disabled {
    background: 0 0;
    color: #777;
    cursor: not-allowed
}

.bootstrap-datetimepicker-widget table td.disabled:hover {
    background: 0 0;
    color: #777;
    cursor: not-allowed
}

.bootstrap-datetimepicker-widget table td span {
    display: inline-block;
    width: 54px;
    height: 54px;
    line-height: 54px;
    margin: 2px 1.5px;
    cursor: pointer;
    border-radius: 4px
}

.bootstrap-datetimepicker-widget table td span:hover {
    color: #1572e8;
    background: #eee
}

.bootstrap-datetimepicker-widget table td span.active {
    background-color: #1572e8;
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25)
}

.bootstrap-datetimepicker-widget table td span.old {
    color: #777
}

.bootstrap-datetimepicker-widget table td span.disabled {
    background: 0 0;
    color: #777;
    cursor: not-allowed
}

.bootstrap-datetimepicker-widget table td span.disabled:hover {
    background: 0 0;
    color: #777;
    cursor: not-allowed
}

.bootstrap-datetimepicker-widget.usetwentyfour td.hour {
    height: 27px;
    line-height: 27px
}

.bootstrap-datetimepicker-widget .datepicker-decades .decade {
    line-height: 1.8em !important
}

.input-group.date .input-group-addon {
    cursor: pointer
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.select2-container {
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    position: relative;
    vertical-align: middle
}

.select2-container .select2-selection--single {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 28px;
    user-select: none;
    -webkit-user-select: none
}

.select2-container .select2-selection--single .select2-selection__rendered {
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container .select2-selection--single .select2-selection__clear {
    position: relative
}

.select2-container[dir=rtl] .select2-selection--single .select2-selection__rendered {
    padding-right: 8px;
    padding-left: 20px
}

.select2-container .select2-selection--multiple {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    min-height: 32px;
    user-select: none;
    -webkit-user-select: none
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
    display: inline-block;
    overflow: hidden;
    padding-left: 8px;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container .select2-search--inline {
    float: left
}

.select2-container .select2-search--inline .select2-search__field {
    box-sizing: border-box;
    border: none;
    font-size: 100%;
    margin-top: 5px;
    padding: 0
}

.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none
}

.select2-dropdown {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    box-sizing: border-box;
    display: block;
    position: absolute;
    left: -100000px;
    width: 100%;
    z-index: 1051
}

.select2-results {
    display: block
}

.select2-results__options {
    list-style: none;
    margin: 0;
    padding: 0
}

.select2-results__option {
    padding: 6px;
    user-select: none;
    -webkit-user-select: none
}

.select2-results__option[aria-selected] {
    cursor: pointer
}

.select2-container--open .select2-dropdown {
    left: 0
}

.select2-container--open .select2-dropdown--above {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--open .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-search--dropdown {
    display: block;
    padding: 5px 10px
}

.select2-search--dropdown .select2-search__field {
    padding: 4px;
    width: 100%;
    box-sizing: border-box
}

.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none
}

.select2-search--dropdown.select2-search--hide {
    display: none
}

.select2-close-mask {
    border: 0;
    margin: 0;
    padding: 0;
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    min-height: 100%;
    min-width: 100%;
    height: auto;
    width: auto;
    opacity: 0;
    z-index: 99;
    background-color: #fff
}

.select2-hidden-accessible {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    -webkit-clip-path: inset(50%) !important;
    clip-path: inset(50%) !important;
    height: 1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
    white-space: nowrap !important
}

.select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px
}

.select2-container--default .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: 700
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #999
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0
}

.select2-container--default[dir=rtl] .select2-selection--single .select2-selection__clear {
    float: left
}

.select2-container--default[dir=rtl] .select2-selection--single .select2-selection__arrow {
    left: 1px;
    right: auto
}

.select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: #eee;
    cursor: default
}

.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
    display: none
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px
}

.select2-container--default .select2-selection--multiple {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 0 5px;
    width: 100%
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    list-style: none
}

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #999;
    margin-top: 5px;
    float: left
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: 700;
    margin-top: 5px;
    margin-right: 10px
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #999;
    cursor: pointer;
    display: inline-block;
    font-weight: 700;
    margin-right: 2px
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #333
}

.select2-container--default[dir=rtl] .select2-selection--multiple .select2-search--inline,
.select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice,
.select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__placeholder {
    float: right
}

.select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice {
    margin-left: 5px;
    margin-right: auto
}

.select2-container--default[dir=rtl] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: solid #000 1px;
    outline: 0
}

.select2-container--default.select2-container--disabled .select2-selection--multiple {
    background-color: #eee;
    cursor: default
}

.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
    display: none
}

.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--below .select2-selection--single {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa
}

.select2-container--default .select2-search--inline .select2-search__field {
    background: 0 0;
    border: none;
    outline: 0;
    box-shadow: none;
    -webkit-appearance: textfield
}

.select2-container--default .select2-results>.select2-results__options {
    max-height: 200px;
    overflow-y: auto
}

.select2-container--default .select2-results__option[role=group] {
    padding: 0
}

.select2-container--default .select2-results__option[aria-disabled=true] {
    color: #999
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #ddd
}

.select2-container--default .select2-results__option .select2-results__option {
    padding-left: 1em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
    padding-left: 0
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -1em;
    padding-left: 2em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -2em;
    padding-left: 3em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -3em;
    padding-left: 4em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -4em;
    padding-left: 5em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -5em;
    padding-left: 6em
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #5897fb;
    color: #fff
}

.select2-container--default .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px
}

.select2-container--classic .select2-selection--single {
    background-color: #f7f7f7;
    border: 1px solid #aaa;
    border-radius: 4px;
    outline: 0;
    background-image: -webkit-linear-gradient(top, #fff 50%, #eee 100%);
    background-image: -o-linear-gradient(top, #fff 50%, #eee 100%);
    background-image: linear-gradient(to bottom, #fff 50%, #eee 100%);
    background-repeat: repeat-x
}

.select2-container--classic .select2-selection--single:focus {
    border: 1px solid #5897fb
}

.select2-container--classic .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px
}

.select2-container--classic .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: 700;
    margin-right: 10px
}

.select2-container--classic .select2-selection--single .select2-selection__placeholder {
    color: #999
}

.select2-container--classic .select2-selection--single .select2-selection__arrow {
    background-color: #ddd;
    border: none;
    border-left: 1px solid #aaa;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px;
    background-image: -webkit-linear-gradient(top, #eee 50%, #ccc 100%);
    background-image: -o-linear-gradient(top, #eee 50%, #ccc 100%);
    background-image: linear-gradient(to bottom, #eee 50%, #ccc 100%);
    background-repeat: repeat-x
}

.select2-container--classic .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0
}

.select2-container--classic[dir=rtl] .select2-selection--single .select2-selection__clear {
    float: left
}

.select2-container--classic[dir=rtl] .select2-selection--single .select2-selection__arrow {
    border: none;
    border-right: 1px solid #aaa;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    left: 1px;
    right: auto
}

.select2-container--classic.select2-container--open .select2-selection--single {
    border: 1px solid #5897fb
}

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
    background: 0 0;
    border: none
}

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px
}

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background-image: -webkit-linear-gradient(top, #fff 0, #eee 50%);
    background-image: -o-linear-gradient(top, #fff 0, #eee 50%);
    background-image: linear-gradient(to bottom, #fff 0, #eee 50%);
    background-repeat: repeat-x
}

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    background-image: -webkit-linear-gradient(top, #eee 50%, #fff 100%);
    background-image: -o-linear-gradient(top, #eee 50%, #fff 100%);
    background-image: linear-gradient(to bottom, #eee 50%, #fff 100%);
    background-repeat: repeat-x
}

.select2-container--classic .select2-selection--multiple {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text;
    outline: 0
}

.select2-container--classic .select2-selection--multiple:focus {
    border: 1px solid #5897fb
}

.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
    list-style: none;
    margin: 0;
    padding: 0 5px
}

.select2-container--classic .select2-selection--multiple .select2-selection__clear {
    display: none
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
    color: #888;
    cursor: pointer;
    display: inline-block;
    font-weight: 700;
    margin-right: 2px
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #555
}

.select2-container--classic[dir=rtl] .select2-selection--multiple .select2-selection__choice {
    float: right;
    margin-left: 5px;
    margin-right: auto
}

.select2-container--classic[dir=rtl] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto
}

.select2-container--classic.select2-container--open .select2-selection--multiple {
    border: 1px solid #5897fb
}

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--classic .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
    outline: 0
}

.select2-container--classic .select2-search--inline .select2-search__field {
    outline: 0;
    box-shadow: none
}

.select2-container--classic .select2-dropdown {
    background-color: #fff;
    border: 1px solid transparent
}

.select2-container--classic .select2-dropdown--above {
    border-bottom: none
}

.select2-container--classic .select2-dropdown--below {
    border-top: none
}

.select2-container--classic .select2-results>.select2-results__options {
    max-height: 200px;
    overflow-y: auto
}

.select2-container--classic .select2-results__option[role=group] {
    padding: 0
}

.select2-container--classic .select2-results__option[aria-disabled=true] {
    color: grey
}

.select2-container--classic .select2-results__option--highlighted[aria-selected] {
    background-color: #3875d7;
    color: #fff
}

.select2-container--classic .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px
}

.select2-container--classic.select2-container--open .select2-dropdown {
    border-color: #5897fb
}

/*!
 * Select2 Bootstrap Theme v0.1.0-beta.10 (https://select2.github.io/select2-bootstrap-theme)
 * Copyright 2015-2017 Florian Kissling and contributors (https://github.com/select2/select2-bootstrap-theme/graphs/contributors)
 * Licensed under MIT (https://github.com/select2/select2-bootstrap-theme/blob/master/LICENSE)
 */
.select2-container--bootstrap {
    display: block
}

.select2-container--bootstrap .select2-selection {
    background-color: #fff;
    border: 1px solid #ebedf2;
    border-radius: 4px;
    color: inherit;
    font-size: 13px;
    outline: 0;
    line-height: 1.5 !important
}

.select2-container--bootstrap .select2-selection.form-control {
    border-radius: 4px
}

.select2-container--bootstrap .select2-search--dropdown .select2-search__field {
    background-color: #fff;
    border: 1px solid #ebedf2;
    border-radius: 4px;
    color: #555;
    font-size: 13px;
    padding: 4px 10px
}

.select2-container--bootstrap .select2-search__field {
    outline: 0
}

.select2-container--bootstrap .select2-search__field:-moz-placeholder,
.select2-container--bootstrap .select2-search__field::-webkit-input-placeholder {
    color: #999
}

.select2-container--bootstrap .select2-search__field::-moz-placeholder {
    color: #999;
    opacity: 1
}

.select2-container--bootstrap .select2-search__field:-ms-input-placeholder {
    color: #999
}

.select2-container--bootstrap .select2-results__option {
    padding: 6px 12px
}

.select2-container--bootstrap .select2-results__option[role=group] {
    padding: 0
}

.select2-container--bootstrap .select2-results__option[aria-disabled=true] {
    color: #777;
    cursor: not-allowed
}

.select2-container--bootstrap .select2-results__option[aria-selected=true] {
    background-color: #f5f5f5;
    color: #262626
}

.select2-container--bootstrap .select2-results__option--highlighted[aria-selected] {
    background-color: #1572e8;
    color: #fff
}

.select2-container--bootstrap .select2-results__option .select2-results__option {
    padding: 6px 12px
}

.select2-container--bootstrap .select2-results__option .select2-results__option .select2-results__group {
    padding-left: 0
}

.select2-container--bootstrap .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -12px;
    padding-left: 24px
}

.select2-container--bootstrap .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -24px;
    padding-left: 36px
}

.select2-container--bootstrap .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -36px;
    padding-left: 48px
}

.select2-container--bootstrap .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -48px;
    padding-left: 60px
}

.select2-container--bootstrap .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -60px;
    padding-left: 72px
}

.select2-container--bootstrap .select2-results__group {
    color: #9e9e9e;
    font-weight: 400;
    display: block;
    padding: 7px 12px;
    line-height: 1.42857143;
    white-space: nowrap;
    margin-top: 6px
}

.select2-container--bootstrap.select2-container--focus .select2-selection {
    border-color: #66afe9
}

.select2-container--bootstrap.select2-container--open .select2-selection {
    -webkit-box-shadow: 0 0 15px 1px rgba(69, 65, 78, .2) !important;
    -moz-box-shadow: 0 0 15px 1px rgba(69, 65, 78, .2) !important;
    box-shadow: 0 0 15px 1px rgba(69, 65, 78, .2) !important;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s, -webkit-box-shadow ease-in-out .15s
}

.select2-container--bootstrap.select2-container--open .select2-selection .select2-selection__arrow b {
    border-color: transparent transparent #999;
    border-width: 0 4px 4px
}

.select2-container--bootstrap.select2-container--open.select2-container--below .select2-selection {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-color: transparent
}

.select2-container--bootstrap.select2-container--open.select2-container--above .select2-selection {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-top-color: transparent
}

.select2-container--bootstrap .select2-selection__clear {
    color: #999;
    cursor: pointer;
    float: right;
    font-weight: 700;
    margin-right: 10px
}

.select2-container--bootstrap .select2-selection__clear:hover {
    color: #333
}

.select2-container--bootstrap.select2-container--disabled .select2-selection {
    border-color: #ccc;
    -webkit-box-shadow: none;
    box-shadow: none
}

.select2-container--bootstrap.select2-container--disabled .select2-search__field {
    cursor: not-allowed
}

.select2-container--bootstrap.select2-container--disabled .select2-selection {
    cursor: not-allowed;
    background-color: #eee
}

.select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
    background-color: #eee
}

.select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice__remove {
    display: none
}

.select2-container--bootstrap.select2-container--disabled .select2-selection__clear {
    display: none
}

.select2-container--bootstrap .select2-dropdown {
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    border-color: #ebedf2;
    overflow-x: hidden;
    margin-top: -1px
}

.select2-container--bootstrap .select2-dropdown--above {
    -webkit-box-shadow: 0 -6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 -6px 12px rgba(0, 0, 0, .175);
    margin-top: 1px
}

.select2-container--bootstrap .select2-results>.select2-results__options {
    max-height: 200px;
    overflow-y: auto
}

.select2-container--bootstrap .select2-selection--single {
    height: 40px;
    line-height: 1.42857143;
    padding: .6rem 1rem
}

.select2-container--bootstrap .select2-selection--single .select2-selection__arrow {
    position: absolute;
    bottom: 0;
    right: 12px;
    top: 0;
    width: 4px
}

.select2-container--bootstrap .select2-selection--single .select2-selection__arrow b {
    border-color: #999 transparent transparent;
    border-style: solid;
    border-width: 4px 4px 0;
    height: 0;
    left: 0;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0
}

.select2-container--bootstrap .select2-selection--single .select2-selection__rendered {
    color: #555;
    padding: 0
}

.select2-container--bootstrap .select2-selection--single .select2-selection__placeholder {
    color: #999
}

.select2-container--bootstrap .select2-selection--multiple {
    padding: 3.25px 0;
    height: auto
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__rendered {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    display: block;
    line-height: 1.42857143;
    list-style: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__placeholder {
    color: #999;
    float: left;
    margin-top: 5px
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__choice {
    color: #555;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 30px;
    cursor: default;
    float: left;
    margin: 5px 0 0 6px;
    font-size: 11px;
    padding: 3px 23px 3px 10px;
    position: relative
}

.select2-container--bootstrap .select2-selection--multiple .select2-search--inline .select2-search__field {
    background: 0 0;
    padding: 0 12px;
    height: 32px;
    line-height: 1.42857143;
    margin-top: 0;
    min-width: 5em
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__choice__remove {
    color: #666 !important;
    cursor: pointer;
    display: inline-block;
    margin-right: 3px;
    position: absolute;
    right: 5px;
    font-weight: 400
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #666 !important
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__clear {
    margin-top: 6px
}

.form-group-sm .select2-container--bootstrap .select2-selection--single,
.input-group-sm .select2-container--bootstrap .select2-selection--single,
.select2-container--bootstrap .select2-selection--single.input-sm {
    border-radius: 3px;
    font-size: 12px;
    height: 30px;
    line-height: 1.5;
    padding: 5px 22px 5px 10px
}

.form-group-sm .select2-container--bootstrap .select2-selection--single .select2-selection__arrow b,
.input-group-sm .select2-container--bootstrap .select2-selection--single .select2-selection__arrow b,
.select2-container--bootstrap .select2-selection--single.input-sm .select2-selection__arrow b {
    margin-left: -5px
}

.form-group-sm .select2-container--bootstrap .select2-selection--multiple,
.input-group-sm .select2-container--bootstrap .select2-selection--multiple,
.select2-container--bootstrap .select2-selection--multiple.input-sm {
    min-height: 30px;
    border-radius: 3px
}

.form-group-sm .select2-container--bootstrap .select2-selection--multiple .select2-selection__choice,
.input-group-sm .select2-container--bootstrap .select2-selection--multiple .select2-selection__choice,
.select2-container--bootstrap .select2-selection--multiple.input-sm .select2-selection__choice {
    font-size: 12px;
    line-height: 1.5;
    margin: 4px 0 0 5px;
    padding: 0 5px
}

.form-group-sm .select2-container--bootstrap .select2-selection--multiple .select2-search--inline .select2-search__field,
.input-group-sm .select2-container--bootstrap .select2-selection--multiple .select2-search--inline .select2-search__field,
.select2-container--bootstrap .select2-selection--multiple.input-sm .select2-search--inline .select2-search__field {
    padding: 0 10px;
    font-size: 12px;
    height: 28px;
    line-height: 1.5
}

.form-group-sm .select2-container--bootstrap .select2-selection--multiple .select2-selection__clear,
.input-group-sm .select2-container--bootstrap .select2-selection--multiple .select2-selection__clear,
.select2-container--bootstrap .select2-selection--multiple.input-sm .select2-selection__clear {
    margin-top: 5px
}

.form-group-lg .select2-container--bootstrap .select2-selection--single,
.input-group-lg .select2-container--bootstrap .select2-selection--single,
.select2-container--bootstrap .select2-selection--single.input-lg {
    border-radius: 6px;
    font-size: 18px;
    height: 46px;
    line-height: 1.3333333;
    padding: 10px 31px 10px 16px
}

.form-group-lg .select2-container--bootstrap .select2-selection--single .select2-selection__arrow,
.input-group-lg .select2-container--bootstrap .select2-selection--single .select2-selection__arrow,
.select2-container--bootstrap .select2-selection--single.input-lg .select2-selection__arrow {
    width: 5px
}

.form-group-lg .select2-container--bootstrap .select2-selection--single .select2-selection__arrow b,
.input-group-lg .select2-container--bootstrap .select2-selection--single .select2-selection__arrow b,
.select2-container--bootstrap .select2-selection--single.input-lg .select2-selection__arrow b {
    border-width: 5px 5px 0;
    margin-left: -10px;
    margin-top: -2.5px
}

.form-group-lg .select2-container--bootstrap .select2-selection--multiple,
.input-group-lg .select2-container--bootstrap .select2-selection--multiple,
.select2-container--bootstrap .select2-selection--multiple.input-lg {
    min-height: 46px;
    border-radius: 6px
}

.form-group-lg .select2-container--bootstrap .select2-selection--multiple .select2-selection__choice,
.input-group-lg .select2-container--bootstrap .select2-selection--multiple .select2-selection__choice,
.select2-container--bootstrap .select2-selection--multiple.input-lg .select2-selection__choice {
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 4px;
    margin: 9px 0 0 8px;
    padding: 0 10px
}

.form-group-lg .select2-container--bootstrap .select2-selection--multiple .select2-search--inline .select2-search__field,
.input-group-lg .select2-container--bootstrap .select2-selection--multiple .select2-search--inline .select2-search__field,
.select2-container--bootstrap .select2-selection--multiple.input-lg .select2-search--inline .select2-search__field {
    padding: 0 16px;
    font-size: 18px;
    height: 44px;
    line-height: 1.3333333
}

.form-group-lg .select2-container--bootstrap .select2-selection--multiple .select2-selection__clear,
.input-group-lg .select2-container--bootstrap .select2-selection--multiple .select2-selection__clear,
.select2-container--bootstrap .select2-selection--multiple.input-lg .select2-selection__clear {
    margin-top: 10px
}

.input-group-lg .select2-container--bootstrap .select2-selection.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #999;
    border-width: 0 5px 5px
}

.select2-container--bootstrap .select2-selection.input-lg.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #999;
    border-width: 0 5px 5px
}

.select2-container--bootstrap[dir=rtl] .select2-selection--single {
    padding-left: 24px;
    padding-right: 12px
}

.select2-container--bootstrap[dir=rtl] .select2-selection--single .select2-selection__rendered {
    padding-right: 0;
    padding-left: 0;
    text-align: right
}

.select2-container--bootstrap[dir=rtl] .select2-selection--single .select2-selection__clear {
    float: left
}

.select2-container--bootstrap[dir=rtl] .select2-selection--single .select2-selection__arrow {
    left: 12px;
    right: auto
}

.select2-container--bootstrap[dir=rtl] .select2-selection--single .select2-selection__arrow b {
    margin-left: 0
}

.select2-container--bootstrap[dir=rtl] .select2-selection--multiple .select2-search--inline,
.select2-container--bootstrap[dir=rtl] .select2-selection--multiple .select2-selection__choice,
.select2-container--bootstrap[dir=rtl] .select2-selection--multiple .select2-selection__placeholder {
    float: right
}

.select2-container--bootstrap[dir=rtl] .select2-selection--multiple .select2-selection__choice {
    margin-left: 0;
    margin-right: 6px
}

.select2-container--bootstrap[dir=rtl] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto
}

.has-warning .select2-dropdown,
.has-warning .select2-selection {
    border-color: #8a6d3b
}

.has-warning .select2-container--focus .select2-selection,
.has-warning .select2-container--open .select2-selection {
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #c0a16b;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #c0a16b;
    border-color: #66512c
}

.has-warning.select2-drop-active {
    border-color: #66512c
}

.has-warning.select2-drop-active.select2-drop.select2-drop-above {
    border-top-color: #66512c
}

.has-error .select2-dropdown,
.has-error .select2-selection {
    border-color: #f25961
}

.has-error .select2-dropdown .select2-selection__rendered,
.has-error .select2-selection .select2-selection__rendered {
    color: #f25961
}

.has-error .select2-container--focus .select2-selection,
.has-error .select2-container--open .select2-selection {
    border-color: #f25961
}

.has-error.select2-drop-active {
    border-color: #f25961
}

.has-error.select2-drop-active.select2-drop.select2-drop-above {
    border-top-color: #f25961
}

.has-success .select2-dropdown,
.has-success .select2-selection {
    border-color: #31ce36
}

.has-success .select2-dropdown .select2-selection__rendered,
.has-success .select2-selection .select2-selection__rendered {
    color: #31ce36
}

.has-success .select2-container--focus .select2-selection,
.has-success .select2-container--open .select2-selection {
    border-color: #31ce36
}

.has-success.select2-drop-active {
    border-color: #31ce36
}

.has-success.select2-drop-active.select2-drop.select2-drop-above {
    border-top-color: #31ce36
}

.input-group>.select2-hidden-accessible:first-child+.select2-container--bootstrap>.selection>.select2-selection {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.input-group>.select2-hidden-accessible:first-child+.select2-container--bootstrap>.selection>.select2-selection.form-control {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.input-group>.select2-hidden-accessible:not(:first-child)+.select2-container--bootstrap:not(:last-child)>.selection>.select2-selection {
    border-radius: 0
}

.input-group>.select2-hidden-accessible:not(:first-child)+.select2-container--bootstrap:not(:last-child)>.selection>.select2-selection.form-control {
    border-radius: 0
}

.input-group>.select2-hidden-accessible:not(:first-child):not(:last-child)+.select2-container--bootstrap:last-child>.selection>.select2-selection {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.input-group>.select2-hidden-accessible:not(:first-child):not(:last-child)+.select2-container--bootstrap:last-child>.selection>.select2-selection.form-control {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.input-group>.select2-container--bootstrap {
    display: table;
    table-layout: fixed;
    position: relative;
    z-index: 2;
    width: 100%;
    margin-bottom: 0;
    vertical-align: top
}

.input-group>.select2-container--bootstrap>.selection>.select2-selection.form-control {
    float: none
}

.input-group>.select2-container--bootstrap.select2-container--focus,
.input-group>.select2-container--bootstrap.select2-container--open {
    z-index: 3
}

.input-group>.select2-container--bootstrap .input-group-btn {
    vertical-align: top
}

.input-group>.select2-container--bootstrap .input-group-btn .btn {
    vertical-align: top
}

.form-control.select2-hidden-accessible {
    position: absolute !important;
    width: 1px !important
}

.select2-black .select2-selection__choice .select2-selection__choice__remove,
.select2-danger .select2-selection__choice .select2-selection__choice__remove,
.select2-info .select2-selection__choice .select2-selection__choice__remove,
.select2-primary .select2-selection__choice .select2-selection__choice__remove,
.select2-success .select2-selection__choice .select2-selection__choice__remove,
.select2-warning .select2-selection__choice .select2-selection__choice__remove {
    color: #fff !important
}

.select2-black .select2-selection__choice .select2-selection__choice__remove:hover,
.select2-danger .select2-selection__choice .select2-selection__choice__remove:hover,
.select2-info .select2-selection__choice .select2-selection__choice__remove:hover,
.select2-primary .select2-selection__choice .select2-selection__choice__remove:hover,
.select2-success .select2-selection__choice .select2-selection__choice__remove:hover,
.select2-warning .select2-selection__choice .select2-selection__choice__remove:hover {
    color: #fff !important
}

.select2-black .select2-selection__choice {
    background: #1a2035 !important;
    border-color: #1a2035 !important;
    color: #fff !important
}

.select2-primary .select2-selection__choice {
    background: #1572e8 !important;
    border-color: #1572e8 !important;
    color: #fff !important
}

.select2-secondary .select2-selection__choice {
    background: #6861ce !important;
    border-color: #6861ce !important;
    color: #fff !important
}

.select2-info .select2-selection__choice {
    background: #48abf7 !important;
    border-color: #48abf7 !important;
    color: #fff !important
}

.select2-success .select2-selection__choice {
    background: #31ce36 !important;
    border-color: #31ce36 !important;
    color: #fff !important
}

.select2-danger .select2-selection__choice {
    background: #f25961 !important;
    border-color: #f25961 !important;
    color: #fff !important
}

.select2-warning .select2-selection__choice {
    background: #ffad46 !important;
    border-color: #ffad46 !important;
    color: #fff !important
}

@media (min-width:768px) {
    .form-inline .select2-container--bootstrap {
        display: inline-block
    }
}

.bootstrap-tagsinput {
    background-color: #fff;
    display: inline-block;
    padding: 4px 6px;
    color: #555;
    vertical-align: middle;
    border-radius: 4px;
    max-width: 100%;
    line-height: 22px;
    cursor: text
}

.bootstrap-tagsinput input {
    border: none;
    box-shadow: none;
    outline: 0;
    background-color: transparent;
    padding: 0 6px;
    margin: 0;
    width: auto;
    max-width: inherit
}

.bootstrap-tagsinput.form-control input::-moz-placeholder {
    color: #777;
    opacity: 1
}

.bootstrap-tagsinput.form-control input:-ms-input-placeholder,
.bootstrap-tagsinput.form-control input::-webkit-input-placeholder {
    color: #777
}

.bootstrap-tagsinput input:focus {
    border: none;
    box-shadow: none
}

.bootstrap-tagsinput .tag {
    margin-right: 2px;
    margin-bottom: 10px
}

.bootstrap-tagsinput .tag [data-role=remove] {
    margin-left: 8px;
    cursor: pointer
}

.bootstrap-tagsinput .tag [data-role=remove]:after {
    content: "x";
    padding: 0 2px
}

.bootstrap-tagsinput .tag [data-role=remove]:hover {
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05)
}

.bootstrap-tagsinput .tag [data-role=remove]:hover:active {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

@-webkit-keyframes passing-through {
    0% {
        opacity: 0;
        -webkit-transform: translateY(40px);
        -moz-transform: translateY(40px);
        -ms-transform: translateY(40px);
        -o-transform: translateY(40px);
        transform: translateY(40px)
    }

    30%,
    70% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        opacity: 0;
        -webkit-transform: translateY(-40px);
        -moz-transform: translateY(-40px);
        -ms-transform: translateY(-40px);
        -o-transform: translateY(-40px);
        transform: translateY(-40px)
    }
}

@-moz-keyframes passing-through {
    0% {
        opacity: 0;
        -webkit-transform: translateY(40px);
        -moz-transform: translateY(40px);
        -ms-transform: translateY(40px);
        -o-transform: translateY(40px);
        transform: translateY(40px)
    }

    30%,
    70% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        opacity: 0;
        -webkit-transform: translateY(-40px);
        -moz-transform: translateY(-40px);
        -ms-transform: translateY(-40px);
        -o-transform: translateY(-40px);
        transform: translateY(-40px)
    }
}

@keyframes passing-through {
    0% {
        opacity: 0;
        -webkit-transform: translateY(40px);
        -moz-transform: translateY(40px);
        -ms-transform: translateY(40px);
        -o-transform: translateY(40px);
        transform: translateY(40px)
    }

    30%,
    70% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        opacity: 0;
        -webkit-transform: translateY(-40px);
        -moz-transform: translateY(-40px);
        -ms-transform: translateY(-40px);
        -o-transform: translateY(-40px);
        transform: translateY(-40px)
    }
}

@-webkit-keyframes slide-in {
    0% {
        opacity: 0;
        -webkit-transform: translateY(40px);
        -moz-transform: translateY(40px);
        -ms-transform: translateY(40px);
        -o-transform: translateY(40px);
        transform: translateY(40px)
    }

    30% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0)
    }
}

@-moz-keyframes slide-in {
    0% {
        opacity: 0;
        -webkit-transform: translateY(40px);
        -moz-transform: translateY(40px);
        -ms-transform: translateY(40px);
        -o-transform: translateY(40px);
        transform: translateY(40px)
    }

    30% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes slide-in {
    0% {
        opacity: 0;
        -webkit-transform: translateY(40px);
        -moz-transform: translateY(40px);
        -ms-transform: translateY(40px);
        -o-transform: translateY(40px);
        transform: translateY(40px)
    }

    30% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0)
    }
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }

    10% {
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
        -o-transform: scale(1.1);
        transform: scale(1.1)
    }

    20% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }
}

@-moz-keyframes pulse {
    0% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }

    10% {
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
        -o-transform: scale(1.1);
        transform: scale(1.1)
    }

    20% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes pulse {
    0% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }

    10% {
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
        -o-transform: scale(1.1);
        transform: scale(1.1)
    }

    20% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1)
    }
}

.dropzone,
.dropzone * {
    box-sizing: border-box
}

.dropzone {
    min-height: 150px;
    border: 2px solid rgba(0, 0, 0, .3);
    background: #fff;
    padding: 20px 20px
}

.dropzone.dz-clickable {
    cursor: pointer
}

.dropzone.dz-clickable * {
    cursor: default
}

.dropzone.dz-clickable .dz-message,
.dropzone.dz-clickable .dz-message * {
    cursor: pointer
}

.dropzone.dz-started .dz-message {
    display: none
}

.dropzone.dz-drag-hover {
    border-style: solid
}

.dropzone.dz-drag-hover .dz-message {
    opacity: .5
}

.dropzone .dz-message {
    text-align: center;
    margin: 2em 0
}

.dropzone .dz-preview {
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin: 16px;
    min-height: 100px
}

.dropzone .dz-preview:hover {
    z-index: 1000
}

.dropzone .dz-preview:hover .dz-details {
    opacity: 1
}

.dropzone .dz-preview.dz-file-preview .dz-image {
    border-radius: 20px;
    background: #999;
    background: linear-gradient(to bottom, #eee, #ddd)
}

.dropzone .dz-preview.dz-file-preview .dz-details {
    opacity: 1
}

.dropzone .dz-preview.dz-image-preview {
    background: #fff
}

.dropzone .dz-preview.dz-image-preview .dz-details {
    -webkit-transition: opacity .2s linear;
    -moz-transition: opacity .2s linear;
    -ms-transition: opacity .2s linear;
    -o-transition: opacity .2s linear;
    transition: opacity .2s linear
}

.dropzone .dz-preview .dz-remove {
    font-size: 1rem;
    text-align: center;
    display: block;
    cursor: pointer;
    border: none
}

.dropzone .dz-preview .dz-remove:hover {
    text-decoration: underline
}

.dropzone .dz-preview:hover .dz-details {
    opacity: 1
}

.dropzone .dz-preview .dz-details {
    z-index: 20;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    font-size: 13px;
    min-width: 100%;
    max-width: 100%;
    padding: 2em 1em;
    text-align: center;
    color: rgba(0, 0, 0, .9);
    line-height: 150%
}

.dropzone .dz-preview .dz-details .dz-size {
    margin-bottom: 1em;
    font-size: 16px
}

.dropzone .dz-preview .dz-details .dz-filename {
    white-space: nowrap
}

.dropzone .dz-preview .dz-details .dz-filename:hover span {
    border: 1px solid rgba(200, 200, 200, .8);
    background-color: rgba(255, 255, 255, .8)
}

.dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
    overflow: hidden;
    text-overflow: ellipsis
}

.dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {
    border: 1px solid transparent
}

.dropzone .dz-preview .dz-details .dz-filename span,
.dropzone .dz-preview .dz-details .dz-size span {
    background-color: rgba(255, 255, 255, .4);
    padding: 0 .4em;
    border-radius: 3px
}

.dropzone .dz-preview:hover .dz-image img {
    -webkit-transform: scale(1.05, 1.05);
    -moz-transform: scale(1.05, 1.05);
    -ms-transform: scale(1.05, 1.05);
    -o-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
    -webkit-filter: blur(8px);
    filter: blur(8px)
}

.dropzone .dz-preview .dz-image {
    border-radius: 20px;
    overflow: hidden;
    width: 120px;
    height: 120px;
    position: relative;
    display: block;
    z-index: 10
}

.dropzone .dz-preview .dz-image img {
    display: block
}

.dropzone .dz-preview.dz-success .dz-success-mark {
    -webkit-animation: passing-through 3s cubic-bezier(.77, 0, .175, 1);
    -moz-animation: passing-through 3s cubic-bezier(.77, 0, .175, 1);
    -ms-animation: passing-through 3s cubic-bezier(.77, 0, .175, 1);
    -o-animation: passing-through 3s cubic-bezier(.77, 0, .175, 1);
    animation: passing-through 3s cubic-bezier(.77, 0, .175, 1)
}

.dropzone .dz-preview.dz-error .dz-error-mark {
    opacity: 1;
    -webkit-animation: slide-in 3s cubic-bezier(.77, 0, .175, 1);
    -moz-animation: slide-in 3s cubic-bezier(.77, 0, .175, 1);
    -ms-animation: slide-in 3s cubic-bezier(.77, 0, .175, 1);
    -o-animation: slide-in 3s cubic-bezier(.77, 0, .175, 1);
    animation: slide-in 3s cubic-bezier(.77, 0, .175, 1)
}

.dropzone .dz-preview .dz-error-mark,
.dropzone .dz-preview .dz-success-mark {
    pointer-events: none;
    opacity: 0;
    z-index: 500;
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    margin-left: -27px;
    margin-top: -27px
}

.dropzone .dz-preview .dz-error-mark svg,
.dropzone .dz-preview .dz-success-mark svg {
    display: block;
    width: 54px;
    height: 54px
}

.dropzone .dz-preview.dz-processing .dz-progress {
    opacity: 1;
    -webkit-transition: all .2s linear;
    -moz-transition: all .2s linear;
    -ms-transition: all .2s linear;
    -o-transition: all .2s linear;
    transition: all .2s linear
}

.dropzone .dz-preview.dz-complete .dz-progress {
    opacity: 0;
    -webkit-transition: opacity .4s ease-in;
    -moz-transition: opacity .4s ease-in;
    -ms-transition: opacity .4s ease-in;
    -o-transition: opacity .4s ease-in;
    transition: opacity .4s ease-in
}

.dropzone .dz-preview:not(.dz-processing) .dz-progress {
    -webkit-animation: pulse 6s ease infinite;
    -moz-animation: pulse 6s ease infinite;
    -ms-animation: pulse 6s ease infinite;
    -o-animation: pulse 6s ease infinite;
    animation: pulse 6s ease infinite
}

.dropzone .dz-preview .dz-progress {
    opacity: 1;
    z-index: 1000;
    pointer-events: none;
    position: absolute;
    height: 16px;
    left: 50%;
    top: 50%;
    margin-top: -8px;
    width: 80px;
    margin-left: -40px;
    background: rgba(255, 255, 255, .9);
    -webkit-transform: scale(1);
    border-radius: 8px;
    overflow: hidden
}

.dropzone .dz-preview .dz-progress .dz-upload {
    background: #333;
    background: linear-gradient(to bottom, #666, #444);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    -webkit-transition: width .3s ease-in-out;
    -moz-transition: width .3s ease-in-out;
    -ms-transition: width .3s ease-in-out;
    -o-transition: width .3s ease-in-out;
    transition: width .3s ease-in-out
}

.dropzone .dz-preview.dz-error .dz-error-message {
    display: block
}

.dropzone .dz-preview.dz-error:hover .dz-error-message {
    opacity: 1;
    pointer-events: auto
}

.dropzone .dz-preview .dz-error-message {
    pointer-events: none;
    z-index: 1000;
    position: absolute;
    display: block;
    display: none;
    opacity: 0;
    -webkit-transition: opacity .3s ease;
    -moz-transition: opacity .3s ease;
    -ms-transition: opacity .3s ease;
    -o-transition: opacity .3s ease;
    transition: opacity .3s ease;
    border-radius: 8px;
    font-size: 13px;
    top: 130px;
    left: -10px;
    width: 140px;
    background: #be2626;
    background: linear-gradient(to bottom, #be2626, #a92222);
    padding: .5em 1.2em;
    color: #fff
}

.dropzone .dz-preview .dz-error-message:after {
    content: '';
    position: absolute;
    top: -6px;
    left: 64px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #be2626
}


[class*=" note-icon"]:before,
[class^=note-icon]:before {
    display: inline-block;
    font-family: summernote;
    font-style: normal;
    font-size: inherit;
    text-decoration: inherit;
    text-rendering: auto;
    text-transform: none;
    vertical-align: middle;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    speak: none
}

.note-icon-fw {
    text-align: center;
    width: 1.25em
}

.note-icon-border {
    border: .08em solid #eee;
    border-radius: .1em;
    padding: .2em .25em .15em
}

.note-icon-pull-left {
    float: left
}

.note-icon-pull-right {
    float: right
}

.note-icon.note-icon-pull-left {
    margin-right: .3em
}

.note-icon.note-icon-pull-right {
    margin-left: .3em
}

.note-icon-align:before {
    content: ""
}

.note-icon-align-center:before {
    content: ""
}

.note-icon-align-indent:before {
    content: ""
}

.note-icon-align-justify:before {
    content: ""
}

.note-icon-align-left:before {
    content: ""
}

.note-icon-align-outdent:before {
    content: ""
}

.note-icon-align-right:before {
    content: ""
}

.note-icon-arrow-circle-down:before {
    content: ""
}

.note-icon-arrow-circle-left:before {
    content: ""
}

.note-icon-arrow-circle-right:before {
    content: ""
}

.note-icon-arrow-circle-up:before {
    content: ""
}

.note-icon-arrows-alt:before {
    content: ""
}

.note-icon-arrows-h:before {
    content: ""
}

.note-icon-arrows-v:before {
    content: ""
}

.note-icon-bold:before {
    content: ""
}

.note-icon-caret:before {
    content: ""
}

.note-icon-chain-broken:before {
    content: ""
}

.note-icon-circle:before {
    content: ""
}

.note-icon-close:before {
    content: ""
}

.note-icon-code:before {
    content: ""
}

.note-icon-col-after:before {
    content: ""
}

.note-icon-col-before:before {
    content: ""
}

.note-icon-col-remove:before {
    content: ""
}

.note-icon-eraser:before {
    content: ""
}

.note-icon-float-left:before {
    content: ""
}

.note-icon-float-none:before {
    content: ""
}

.note-icon-float-right:before {
    content: ""
}

.note-icon-font:before {
    content: ""
}

.note-icon-frame:before {
    content: ""
}

.note-icon-italic:before {
    content: ""
}

.note-icon-link:before {
    content: ""
}

.note-icon-magic:before {
    content: ""
}

.note-icon-menu-check:before {
    content: ""
}

.note-icon-minus:before {
    content: ""
}

.note-icon-orderedlist:before {
    content: ""
}

.note-icon-pencil:before {
    content: ""
}

.note-icon-picture:before {
    content: ""
}

.note-icon-question:before {
    content: ""
}

.note-icon-redo:before {
    content: ""
}

.note-icon-rollback:before {
    content: ""
}

.note-icon-row-above:before {
    content: ""
}

.note-icon-row-below:before {
    content: ""
}

.note-icon-row-remove:before {
    content: ""
}

.note-icon-special-character:before {
    content: ""
}

.note-icon-square:before {
    content: ""
}

.note-icon-strikethrough:before {
    content: ""
}

.note-icon-subscript:before {
    content: ""
}

.note-icon-summernote:before {
    content: ""
}

.note-icon-superscript:before {
    content: ""
}

.note-icon-table:before {
    content: ""
}

.note-icon-text-height:before {
    content: ""
}

.note-icon-trash:before {
    content: ""
}

.note-icon-underline:before {
    content: ""
}

.note-icon-undo:before {
    content: ""
}

.note-icon-unorderedlist:before {
    content: ""
}

.note-icon-video:before {
    content: ""
}

.note-frame {
    -ms-box-sizing: border-box;
    box-sizing: border-box;
    color: #000;
    font-family: sans-serif;
    border-radius: 4px
}

.note-toolbar {
    padding: 10px 5px;
    color: #333;
    background-color: #f5f5f5;
    border-bottom: 1px solid;
    border-color: #ddd;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px
}

.note-btn-group {
    position: relative;
    display: inline-block;
    margin-right: 8px
}

.note-btn-group>.note-btn-group {
    margin-right: 0
}

.note-btn-group>.note-btn:first-child {
    margin-left: 0
}

.note-btn-group .note-btn+.note-btn,
.note-btn-group .note-btn+.note-btn-group,
.note-btn-group .note-btn-group+.note-btn,
.note-btn-group .note-btn-group+.note-btn-group {
    margin-left: -1px
}

.note-btn-group>.note-btn-group:not(:first-child)>.note-btn,
.note-btn-group>.note-btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.note-btn-group>.note-btn-group:not(:last-child)>.note-btn,
.note-btn-group>.note-btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.note-btn-group.open>.note-dropdown {
    display: block
}

.note-btn {
    display: inline-block;
    font-weight: 400;
    margin-bottom: 0;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    white-space: nowrap;
    outline: 0;
    color: #333;
    background-color: #fff;
    border: 1px solid #dae0e5;
    padding: 5px 10px;
    font-size: 14px;
    line-height: 1.4;
    border-radius: 3px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.note-btn.focus,
.note-btn:focus,
.note-btn:hover {
    color: #333;
    background-color: #ebebeb;
    border-color: #dae0e5
}

.note-btn.disabled.focus,
.note-btn.disabled:focus,
.note-btn[disabled].focus,
.note-btn[disabled]:focus,
fieldset[disabled] .note-btn.focus,
fieldset[disabled] .note-btn:focus {
    background-color: #fff;
    border-color: #dae0e5
}

.note-btn.active,
.note-btn.focus,
.note-btn:active,
.note-btn:focus,
.note-btn:hover {
    color: #333;
    text-decoration: none;
    border: 1px solid #dae0e5;
    background-color: #ebebeb;
    outline: 0;
    border-radius: 1px
}

.note-btn.active,
.note-btn:active {
    background-image: none;
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

.note-btn.disabled,
.note-btn[disabled],
fieldset[disabled] .note-btn {
    cursor: not-allowed;
    -webkit-opacity: .65;
    -khtml-opacity: .65;
    -moz-opacity: .65;
    opacity: .65;
    box-shadow: none
}

.note-btn>span.note-icon-caret:first-child {
    margin-left: -1px
}

.note-btn>span.note-icon-caret:nth-child(2) {
    padding-left: 3px;
    margin-right: -5px
}

.note-btn-primary {
    background: #fa6362;
    color: #fff
}

.note-btn-primary.focus,
.note-btn-primary:focus,
.note-btn-primary:hover {
    color: #fff;
    text-decoration: none;
    border: 1px solid #dae0e5;
    background-color: #fa6362;
    border-radius: 1px
}

.note-btn-block {
    display: block;
    width: 100%
}

.note-btn-block+.note-btn-block {
    margin-top: 5px
}

input[type=button].note-btn-block,
input[type=reset].note-btn-block,
input[type=submit].note-btn-block {
    width: 100%
}

button.close {
    padding: 0;
    cursor: pointer;
    background: 0 0;
    border: 0;
    -webkit-appearance: none
}

.close {
    float: right;
    font-size: 21px;
    line-height: 1;
    color: #000;
    opacity: .2
}

.close:hover {
    -webkit-opacity: 1;
    -khtml-opacity: 1;
    -moz-opacity: 1;
    opacity: 1
}

.note-dropdown {
    position: relative
}

.note-color .dropdown-toggle {
    width: 30px;
    padding-left: 5px
}

.note-dropdown-menu {
    display: none;
    min-width: 100px;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    float: left;
    text-align: left;
    background: #fff;
    border: 1px solid #e2e2e2;
    padding: 5px;
    background-clip: padding-box;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .06)
}

.note-dropdown-menu>:last-child {
    margin-right: 0
}

.note-btn-group.open .note-dropdown-menu,
.note-dropdown-item {
    display: block
}

.note-dropdown-item:hover {
    background-color: #ebebeb
}

a.note-dropdown-item,
a.note-dropdown-item:hover {
    margin: 5px 0;
    color: #000;
    text-decoration: none
}

.note-modal {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1050;
    -webkit-opacity: 1;
    -khtml-opacity: 1;
    -moz-opacity: 1;
    opacity: 1;
    display: none
}

.note-modal.open {
    display: block
}

.note-modal-content {
    position: relative;
    width: auto;
    margin: 30px 20px;
    border: 1px solid rgba(0, 0, 0, .2);
    background: #fff;
    background-clip: border-box;
    outline: 0;
    border-radius: 5px;
    box-shadow: 0 3px 9px rgba(0, 0, 0, .5)
}

.note-modal-header {
    padding: 10px 20px;
    border: 1px solid #ededef
}

.note-modal-body {
    position: relative;
    padding: 20px 30px
}

.note-modal-body kbd {
    border-radius: 2px;
    background-color: #000;
    color: #fff;
    padding: 3px 5px;
    font-weight: 700;
    -ms-box-sizing: border-box;
    box-sizing: border-box
}

.note-modal-footer {
    height: 40px;
    padding: 10px;
    text-align: center
}

.note-modal-footer a {
    color: #337ab7;
    text-decoration: none
}

.note-modal-footer a:focus,
.note-modal-footer a:hover {
    color: #23527c;
    text-decoration: underline
}

.note-modal-footer .note-btn {
    float: right
}

.note-modal-title {
    font-size: 20px;
    color: #42515f;
    margin: 0;
    line-height: 1.4
}

.note-modal-backdrop {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 1040;
    background: #000;
    -webkit-opacity: .5;
    -khtml-opacity: .5;
    -moz-opacity: .5;
    opacity: .5;
    display: none
}

.note-modal-backdrop.open {
    display: block
}

@media (min-width:768px) {
    .note-modal-content {
        width: 600px;
        margin: 30px auto
    }
}

@media (min-width:992px) {
    .note-modal-content-large {
        width: 900px
    }
}

.note-modal .note-help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #737373
}

.note-modal .note-nav {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.note-modal .note-nav-link {
    display: block;
    padding: .5rem 1rem;
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
    -webkit-text-decoration-skip: objects
}

.note-modal .note-nav-link:focus,
.note-modal .note-nav-link:hover {
    color: #0056b3;
    text-decoration: none
}

.note-modal .note-nav-link.disabled {
    color: #868e96
}

.note-modal .note-nav-tabs {
    border-bottom: 1px solid #ddd
}

.note-modal .note-nav-tabs .note-nav-item {
    margin-bottom: -1px
}

.note-modal .note-nav-tabs .note-nav-link {
    border: 1px solid transparent;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}

.note-modal .note-nav-tabs .note-nav-link:focus,
.note-modal .note-nav-tabs .note-nav-link:hover {
    border-color: #e9ecef #e9ecef #ddd
}

.note-modal .note-nav-tabs .note-nav-link.disabled {
    color: #868e96;
    background-color: transparent;
    border-color: transparent
}

.note-modal .note-nav-tabs .note-nav-item.show .note-nav-link {
    color: #495057;
    background-color: #fff;
    border-color: #ddd #ddd #fff
}

.note-modal .note-tab-content {
    margin: 15px auto
}

.note-modal .note-tab-content>.note-tab-pane,
.note-modal .note-tab-content>.note-tab-pane:target~.note-tab-pane:last-child {
    display: none
}

.note-modal .note-tab-content>.note-tab-pane:target,
.note-modal .note-tab-content>:last-child {
    display: block
}

.note-form-group {
    padding-bottom: 20px
}

.note-form-group:last-child {
    padding-bottom: 0
}

.note-form-label {
    display: block;
    width: 100%;
    font-size: 16px;
    color: #42515f;
    margin-bottom: 10px;
    font-weight: 700
}

.note-input {
    width: 100%;
    display: block;
    border: 1px solid #ededef;
    background: #fff;
    outline: 0;
    padding: 6px 4px;
    font-size: 14px;
    -ms-box-sizing: border-box;
    box-sizing: border-box
}

.note-input::-webkit-input-placeholder {
    color: #eee
}

.note-input:-moz-placeholder,
.note-input::-moz-placeholder {
    color: #eee
}

.note-input:-ms-input-placeholder {
    color: #eee
}

.note-tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-size: 13px;
    transition: opacity .15s;
    -webkit-opacity: 0;
    -khtml-opacity: 0;
    -moz-opacity: 0;
    opacity: 0
}

.note-tooltip.in {
    -webkit-opacity: .9;
    -khtml-opacity: .9;
    -moz-opacity: .9;
    opacity: .9
}

.note-tooltip.top {
    margin-top: -3px;
    padding: 5px 0
}

.note-tooltip.right {
    margin-left: 3px;
    padding: 0 5px
}

.note-tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0
}

.note-tooltip.left {
    margin-left: -3px;
    padding: 0 5px
}

.note-tooltip.bottom .note-tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.note-tooltip.top .note-tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.note-tooltip.right .note-tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000
}

.note-tooltip.left .note-tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #000
}

.note-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.note-tooltip-content {
    max-width: 200px;
    font-family: sans-serif;
    padding: 3px 8px;
    color: #fff;
    text-align: center;
    background-color: #000
}

.note-popover {
    position: absolute;
    z-index: 1060;
    display: block;
    font-size: 13px;
    font-family: sans-serif;
    display: none;
    background: #fff;
    border: 1px solid #ccc
}

.note-popover.in {
    display: block
}

.note-popover.top {
    margin-top: -10px;
    padding: 5px 0
}

.note-popover.right {
    margin-left: 10px;
    padding: 0 5px
}

.note-popover.bottom {
    margin-top: 10px;
    padding: 5px 0
}

.note-popover.left {
    margin-left: -10px;
    padding: 0 5px
}

.note-popover.bottom .note-popover-arrow {
    top: -11px;
    left: 20px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #999;
    border-bottom-color: rgba(0, 0, 0, .25)
}

.note-popover.bottom .note-popover-arrow:after {
    top: 1px;
    margin-left: -10px;
    content: " ";
    border-top-width: 0;
    border-bottom-color: #fff
}

.note-popover.top .note-popover-arrow {
    bottom: -11px;
    left: 20px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #999;
    border-top-color: rgba(0, 0, 0, .25)
}

.note-popover.top .note-popover-arrow:after {
    bottom: 1px;
    margin-left: -10px;
    content: " ";
    border-bottom-width: 0;
    border-top-color: #fff
}

.note-popover.right .note-popover-arrow {
    top: 50%;
    left: -11px;
    margin-top: -10px;
    border-left-width: 0;
    border-right-color: #999;
    border-right-color: rgba(0, 0, 0, .25)
}

.note-popover.right .note-popover-arrow:after {
    left: 1px;
    margin-top: -10px;
    content: " ";
    border-left-width: 0;
    border-right-color: #fff
}

.note-popover.left .note-popover-arrow {
    top: 50%;
    right: -11px;
    margin-top: -10px;
    border-right-width: 0;
    border-left-color: #999;
    border-left-color: rgba(0, 0, 0, .25)
}

.note-popover.left .note-popover-arrow:after {
    right: 1px;
    margin-top: -10px;
    content: " ";
    border-right-width: 0;
    border-left-color: #fff
}

.note-popover-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 11px solid transparent
}

.note-popover-arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    content: " ";
    border: 10px solid transparent
}

.note-popover-content {
    padding: 3px 8px;
    color: #000;
    text-align: center;
    background-color: #fff;
    min-width: 100px;
    min-height: 30px
}

.note-editor {
    position: relative
}

.note-editor .note-dropzone {
    position: absolute;
    display: none;
    z-index: 100;
    color: #87cefa;
    background-color: #fff;
    opacity: .95
}

.note-editor .note-dropzone .note-dropzone-message {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    font-size: 28px;
    font-weight: 700
}

.note-editor .note-dropzone.hover {
    color: #098ddf
}

.note-editor.dragover .note-dropzone {
    display: table
}

.note-editor .note-editing-area {
    position: relative
}

.note-editor .note-editing-area .note-editable {
    outline: 0
}

.note-editor .note-editing-area .note-editable sup {
    vertical-align: super
}

.note-editor .note-editing-area .note-editable sub {
    vertical-align: sub
}

.note-editor .note-editing-area .note-editable img.note-float-left {
    margin-right: 10px
}

.note-editor .note-editing-area .note-editable img.note-float-right {
    margin-left: 10px
}

.note-editor.note-airframe,
.note-editor.note-frame {
    border: 1px solid rgba(0, 0, 0, .2)
}

.note-editor.note-airframe.codeview .note-editing-area .note-editable,
.note-editor.note-frame.codeview .note-editing-area .note-editable {
    display: none
}

.note-editor.note-airframe.codeview .note-editing-area .note-codable,
.note-editor.note-frame.codeview .note-editing-area .note-codable {
    display: block
}

.note-editor.note-airframe .note-editing-area,
.note-editor.note-frame .note-editing-area {
    overflow: hidden
}

.note-editor.note-airframe .note-editing-area .note-editable,
.note-editor.note-frame .note-editing-area .note-editable {
    padding: 10px;
    overflow: auto;
    word-wrap: break-word
}

.note-editor.note-airframe .note-editing-area .note-editable[contenteditable=false],
.note-editor.note-frame .note-editing-area .note-editable[contenteditable=false] {
    background-color: rgba(128, 128, 128, .11)
}

.note-editor.note-airframe .note-editing-area .note-codable,
.note-editor.note-frame .note-editing-area .note-codable {
    display: none;
    width: 100%;
    padding: 10px;
    border: none;
    box-shadow: none;
    font-family: Menlo, Monaco, monospace, sans-serif;
    font-size: 14px;
    color: #ccc;
    background-color: #222;
    resize: none;
    outline: 0;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 0;
    margin-bottom: 0
}

.note-editor.note-airframe.fullscreen,
.note-editor.note-frame.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100% !important;
    z-index: 1050
}

.note-editor.note-airframe.fullscreen .note-resizebar,
.note-editor.note-frame.fullscreen .note-resizebar {
    display: none
}

.note-editor.note-airframe .note-status-output,
.note-editor.note-frame .note-status-output {
    display: block;
    width: 100%;
    font-size: 14px;
    line-height: 1.42857143;
    height: 20px;
    margin-bottom: 0;
    color: #000;
    border: 0;
    border-top: 1px solid #e2e2e2
}

.note-editor.note-airframe .note-status-output:empty,
.note-editor.note-frame .note-status-output:empty {
    height: 0;
    border-top: 0 solid transparent
}

.note-editor.note-airframe .note-status-output .pull-right,
.note-editor.note-frame .note-status-output .pull-right {
    float: right !important
}

.note-editor.note-airframe .note-status-output .text-muted,
.note-editor.note-frame .note-status-output .text-muted {
    color: #777
}

.note-editor.note-airframe .note-status-output .text-primary,
.note-editor.note-frame .note-status-output .text-primary {
    color: #286090
}

.note-editor.note-airframe .note-status-output .text-success,
.note-editor.note-frame .note-status-output .text-success {
    color: #3c763d
}

.note-editor.note-airframe .note-status-output .text-info,
.note-editor.note-frame .note-status-output .text-info {
    color: #31708f
}

.note-editor.note-airframe .note-status-output .text-warning,
.note-editor.note-frame .note-status-output .text-warning {
    color: #8a6d3b
}

.note-editor.note-airframe .note-status-output .text-danger,
.note-editor.note-frame .note-status-output .text-danger {
    color: #a94442
}

.note-editor.note-airframe .note-status-output .alert,
.note-editor.note-frame .note-status-output .alert {
    margin: -7px 0 0;
    padding: 7px 10px 2px;
    border-radius: 0;
    color: #000;
    background-color: #f5f5f5
}

.note-editor.note-airframe .note-status-output .alert .note-icon,
.note-editor.note-frame .note-status-output .alert .note-icon {
    margin-right: 5px
}

.note-editor.note-airframe .note-status-output .alert-success,
.note-editor.note-frame .note-status-output .alert-success {
    color: #3c763d !important;
    background-color: #dff0d8 !important
}

.note-editor.note-airframe .note-status-output .alert-info,
.note-editor.note-frame .note-status-output .alert-info {
    color: #31708f !important;
    background-color: #d9edf7 !important
}

.note-editor.note-airframe .note-status-output .alert-warning,
.note-editor.note-frame .note-status-output .alert-warning {
    color: #8a6d3b !important;
    background-color: #fcf8e3 !important
}

.note-editor.note-airframe .note-status-output .alert-danger,
.note-editor.note-frame .note-status-output .alert-danger {
    color: #a94442 !important;
    background-color: #f2dede !important
}

.note-editor.note-airframe .note-statusbar,
.note-editor.note-frame .note-statusbar {
    background-color: rgba(128, 128, 128, .11);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top: 1px solid rgba(0, 0, 0, .2)
}

.note-editor.note-airframe .note-statusbar .note-resizebar,
.note-editor.note-frame .note-statusbar .note-resizebar {
    padding-top: 1px;
    height: 9px;
    width: 100%;
    cursor: ns-resize
}

.note-editor.note-airframe .note-statusbar .note-resizebar .note-icon-bar,
.note-editor.note-frame .note-statusbar .note-resizebar .note-icon-bar {
    width: 20px;
    margin: 1px auto;
    border-top: 1px solid rgba(0, 0, 0, .2)
}

.note-editor.note-airframe .note-statusbar.locked .note-resizebar,
.note-editor.note-frame .note-statusbar.locked .note-resizebar {
    cursor: default
}

.note-editor.note-airframe .note-statusbar.locked .note-resizebar .note-icon-bar,
.note-editor.note-frame .note-statusbar.locked .note-resizebar .note-icon-bar {
    display: none
}

.note-editor.note-airframe .note-placeholder,
.note-editor.note-frame .note-placeholder {
    padding: 10px
}

.note-editor.note-airframe {
    border: 0
}

.note-editor.note-airframe .note-editing-area .note-editable {
    padding: 0
}

.note-popover.popover {
    display: none;
    max-width: none
}

.note-popover.popover .popover-content a {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle
}

.note-popover.popover .arrow {
    left: 20px !important
}

.note-toolbar {
    position: relative
}

.note-editor .note-toolbar,
.note-popover .popover-content {
    margin: 0;
    padding: 0 0 5px 5px
}

.note-editor .note-toolbar>.note-btn-group,
.note-popover .popover-content>.note-btn-group {
    margin-top: 5px;
    margin-left: 0;
    margin-right: 5px
}

.note-editor .note-toolbar .note-btn-group .note-table,
.note-popover .popover-content .note-btn-group .note-table {
    min-width: 0;
    padding: 5px
}

.note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker,
.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker {
    font-size: 18px
}

.note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher,
.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher {
    position: absolute !important;
    z-index: 3;
    width: 10em;
    height: 10em;
    cursor: pointer
}

.note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted,
.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted {
    position: relative !important;
    z-index: 1;
    width: 5em;
    height: 5em;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC) repeat
}

.note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted,
.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted {
    position: absolute !important;
    z-index: 2;
    width: 1em;
    height: 1em;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC) repeat
}

.note-editor .note-toolbar .note-style .dropdown-style blockquote,
.note-editor .note-toolbar .note-style .dropdown-style pre,
.note-popover .popover-content .note-style .dropdown-style blockquote,
.note-popover .popover-content .note-style .dropdown-style pre {
    margin: 0;
    padding: 5px 10px
}

.note-editor .note-toolbar .note-style .dropdown-style h1,
.note-editor .note-toolbar .note-style .dropdown-style h2,
.note-editor .note-toolbar .note-style .dropdown-style h3,
.note-editor .note-toolbar .note-style .dropdown-style h4,
.note-editor .note-toolbar .note-style .dropdown-style h5,
.note-editor .note-toolbar .note-style .dropdown-style h6,
.note-editor .note-toolbar .note-style .dropdown-style p,
.note-popover .popover-content .note-style .dropdown-style h1,
.note-popover .popover-content .note-style .dropdown-style h2,
.note-popover .popover-content .note-style .dropdown-style h3,
.note-popover .popover-content .note-style .dropdown-style h4,
.note-popover .popover-content .note-style .dropdown-style h5,
.note-popover .popover-content .note-style .dropdown-style h6,
.note-popover .popover-content .note-style .dropdown-style p {
    margin: 0;
    padding: 0
}

.note-editor .note-toolbar .note-color-all .note-dropdown-menu,
.note-popover .popover-content .note-color-all .note-dropdown-menu {
    min-width: 337px
}

.note-editor .note-toolbar .note-color .dropdown-toggle,
.note-popover .popover-content .note-color .dropdown-toggle {
    width: 20px;
    padding-left: 5px
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette {
    display: inline-block;
    margin: 0;
    width: 160px
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette:first-child,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette:first-child {
    margin: 0 5px
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-palette-title,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-palette-title {
    font-size: 12px;
    margin: 2px 7px;
    text-align: center;
    border-bottom: 1px solid #eee
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset,
.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select {
    font-size: 11px;
    margin: 3px;
    padding: 0 3px;
    cursor: pointer;
    width: 100%;
    border-radius: 5px
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset:hover,
.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select:hover,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset:hover,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select:hover {
    background: #eee
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-row,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-row {
    height: 20px
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select-btn,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select-btn {
    display: none
}

.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn,
.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn {
    border: 1px solid #eee
}

.note-editor .note-toolbar .note-para .note-dropdown-menu,
.note-popover .popover-content .note-para .note-dropdown-menu {
    min-width: 228px;
    padding: 5px
}

.note-editor .note-toolbar .note-para .note-dropdown-menu>div+div,
.note-popover .popover-content .note-para .note-dropdown-menu>div+div {
    margin-left: 5px
}

.note-editor .note-toolbar .note-dropdown-menu,
.note-popover .popover-content .note-dropdown-menu {
    min-width: 160px
}

.note-editor .note-toolbar .note-dropdown-menu.right,
.note-popover .popover-content .note-dropdown-menu.right {
    right: 0;
    left: auto
}

.note-editor .note-toolbar .note-dropdown-menu.right:before,
.note-popover .popover-content .note-dropdown-menu.right:before {
    right: 9px;
    left: auto !important
}

.note-editor .note-toolbar .note-dropdown-menu.right:after,
.note-popover .popover-content .note-dropdown-menu.right:after {
    right: 10px;
    left: auto !important
}

.note-editor .note-toolbar .note-dropdown-menu.note-check a i,
.note-popover .popover-content .note-dropdown-menu.note-check a i {
    color: #00bfff;
    visibility: hidden
}

.note-editor .note-toolbar .note-dropdown-menu.note-check a.checked i,
.note-popover .popover-content .note-dropdown-menu.note-check a.checked i {
    visibility: visible
}

.note-editor .note-toolbar .note-fontsize-10,
.note-popover .popover-content .note-fontsize-10 {
    font-size: 10px
}

.note-editor .note-toolbar .note-color-palette,
.note-popover .popover-content .note-color-palette {
    line-height: 1
}

.note-editor .note-toolbar .note-color-palette div .note-color-btn,
.note-popover .popover-content .note-color-palette div .note-color-btn {
    width: 20px;
    height: 20px;
    padding: 0;
    margin: 0;
    border: 0;
    border-radius: 0
}

.note-editor .note-toolbar .note-color-palette div .note-color-btn:hover,
.note-popover .popover-content .note-color-palette div .note-color-btn:hover {
    transform: scale(1.2);
    transition: all .2s
}

.note-modal .modal-dialog {
    outline: 0;
    border-radius: 5px;
    box-shadow: 0 3px 9px rgba(0, 0, 0, .5)
}

.note-modal .form-group {
    margin-left: 0;
    margin-right: 0
}

.note-modal .note-modal-form {
    margin: 0
}

.note-modal .note-image-dialog .note-dropzone {
    min-height: 100px;
    font-size: 30px;
    line-height: 4;
    color: #d3d3d3;
    text-align: center;
    border: 4px dashed #d3d3d3;
    margin-bottom: 10px
}

.note-placeholder {
    position: absolute;
    display: none;
    color: grey
}

.note-handle .note-control-selection {
    position: absolute;
    display: none;
    border: 1px solid #000
}

.note-handle .note-control-selection>div {
    position: absolute
}

.note-handle .note-control-selection .note-control-selection-bg {
    width: 100%;
    height: 100%;
    background-color: #000;
    -webkit-opacity: .3;
    -khtml-opacity: .3;
    -moz-opacity: .3;
    opacity: .3
}

.note-handle .note-control-selection .note-control-handle,
.note-handle .note-control-selection .note-control-holder,
.note-handle .note-control-selection .note-control-sizing {
    width: 7px;
    height: 7px;
    border: 1px solid #000
}

.note-handle .note-control-selection .note-control-sizing {
    background-color: #000
}

.note-handle .note-control-selection .note-control-nw {
    top: -5px;
    left: -5px;
    border-right: none;
    border-bottom: none
}

.note-handle .note-control-selection .note-control-ne {
    top: -5px;
    right: -5px;
    border-bottom: none;
    border-left: none
}

.note-handle .note-control-selection .note-control-sw {
    bottom: -5px;
    left: -5px;
    border-top: none;
    border-right: none
}

.note-handle .note-control-selection .note-control-se {
    right: -5px;
    bottom: -5px;
    cursor: se-resize
}

.note-handle .note-control-selection .note-control-se.note-control-holder {
    cursor: default;
    border-top: none;
    border-left: none
}

.note-handle .note-control-selection .note-control-selection-info {
    right: 0;
    bottom: 0;
    padding: 5px;
    margin: 5px;
    color: #fff;
    background-color: #000;
    font-size: 12px;
    border-radius: 5px;
    -webkit-opacity: .7;
    -khtml-opacity: .7;
    -moz-opacity: .7;
    opacity: .7
}

.note-hint-popover {
    min-width: 100px;
    padding: 2px
}

.note-hint-popover .popover-content {
    padding: 3px;
    max-height: 150px;
    overflow: auto
}

.note-hint-popover .popover-content .note-hint-group .note-hint-item {
    display: block !important;
    padding: 3px
}

.note-hint-popover .popover-content .note-hint-group .note-hint-item.active,
.note-hint-popover .popover-content .note-hint-group .note-hint-item:hover {
    display: block;
    clear: both;
    font-weight: 400;
    line-height: 1.4;
    color: #fff;
    white-space: nowrap;
    text-decoration: none;
    background-color: #428bca;
    outline: 0;
    cursor: pointer
}

.note-editor .note-editing-area .note-editable table {
    width: 100%;
    border-collapse: collapse
}

.note-editor .note-editing-area .note-editable table td,
.note-editor .note-editing-area .note-editable table th {
    border: 1px solid #ececec;
    padding: 5px 3px
}

.note-editor .note-editing-area .note-editable a {
    background-color: inherit;
    text-decoration: inherit;
    font-family: inherit;
    font-weight: inherit;
    color: #337ab7
}

.note-editor .note-editing-area .note-editable a:focus,
.note-editor .note-editing-area .note-editable a:hover {
    color: #23527c;
    text-decoration: underline;
    outline: 0
}

.note-editor .note-editing-area .note-editable figure {
    margin: 0
}

.note-modal .note-modal-body label {
    margin-bottom: 2px;
    padding: 2px 5px;
    display: inline-block
}

.note-modal .note-modal-body .help-list-item:hover {
    background-color: #e0e0e0
}

@-moz-document url-prefix() {
    .note-modal .note-image-input {
        height: auto
    }
}

.help-list-item label {
    margin-bottom: 5px;
    display: inline-block
}

.owl-carousel {
    -webkit-tap-highlight-color: transparent;
    position: relative;
    display: none;
    width: 100%;
    z-index: 1
}

.owl-carousel .owl-item {
    -webkit-tap-highlight-color: transparent;
    position: relative
}

.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    touch-action: manipulation;
    -moz-backface-visibility: hidden
}

.owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0
}

.owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0, 0, 0)
}

.owl-carousel .owl-item,
.owl-carousel .owl-wrapper {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0)
}

.owl-carousel .owl-item {
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-touch-callout: none
}

.owl-carousel .owl-item img {
    display: block;
    width: 100%
}

.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled {
    display: none
}

.no-js .owl-carousel {
    display: block
}

.owl-carousel.owl-loaded {
    display: block
}

.owl-carousel .owl-dot {
    cursor: pointer;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel .owl-nav .owl-next,
.owl-carousel .owl-nav .owl-prev {
    cursor: pointer;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev {
    background: 0 0;
    color: inherit;
    border: none;
    padding: 0 !important
}

.owl-carousel button.owl-dot {
    background: 0 0;
    color: inherit;
    border: none;
    padding: 0 !important
}

.owl-carousel.owl-loading {
    opacity: 0;
    display: block
}

.owl-carousel.owl-hidden {
    opacity: 0
}

.owl-carousel.owl-refresh .owl-item {
    visibility: hidden
}

.owl-carousel.owl-drag .owl-item {
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel.owl-grab {
    cursor: move;
    cursor: grab
}

.owl-carousel.owl-rtl {
    direction: rtl
}

.owl-carousel.owl-rtl .owl-item {
    float: right
}

.owl-carousel .animated {
    animation-duration: 1s;
    animation-fill-mode: both
}

.owl-carousel .owl-animated-in {
    z-index: 0
}

.owl-carousel .owl-animated-out {
    z-index: 1
}

.owl-carousel .fadeOut {
    animation-name: fadeOut
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

.owl-height {
    transition: height .5s ease-in-out
}

.owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    transition: opacity .4s ease
}

.owl-carousel .owl-item .owl-lazy:not([src]),
.owl-carousel .owl-item .owl-lazy[src^=""] {
    max-height: 0
}

.owl-carousel .owl-item img.owl-lazy {
    transform-style: preserve-3d
}

.owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000
}

.owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    /* background: url(owl.video.play.png) no-repeat; */
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    transition: transform .1s ease
}

.owl-carousel .owl-video-play-icon:hover {
    -ms-transform: scale(1.3, 1.3);
    transform: scale(1.3, 1.3)
}

.owl-carousel .owl-video-playing .owl-video-play-icon,
.owl-carousel .owl-video-playing .owl-video-tn {
    display: none
}

.owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    transition: opacity .4s ease
}

.owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%
}

.owl-theme .owl-dots {
    text-align: center;
    -webkit-tap-highlight-color: transparent
}

.owl-theme .owl-nav {
    text-align: center;
    -webkit-tap-highlight-color: transparent;
    margin-top: 10px
}

.owl-theme .owl-nav [class*=owl-] {
    color: #fff;
    font-size: 18px;
    margin: 5px;
    padding: 4px 7px;
    background: #d6d6d6;
    display: inline-block;
    cursor: pointer;
    border-radius: 3px
}

.owl-theme .owl-nav [class*=owl-]:hover {
    background: #869791;
    color: #fff;
    text-decoration: none
}

.owl-theme .owl-nav .disabled {
    opacity: .5;
    cursor: default
}

.owl-theme .owl-nav.disabled+.owl-dots {
    margin-top: 10px
}

.owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom: 1
}

.owl-theme .owl-dots .owl-dot span {
    width: 10px;
    height: 10px;
    margin: 5px 7px;
    background: #d6d6d6;
    display: block;
    -webkit-backface-visibility: visible;
    transition: opacity .2s ease;
    border-radius: 30px
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
    background: #869791
}

.owl-img-responsive .owl-stage,
.owl-img-responsive .owl-wrapper {
    display: flex !important
}

.owl-img-responsive .owl-stage .item,
.owl-img-responsive .owl-wrapper .item {
    height: 100%
}

.owl-img-responsive .owl-item img.img-fluid {
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-width: initial
}

.mfp-bg {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1042;
    overflow: hidden;
    position: fixed;
    background: #0b0b0b;
    opacity: .8
}

.mfp-wrap {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1043;
    position: fixed;
    outline: 0 !important;
    -webkit-backface-visibility: hidden
}

.mfp-container {
    text-align: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    padding: 0 8px;
    box-sizing: border-box
}

.mfp-container:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.mfp-align-top .mfp-container:before {
    display: none
}

.mfp-content {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin: 0 auto;
    text-align: left;
    z-index: 1045
}

.mfp-ajax-holder .mfp-content,
.mfp-inline-holder .mfp-content {
    width: 100%;
    cursor: auto
}

.mfp-ajax-cur {
    cursor: progress
}

.mfp-zoom-out-cur {
    cursor: -moz-zoom-out;
    cursor: -webkit-zoom-out;
    cursor: zoom-out
}

.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
    cursor: -moz-zoom-out;
    cursor: -webkit-zoom-out;
    cursor: zoom-out
}

.mfp-zoom {
    cursor: pointer;
    cursor: -webkit-zoom-in;
    cursor: -moz-zoom-in;
    cursor: zoom-in
}

.mfp-auto-cursor .mfp-content {
    cursor: auto
}

.mfp-arrow,
.mfp-close,
.mfp-counter,
.mfp-preloader {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.mfp-loading.mfp-figure {
    display: none
}

.mfp-hide {
    display: none !important
}

.mfp-preloader {
    color: #ccc;
    position: absolute;
    top: 50%;
    width: auto;
    text-align: center;
    margin-top: -.8em;
    left: 8px;
    right: 8px;
    z-index: 1044
}

.mfp-preloader a {
    color: #ccc
}

.mfp-preloader a:hover {
    color: #fff
}

.mfp-s-error .mfp-content,
.mfp-s-ready .mfp-preloader {
    display: none
}

button.mfp-arrow,
button.mfp-close {
    overflow: visible;
    cursor: pointer;
    background: 0 0;
    border: 0;
    -webkit-appearance: none;
    display: block;
    outline: 0;
    padding: 0;
    z-index: 1046;
    box-shadow: none;
    touch-action: manipulation
}

button::-moz-focus-inner {
    padding: 0;
    border: 0
}

.mfp-close {
    width: 44px;
    height: 44px;
    line-height: 44px;
    position: absolute;
    right: 0;
    top: 0;
    text-decoration: none;
    text-align: center;
    opacity: .65;
    padding: 0 0 18px 10px;
    color: #fff;
    font-style: normal;
    font-size: 28px;
    font-family: Arial, Baskerville, monospace
}

.mfp-close:focus,
.mfp-close:hover {
    opacity: 1
}

.mfp-close:active {
    top: 1px
}

.mfp-close-btn-in .mfp-close {
    color: #333
}

.mfp-iframe-holder .mfp-close,
.mfp-image-holder .mfp-close {
    color: #fff;
    right: -6px;
    text-align: right;
    padding-right: 6px;
    width: 100%
}

.mfp-counter {
    position: absolute;
    top: 0;
    right: 0;
    color: #ccc;
    font-size: 12px;
    line-height: 18px;
    white-space: nowrap
}

.mfp-arrow {
    position: absolute;
    opacity: .65;
    margin: 0;
    top: 50%;
    margin-top: -55px;
    padding: 0;
    width: 90px;
    height: 110px;
    -webkit-tap-highlight-color: transparent
}

.mfp-arrow:active {
    margin-top: -54px
}

.mfp-arrow:focus,
.mfp-arrow:hover {
    opacity: 1
}

.mfp-arrow:before {
    content: '';
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    left: 0;
    top: 0;
    margin-top: 35px;
    margin-left: 35px;
    border: medium inset transparent
}

.mfp-arrow:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    left: 0;
    top: 0;
    margin-top: 35px;
    margin-left: 35px;
    border: medium inset transparent;
    border-top-width: 13px;
    border-bottom-width: 13px;
    top: 8px
}

.mfp-arrow:before {
    border-top-width: 21px;
    border-bottom-width: 21px;
    opacity: .7
}

.mfp-arrow-left {
    left: 0
}

.mfp-arrow-left:after {
    border-right: 17px solid #fff;
    margin-left: 31px
}

.mfp-arrow-left:before {
    margin-left: 25px;
    border-right: 27px solid #3f3f3f
}

.mfp-arrow-right {
    right: 0
}

.mfp-arrow-right:after {
    border-left: 17px solid #fff;
    margin-left: 39px
}

.mfp-arrow-right:before {
    border-left: 27px solid #3f3f3f
}

.mfp-iframe-holder {
    padding-top: 40px;
    padding-bottom: 40px
}

.mfp-iframe-holder .mfp-content {
    line-height: 0;
    width: 100%;
    max-width: 900px
}

.mfp-iframe-holder .mfp-close {
    top: -40px
}

.mfp-iframe-scaler {
    width: 100%;
    height: 0;
    overflow: hidden;
    padding-top: 56.25%
}

.mfp-iframe-scaler iframe {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: 0 0 8px rgba(0, 0, 0, .6);
    background: #000
}

img.mfp-img {
    width: auto;
    max-width: 100%;
    height: auto;
    display: block;
    line-height: 0;
    box-sizing: border-box;
    padding: 40px 0 40px;
    margin: 0 auto
}

.mfp-figure {
    line-height: 0
}

.mfp-figure:after {
    content: '';
    position: absolute;
    left: 0;
    top: 40px;
    bottom: 40px;
    display: block;
    right: 0;
    width: auto;
    height: auto;
    z-index: -1;
    box-shadow: 0 0 8px rgba(0, 0, 0, .6);
    background: #444
}

.mfp-figure small {
    color: #bdbdbd;
    display: block;
    font-size: 12px;
    line-height: 14px
}

.mfp-figure figure {
    margin: 0
}

.mfp-bottom-bar {
    margin-top: -36px;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    cursor: auto
}

.mfp-title {
    text-align: left;
    line-height: 18px;
    color: #f3f3f3;
    word-wrap: break-word;
    padding-right: 36px
}

.mfp-image-holder .mfp-content {
    max-width: 100%
}

.mfp-gallery .mfp-image-holder .mfp-figure {
    cursor: pointer
}

@media screen and (max-width:800px) and (orientation:landscape),
screen and (max-height:300px) {
    .mfp-img-mobile .mfp-image-holder {
        padding-left: 0;
        padding-right: 0
    }

    .mfp-img-mobile img.mfp-img {
        padding: 0
    }

    .mfp-img-mobile .mfp-figure:after {
        top: 0;
        bottom: 0
    }

    .mfp-img-mobile .mfp-figure small {
        display: inline;
        margin-left: 5px
    }

    .mfp-img-mobile .mfp-bottom-bar {
        background: rgba(0, 0, 0, .6);
        bottom: 0;
        margin: 0;
        top: auto;
        padding: 3px 5px;
        position: fixed;
        box-sizing: border-box
    }

    .mfp-img-mobile .mfp-bottom-bar:empty {
        padding: 0
    }

    .mfp-img-mobile .mfp-counter {
        right: 5px;
        top: 3px
    }

    .mfp-img-mobile .mfp-close {
        top: 0;
        right: 0;
        width: 35px;
        height: 35px;
        line-height: 35px;
        background: rgba(0, 0, 0, .6);
        position: fixed;
        text-align: center;
        padding: 0
    }
}

@media all and (max-width:900px) {
    .mfp-arrow {
        -webkit-transform: scale(.75);
        transform: scale(.75)
    }

    .mfp-arrow-left {
        -webkit-transform-origin: 0;
        transform-origin: 0
    }

    .mfp-arrow-right {
        -webkit-transform-origin: 100%;
        transform-origin: 100%
    }

    .mfp-container {
        padding-left: 6px;
        padding-right: 6px
    }
}

svg {
    -ms-touch-action: none;
    touch-action: none
}

.jvm-zoomin,
.jvm-zoomout,
image,
text {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.jvm-container {
    -ms-touch-action: none;
    touch-action: none;
    position: relative;
    overflow: hidden;
    height: 100%;
    width: 100%
}

.jvm-tooltip {
    border-radius: 3px;
    background-color: #5c5cff;
    font-family: sans-serif, Verdana;
    font-size: smaller;
    box-shadow: 1px 2px 12px rgba(0, 0, 0, .2);
    padding: 3px 5px;
    white-space: nowrap;
    position: absolute;
    display: none;
    color: #fff
}

.jvm-tooltip.active {
    display: block
}

.jvm-zoom-btn {
    border-radius: 3px;
    background-color: #292929;
    padding: 3px;
    box-sizing: border-box;
    position: absolute;
    line-height: 10px;
    cursor: pointer;
    color: #fff;
    height: 15px;
    width: 15px;
    left: 10px
}

.jvm-zoom-btn.jvm-zoomout {
    top: 30px
}

.jvm-zoom-btn.jvm-zoomin {
    top: 10px
}

.jvm-series-container {
    right: 15px;
    position: absolute
}

.jvm-series-container.jvm-series-h {
    bottom: 15px
}

.jvm-series-container.jvm-series-v {
    top: 15px
}

.jvm-series-container .jvm-legend {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    margin-left: .75rem;
    border-radius: .25rem;
    border-color: #e5e7eb;
    padding: .6rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    float: left
}

.jvm-series-container .jvm-legend .jvm-legend-title {
    line-height: 1;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: .5rem;
    margin-bottom: .575rem;
    text-align: left
}

.jvm-series-container .jvm-legend .jvm-legend-inner {
    overflow: hidden
}

.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {
    overflow: hidden;
    min-width: 40px
}

.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick:not(:first-child) {
    margin-top: .575rem
}

.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-sample {
    border-radius: 4px;
    margin-right: .65rem;
    height: 16px;
    width: 16px;
    float: left
}

.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-text {
    font-size: 12px;
    text-align: center;
    float: left
}

.jvm-line[animation=true] {
    -webkit-animation: jvm-line-animation 10s linear forwards infinite;
    animation: jvm-line-animation 10s linear forwards infinite
}

@-webkit-keyframes jvm-line-animation {
    from {
        stroke-dashoffset: 250
    }
}

@keyframes jvm-line-animation {
    from {
        stroke-dashoffset: 250
    }
}