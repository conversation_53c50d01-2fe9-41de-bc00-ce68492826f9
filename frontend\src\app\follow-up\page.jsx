"use client";
import AssesserFollowUpPage from "./AssesserFollowUp";
import DeliveryFollowUpPage from "./DeliveryFollowUp";
import InstallerFollowUpPage from "./InstallerFollowUp";
import { useUser } from "../provider/UserContext";
import { Flex, Spinner } from "@chakra-ui/react";

export default function Followup() {
  const { userRole } = useUser();

  return (
    userRole === "Assessor" ? (
      <AssesserFollowUpPage />
    ) : userRole === "Delivery" ? (
      <DeliveryFollowUpPage />
    ) : userRole === "Installer" ? (
      <InstallerFollowUpPage />
    ) : (
      <Flex h="100vh" align="center" justify="center">
        <Spinner size="xl" color="blue.500" />
      </Flex>
    )
  )
}
