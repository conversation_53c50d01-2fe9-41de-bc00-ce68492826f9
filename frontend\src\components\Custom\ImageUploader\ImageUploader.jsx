import React, { useEffect, useRef, useState } from 'react';
import { Box, Button, Image as ChakraImage } from '@chakra-ui/react';
import { HiUpload } from "react-icons/hi";
import { BiTrash } from "react-icons/bi";
import { EditIcon } from "@chakra-ui/icons";

const ImageUploader = ({ onChange, disabled = false, imagePreview = null }) => {
  const [preview, setPreview] = useState(null);
  const fileInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
        onChange?.(file, reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemove = () => {
    setPreview(null);
    onChange?.(null, null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  useEffect(() => {
    setPreview(imagePreview);
  }, [imagePreview])

  return (
    <Box width="100%" height="100%">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleImageChange}
        accept="image/*"
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {!preview ? (
        <Button
          width="100%"
          colorScheme="blue"
          onClick={handleUploadClick}
          disabled={disabled}
        >
          <HiUpload size={20} style={{ marginRight: "5px" }} />
          Upload Image
        </Button>
      ) : (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
          gap="10px"
        >
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            gap="5px"
          >
            <Button
              colorScheme="blue"
              onClick={handleUploadClick}
              disabled={disabled}
              size="sm"
            >
              <EditIcon />
            </Button>
            <ChakraImage
              src={preview}
              alt="Preview"
              maxHeight="150px"
              borderRadius="10%"
              objectFit="contain"
            />
            <Button
              colorScheme="blue"
              onClick={handleRemove}
              disabled={disabled}
              size="sm"
            >
              <BiTrash />
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default ImageUploader;
