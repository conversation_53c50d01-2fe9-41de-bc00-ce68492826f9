"use client";
import { useState, useCallback, lazy, Suspense, memo } from "react";
import PropTypes from "prop-types";
import { Input, But<PERSON>, Box, Spinner } from "@chakra-ui/react";
import "./TableComboBox.css";

const Modal = lazy(() => import("../Modal/Modal"));

const ChevronDownIcon = () => (
  <svg
    fill="#ededed"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    stroke="#ededed"
    aria-hidden="true"
  >
    <polygon points="12 20.1 0.1 8.2 2.9 5.3 12 14.4 21.1 5.3 23.9 8.2 12 20.1"></polygon>
  </svg>
);

function TableComboBox({
  inputWidth = "70px",
  buttonWidth = "20px",
  inputHeight = "35px",
  buttonHeight = "35px",
  style = {},
  styleInput = {},
  styleButton = {},
  modalData = [],
  modalHeaders = [],
  onChange = () => {},
  rowClickHandler = () => {},
  target = false,
  name = "default",
  value = "",
  placeholder = "",
  ariaLabel = "Combo Box Input",
  rowIndex,
  colIndex,
  isDisabled = false,
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = useCallback(() => setIsModalOpen(true), []);
  const closeModal = useCallback(() => setIsModalOpen(false), []);

  const handleInputChange = useCallback(
    (e) => {
      onChange(target ? e : e.target.value);
    },
    [onChange, target]
  );

  const handleRowClick = useCallback(
    (data) => {
      rowClickHandler(data, rowIndex, colIndex);
      closeModal();
    },
    [onChange, closeModal]
  );

  return (
    <Box
      position="relative"
      width={inputWidth}
      {...style}
      role="combobox"
      aria-expanded={isModalOpen}
    >
      <Input
        pr={buttonWidth}
        height={inputHeight}
        width="100%"
        fontSize="sm"
        {...styleInput}
        aria-label={ariaLabel}
        onChange={handleInputChange}
        name={name}
        value={value}
        autoComplete="off"
        placeholder={placeholder}
        readOnly
        disabled={isDisabled}
      />

      <Button
        colorScheme="blue"
        className="AanzaComboBox"
        onClick={openModal}
        height={buttonHeight}
        width={buttonWidth}
        minWidth="auto"
        p="3px"
        position="absolute"
        right="0"
        top="0"
        aria-label="Open Combo Box Modal"
        {...styleButton}
        isDisabled={isDisabled}
      >
        <ChevronDownIcon />
      </Button>

      {isModalOpen && (
        <Suspense fallback={<></>}>
          <Modal
            onClose={closeModal}
            tableData={modalData}
            tableHeaders={modalHeaders}
            handleRowClick={handleRowClick}
          />
        </Suspense>
      )}
    </Box>
  );
}

TableComboBox.propTypes = {
  inputWidths: PropTypes.arrayOf(PropTypes.string),
  buttonWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  inputHeight: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  buttonHeight: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  style: PropTypes.object,
  styleInput: PropTypes.object,
  styleButton: PropTypes.object,
  tableData: PropTypes.array,
  tableHeaders: PropTypes.array,
  onChange: PropTypes.func,
  nameFields: PropTypes.arrayOf(PropTypes.string),
  values: PropTypes.object,
  ariaLabel: PropTypes.string,
  placeholders: PropTypes.arrayOf(PropTypes.string),
  keys: PropTypes.arrayOf(PropTypes.string),
};

export default memo(TableComboBox);
