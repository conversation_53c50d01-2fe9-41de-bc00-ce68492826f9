import { io } from 'socket.io-client';

let socket = null

const createSocket = () => {
    // Only create socket on client side and if enabled via environment variable
    if (typeof window === 'undefined') {
        return null;
    }

    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5000';
    console.log('Creating socket connection to:', backendUrl);
    console.log('Environment NEXT_PUBLIC_BACKEND_URL:', process.env.NEXT_PUBLIC_BACKEND_URL);
    console.log('Environment NEXT_PUBLIC_ENABLE_SOCKET:', process.env.NEXT_PUBLIC_ENABLE_SOCKET);

    return io(backendUrl, {
        autoConnect: true,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        maxReconnectionAttempts: 5,
        timeout: 20000,
        transports: ['polling', 'websocket'], // Try polling first, then websocket
        forceNew: true,
        upgrade: true,
        rememberUpgrade: false
    });
};

export const getSocket = () => {
    // Only create socket on client side
    if (typeof window === 'undefined') {
        return null;
    }

    if (!socket) {
        socket = createSocket();

        if (socket) {
            socket.on('connect', () => {
                console.log('Socket.IO connected:', socket.id);
            });

            socket.on('disconnect', (reason) => {
                console.log('Socket.IO disconnected:', reason);
            });

            socket.on('connect_error', (error) => {
                console.error('Socket.IO connection error:', error);
                console.error('Error details:', error.message, error.type, error.description);
            });

            socket.on('reconnect', (attemptNumber) => {
                console.log('Socket.IO reconnected after', attemptNumber, 'attempts');
            });

            socket.on('reconnect_error', (error) => {
                console.error('Socket.IO reconnection error:', error);
            });
        }
    }
    return socket;
};

export const reconnectSocket = () => {
    if (socket) {
        socket.disconnect();
    }
    socket = null;
    return getSocket(); // Use getSocket to properly create and set up the socket
};

export const disconnectSocket = () => {
    if (socket) {
        socket.disconnect();
        socket = null;
    }
};
