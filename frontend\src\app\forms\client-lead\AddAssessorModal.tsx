import {
  Modal,
  Modal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
} from "@chakra-ui/react";

interface AddAssessorModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddAssessorModal = ({ isOpen, onClose }: AddAssessorModalProps) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Add your client submission logic here
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader style={{ background: "#071533", color: "white" }}>Add New Assessor</ModalHeader>
        <ModalCloseButton style={{ color: "white" }} />
        <form onSubmit={handleSubmit}>
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>Assessor Name</FormLabel>
                <Input placeholder="Enter client name" />
              </FormControl>
              <FormControl isRequired>
                <FormLabel>Email</FormLabel>
                <Input type="email" placeholder="Enter email" />
              </FormControl>
              <FormControl>
                <FormLabel>Phone</FormLabel>
                <Input type="tel" placeholder="Enter phone number" />
              </FormControl>
              <FormControl>
                <FormLabel>Location</FormLabel>
                <Input type="text" placeholder="Enter Location" />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} type="submit">
              Save
            </Button>
            <Button onClick={onClose} colorScheme="red">Cancel</Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default AddAssessorModal;
