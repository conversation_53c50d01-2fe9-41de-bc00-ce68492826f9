.main-panel {
    position: relative;
    width: calc(100% - 200px);
    float: right;
    transition: all .3s;
}

@media screen and (max-width: 991.5px) {
    .main-panel {
        width: calc(100% - 200px) !important;
        transition: all .5s;
    }
}

@media screen and (max-width: 767px) {
    .main-panel {
        width: 100% !important;
        transition: all .5s;
    }
}

/* Calendar View Styles */
.rbc-calendar {
    font-family: inherit;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
}

.rbc-toolbar {
    padding: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.rbc-toolbar button {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    color: #333;
    border-radius: 4px;
    padding: 5px 10px;
}

.rbc-toolbar button.rbc-active {
    background-color: #3275bb;
    color: white;
    border-color: #3275bb;
}

.rbc-event {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
}

.rbc-today {
    background-color: rgba(50, 117, 187, 0.1);
}

.rbc-header {
    padding: 8px 0;
    font-weight: 600;
    border-bottom: 1px solid #eee;
}

.rbc-day-bg+.rbc-day-bg {
    border-left: 1px solid #f0f0f0;
}

.rbc-month-view {
    border: 1px solid #eee;
    border-radius: 8px;
}

/* Day View Styles */
.rbc-time-view {
    border: 1px solid #eee;
    border-radius: 8px;
}

.rbc-time-header {
    border-bottom: 1px solid #eee;
}

.rbc-time-header-content {
    border-left: 1px solid #eee;
}

.rbc-time-content {
    border-top: 1px solid #eee;
}

.rbc-time-slot {
    border-top: 1px solid #f0f0f0;
}

.rbc-day-slot .rbc-time-slot {
    border-top: 1px solid #f0f0f0;
}

.rbc-timeslot-group {
    border-bottom: 1px solid #f0f0f0;
}

.rbc-time-gutter {
    background-color: #f8f9fa;
}

/* Week View Styles */
.rbc-time-header-cell {
    padding: 8px 0;
}

.rbc-header+.rbc-header {
    border-left: 1px solid #f0f0f0;
}

/* Agenda View Styles */
.rbc-agenda-view {
    border: 1px solid #eee;
    border-radius: 8px;
}

.rbc-agenda-view table {
    border: none;
}

.rbc-agenda-view table thead {
    background-color: #f8f9fa;
}

.rbc-agenda-view table.rbc-agenda-table tbody>tr>td {
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.rbc-agenda-view table.rbc-agenda-table tbody>tr:last-child>td {
    border-bottom: none;
}

.rbc-agenda-time-cell {
    font-weight: 500;
}

.rbc-agenda-date-cell {
    font-weight: 500;
}

/* Popup Styles */
.rbc-overlay {
    background-color: white;
    border: 1px solid #eee;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

.rbc-overlay-header {
    padding: 8px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.rbc-allday-cell {
    display: none !important;
}

.rbc-header {
    border-bottom: none !important;
    padding: 10px 20px !important;
}

.rbc-row.rbc-time-header-cell {
    padding: 0 !important;
}

.rbc-event.rbc-event-continues-earlier {
    height: auto !important;
}

.rbc-month-row {
    overflow: initial !important;
}

.rbc-row-segment .rbc-event .rbc-event-content {
    overflow: auto !important;
    text-overflow: initial !important;
    white-space: normal !important;
    text-transform: capitalize !important;
}

/* Responsive Styles */
@media screen and (max-width: 768px) {
    .rbc-toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .rbc-toolbar>* {
        margin-bottom: 10px;
    }

    .rbc-toolbar>*:last-child {
        margin-bottom: 0;
    }
}