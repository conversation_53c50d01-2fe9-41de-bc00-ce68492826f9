# AANZA TECHNOLOGY ERP Application

## Overview
Welcome to the ERP application developed by **AANZA TECHNOLOGY**. This project aims to streamline and automate various business processes within an organization, enhancing efficiency and productivity.

## Features
- **User Management**: Manage user roles and permissions.
- **Inventory Management**: Track and manage inventory levels, orders, sales, and deliveries.
- **Financial Management**: Handle accounting, invoicing, and financial reporting.
- **Customer Relationship Management (CRM)**: Manage customer interactions and data.
- **Human Resources (HR)**: Manage employee records, payroll, and benefits.
- **Reporting and Analytics**: Generate detailed reports and insights.

## Installation
To get started with the ERP application, follow these steps:

1. **Clone the repository**:
    ```bash
    git clone https://github.com/thehassanabbas/erpaanza.git
    ```
2. **Navigate to the project directory**:
    ```bash
    cd erpaanza
    ```
3. **Node Version**:
    ```bash
    nvm use 20.12.0
    ```
4. **Install dependencies**:
    ```bash
    npm install
    ```

## Usage
To run the application locally, use the following command:
```bash
npm start
```
The application will be available at http://localhost:3000.

Contact
For any inquiries or support, please contact <NAME_EMAIL>.

Thank you for using the AANZA TECHNOLOGY ERP application!
