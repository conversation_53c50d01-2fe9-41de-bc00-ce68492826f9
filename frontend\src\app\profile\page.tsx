"use client";
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Avatar,
  useToast,
  IconButton,
  Flex,
  Text,
  Spinner,
  Switch,
  InputGroup,
  InputRightElement,
  Container,
} from "@chakra-ui/react";
import { useUser } from "@src/app/provider/UserContext";
import React, { useState, useRef, useEffect } from "react";
import { FaCamera, FaEye, FaEyeSlash } from "react-icons/fa";
import axiosInstance from "@src/app/axios";
import useBrowserNotifications from "@src/hooks/useBrowserNotifications";

const Profile = () => {
  const { user, updateUserProfile, loading: userLoading } = useUser();
  const toast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    requestPermission,
    testNotification,
    permission,
    isSupported,
  } = useBrowserNotifications();
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isProfileModified, setIsProfileModified] = useState(false);
  const [isPasswordValid, setIsPasswordValid] = useState(false);

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    EmailAddress: "",
    imageUrl: "",
  });

  const [initialFormData, setInitialFormData] = useState({
    firstName: "",
    lastName: "",
    EmailAddress: "",
    imageUrl: "",
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({
    firstName: "",
    lastName: "",
    EmailAddress: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  useEffect(() => {
    if (user) {
      const [firstName, ...lastNameParts] = (user.userName || "").split(" ");
      const newFormData = {
        firstName: firstName || "",
        lastName: lastNameParts.join(" ") || "",
        EmailAddress: user.EmailAddress || "",
        imageUrl: user.imageUrl || "",
      };
      setFormData(newFormData);
      setInitialFormData(newFormData);
    }
  }, [user]);

  // Check if profile data has been modified
  useEffect(() => {
    const isModified =
      formData.firstName !== initialFormData.firstName ||
      formData.lastName !== initialFormData.lastName ||
      formData.EmailAddress !== initialFormData.EmailAddress ||
      formData.imageUrl !== initialFormData.imageUrl;
    setIsProfileModified(isModified);
  }, [formData, initialFormData]);

  // Check if password form is valid
  useEffect(() => {
    const isValid = validatePasswordForm(false);
    setIsPasswordValid(isValid);
  }, [passwordData]);

  const validateForm = () => {
    const newErrors = { ...errors };
    let isValid = true;

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
      isValid = false;
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
      isValid = false;
    }
    if (!formData.EmailAddress.trim()) {
      newErrors.EmailAddress = "Email is required";
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.EmailAddress)) {
      newErrors.EmailAddress = "Invalid email format";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const validatePasswordForm = (updateErrorState = true) => {
    const newErrors = { ...errors };
    let isValid = true;

    if (!passwordData.currentPassword) {
      newErrors.currentPassword = "Current password is required";
      isValid = false;
    }

    if (!passwordData.newPassword) {
      newErrors.newPassword = "New password is required";
      isValid = false;
    } else if (passwordData.newPassword.length < 6) {
      newErrors.newPassword = "Password must be at least 6 characters";
      isValid = false;
    } else if (!/(?=.*[a-z])/.test(passwordData.newPassword)) {
      newErrors.newPassword = "Password must contain at least one lowercase letter";
      isValid = false;
    } else if (!/(?=.*[A-Z])/.test(passwordData.newPassword)) {
      newErrors.newPassword = "Password must contain at least one uppercase letter";
      isValid = false;
    } else if (!/(?=.*\d)/.test(passwordData.newPassword)) {
      newErrors.newPassword = "Password must contain at least one number";
      isValid = false;
    }

    if (!passwordData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
      isValid = false;
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    if (updateErrorState) {
      setErrors(newErrors);
    }
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    setErrors(prev => ({
      ...prev,
      [name]: "",
    }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    setErrors(prev => ({
      ...prev,
      [name]: "",
    }));
  };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Image size should be less than 5MB",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setLoading(true);
      const formData = new FormData();
      formData.append("image", file);

      const response = await axiosInstance.post("user/upload-image", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      updateUserProfile(response.data);

      setFormData(prev => ({
        ...prev,
        imageUrl: response.data.imageUrl,
      }));

      toast({
        title: "Image uploaded successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Error uploading image",
        description: "Please try again later",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    try {
      const updatedData = {
        userName: `${formData.firstName} ${formData.lastName}`,
        email: formData.EmailAddress,
      };

      const response = await axiosInstance.put("user/update-profile", updatedData);
      updateUserProfile(response.data);

      toast({
        title: "Profile updated successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Error updating profile",
        description: "Please try again later",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validatePasswordForm(true)) return;

    setLoading(true);
    try {
      const { currentPassword, newPassword, confirmPassword } = passwordData;
      await axiosInstance.put("user/update-password", {
        currentPassword,
        newPassword,
        confirmPassword
      });

      toast({
        title: "Password updated successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
      });

      // Clear password fields after successful update
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (error: any) {
      let errorMessage = "Please try again later";

      // Check if we have validation errors from the API
      if (error.response?.data?.errors) {
        const apiErrors = error.response.data.errors;

        // Update the errors state with API validation errors
        const newErrors = { ...errors };

        apiErrors.forEach((err: any) => {
          if (err.path === 'confirmPassword') {
            newErrors.confirmPassword = err.msg;
          } else if (err.path === 'newPassword') {
            newErrors.newPassword = err.msg;
          } else if (err.path === 'currentPassword') {
            newErrors.currentPassword = err.msg;
          }
        });

        setErrors(newErrors);
        errorMessage = "Please fix the validation errors";
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      toast({
        title: "Error updating password",
        description: errorMessage,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFormData({ ...initialFormData });
    setErrors(prev => ({
      ...prev,
      firstName: "",
      lastName: "",
      EmailAddress: ""
    }));
  };

  const handlePasswordReset = () => {
    setPasswordData({
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
    setErrors(prev => ({
      ...prev,
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    }));
  };

  // Handle notification test
  const handleTestNotification = async () => {
    try {
      const success = await testNotification();
      if (success) {
        toast({
          title: "Test notification sent",
          description: "Check your browser notifications",
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        toast({
          title: "Failed to send test notification",
          description: "Please check your browser notification permissions",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send test notification",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const renderNotificationSection = () => {
    if (!isSupported) {
      return (
        <Text color="red.500">
          Browser notifications are not supported in your browser.
        </Text>
      );
    }

    return (
      <VStack spacing={4} align="stretch" width="100%">
        <FormControl>
          <FormLabel>Browser Notifications</FormLabel>
          <Text fontSize="sm" color="gray.600" mb={2}>
            Status: {permission === "granted" ? "Enabled" : "Disabled"}
          </Text>
          {permission !== "granted" && (
            <Button
              colorScheme="blue"
              onClick={requestPermission}
              isLoading={loading}
              mb={2}
            >
              Enable Notifications
            </Button>
          )}
          <Button
            colorScheme="teal"
            onClick={handleTestNotification}
            isDisabled={permission !== "granted"}
            mt={2}
          >
            Test Notification
          </Button>
        </FormControl>
      </VStack>
    );
  };

  if (userLoading) {
    return (
      <Flex h="100vh" align="center" justify="center">
        <Spinner size="xl" color="blue.500" />
      </Flex>
    );
  }

  return (
    <Container p={8} w="100%" margin={0} maxWidth={"none"}>
      <Box bg="white" borderRadius="xl" p={8} boxShadow="sm">
        <VStack spacing={8} align="stretch">
          <Box>
            <Text fontSize="2xl" fontWeight="bold" color="gray.800" mb={1}>
              Profile Settings
            </Text>
            <Text fontSize="sm" color="gray.600">
              Update your profile
            </Text>
          </Box>

          <Flex gap={8}>
            <Box display="flex" flexDirection="column" alignItems="center" gap={4}>
              <Box position="relative" width="fit-content">
                <Avatar
                  size="xl"
                  src={formData.imageUrl}
                />
                <IconButton
                  aria-label="Upload photo"
                  icon={<FaCamera />}
                  size="sm"
                  colorScheme="blue"
                  position="absolute"
                  bottom="-2"
                  right="-2"
                  rounded="full"
                  onClick={() => fileInputRef.current?.click()}
                />
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" mt={2} textAlign="center">
                  Click to Upload or drag and drop
                </Text>
                <Text fontSize="xs" color="gray.500" textAlign="center">
                  JPG, PNG or GIF
                </Text>
              </Box>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageChange}
                accept="image/*"
                style={{ display: "none" }}
              />
            </Box>

            <VStack flex={1} spacing={6} align="stretch">
              <Flex gap={6}>
                <FormControl>
                  <FormLabel color="gray.700" fontSize="sm">First Name</FormLabel>
                  <Input
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    placeholder="Enter first name"
                    bg="gray.50"
                    border="1px solid"
                    borderColor="gray.200"
                    color="gray.800"
                    _placeholder={{ color: "gray.400" }}
                    _focus={{ borderColor: "blue.500", boxShadow: "none" }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel color="gray.700" fontSize="sm">Last Name</FormLabel>
                  <Input
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    placeholder="Enter last name"
                    bg="gray.50"
                    border="1px solid"
                    borderColor="gray.200"
                    color="gray.800"
                    _placeholder={{ color: "gray.400" }}
                    _focus={{ borderColor: "blue.500", boxShadow: "none" }}
                  />
                </FormControl>
              </Flex>

              <FormControl>
                <FormLabel color="gray.700" fontSize="sm">Email</FormLabel>
                <Input
                  name="EmailAddress"
                  type="email"
                  value={formData.EmailAddress}
                  onChange={handleChange}
                  placeholder="Enter email"
                  bg="gray.50"
                  border="1px solid"
                  borderColor="gray.200"
                  color="gray.800"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{ borderColor: "blue.500", boxShadow: "none" }}
                />
              </FormControl>
            </VStack>
          </Flex>

          <Flex justify="flex-end" gap={4} mt={4}>
            <Button
              variant="outline"
              onClick={handleReset}
              color="gray.800"
              borderColor="gray.300"
              _hover={{ bg: "gray.50" }}
              isDisabled={!isProfileModified}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              isLoading={loading}
              loadingText="Saving..."
              onClick={handleSubmit}
              isDisabled={!isProfileModified}
            >
              Update profile
            </Button>
          </Flex>

          <Box>
            <Text fontSize="md" fontWeight="semibold" color="gray.800" mb={4}>
              Change Password
            </Text>
            <Text fontSize="sm" color="gray.600" mb={4}>
              Enter your current password to change it
            </Text>

            <VStack spacing={4} as="form" onSubmit={handlePasswordSubmit} sx={{ padding: 0 }}>
              <FormControl isRequired isInvalid={!!errors.currentPassword}>
                <FormLabel color="gray.700" fontSize="sm">Current Password</FormLabel>
                <InputGroup>
                  <Input
                    name="currentPassword"
                    type={showCurrentPassword ? "text" : "password"}
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    placeholder="Enter current password"
                    bg="gray.50"
                    border="1px solid"
                    borderColor="gray.200"
                    color="gray.800"
                    _placeholder={{ color: "gray.400" }}
                    _focus={{ borderColor: "blue.500", boxShadow: "none" }}
                  />
                  <InputRightElement>
                    <IconButton
                      aria-label={showCurrentPassword ? "Hide password" : "Show password"}
                      icon={showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
                      variant="ghost"
                      color="gray.400"
                      _hover={{ color: "gray.600" }}
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    />
                  </InputRightElement>
                </InputGroup>
                {errors.currentPassword && (
                  <Text color="red.500" fontSize="sm">{errors.currentPassword}</Text>
                )}
              </FormControl>

              <FormControl isRequired isInvalid={!!errors.newPassword}>
                <FormLabel color="gray.700" fontSize="sm">New Password</FormLabel>
                <InputGroup>
                  <Input
                    name="newPassword"
                    type={showNewPassword ? "text" : "password"}
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    placeholder="Enter new password"
                    bg="gray.50"
                    border="1px solid"
                    borderColor="gray.200"
                    color="gray.800"
                    _placeholder={{ color: "gray.400" }}
                    _focus={{ borderColor: "blue.500", boxShadow: "none" }}
                  />
                  <InputRightElement>
                    <IconButton
                      aria-label={showNewPassword ? "Hide password" : "Show password"}
                      icon={showNewPassword ? <FaEyeSlash /> : <FaEye />}
                      variant="ghost"
                      color="gray.400"
                      _hover={{ color: "gray.600" }}
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    />
                  </InputRightElement>
                </InputGroup>
                {errors.newPassword && (
                  <Text color="red.500" fontSize="sm">{errors.newPassword}</Text>
                )}
              </FormControl>

              <FormControl isRequired isInvalid={!!errors.confirmPassword}>
                <FormLabel color="gray.700" fontSize="sm">Confirm Password</FormLabel>
                <InputGroup>
                  <Input
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    placeholder="Confirm new password"
                    bg="gray.50"
                    border="1px solid"
                    borderColor="gray.200"
                    color="gray.800"
                    _placeholder={{ color: "gray.400" }}
                    _focus={{ borderColor: "blue.500", boxShadow: "none" }}
                  />
                  <InputRightElement>
                    <IconButton
                      aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                      icon={showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                      variant="ghost"
                      color="gray.400"
                      _hover={{ color: "gray.600" }}
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    />
                  </InputRightElement>
                </InputGroup>
                {errors.confirmPassword && (
                  <Text color="red.500" fontSize="sm">{errors.confirmPassword}</Text>
                )}
              </FormControl>

              <Flex w="100%" justify="flex-end" gap={4}>
                <Button
                  variant="outline"
                  onClick={handlePasswordReset}
                  color="gray.800"
                  borderColor="gray.300"
                  _hover={{ bg: "gray.50" }}
                  isDisabled={!passwordData.currentPassword && !passwordData.newPassword && !passwordData.confirmPassword}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  colorScheme="blue"
                  isLoading={loading}
                  loadingText="Updating..."
                  isDisabled={!isPasswordValid}
                >
                  Update Password
                </Button>
              </Flex>
            </VStack>
          </Box>

          {/* Notification Settings Section */}
          <Box p={6} bg="white" borderRadius="lg" boxShadow="sm">
            <Text fontSize="xl" fontWeight="bold" mb={4}>
              Notification Settings
            </Text>
            {renderNotificationSection()}
          </Box>
        </VStack>
      </Box>
    </Container>
  );
};

export default Profile;
