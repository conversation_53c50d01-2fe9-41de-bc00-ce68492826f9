import withP<PERSON> from "next-pwa";

// Define Next.js settings
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
};

// Define PWA options for `next-pwa`
const pwaConfig = {
  dest: "public",
  disable: process.env.NODE_ENV === "development",
  register: true,
  skipWaiting: true,
  buildExcludes: [
    /middleware-manifest\.json$/,
    /notification-sw\.js$/,
    /notification-worker\.js$/
  ],
  runtimeCaching: [
    {
      urlPattern: /^https:\/\/fonts\./,
      handler: 'CacheFirst',
      options: {
        cacheName: 'google-fonts',
        expiration: {
          maxEntries: 10,
          maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
        }
      }
    },
    {
      urlPattern: /\.(png|jpg|jpeg|svg|gif|ico)$/,
      handler: 'CacheFirst',
      options: {
        cacheName: 'images',
        expiration: {
          maxEntries: 50,
          maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
        }
      }
    }
  ]
};

// Export the configuration with `withPWA`, applying `pwaConfig` only to `next-pwa`
export default withPWA(pwaConfig)(nextConfig);
