"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _src_components_Custom_ReportTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @src/components/Custom/ReportTable */ \"(app-pages-browser)/./src/components/Custom/ReportTable/index.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        totalCount: 0,\n        totalPages: 0\n    });\n    const [selectedVoucher, setSelectedVoucher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voucherDetails, setVoucherDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentHistory, setPaymentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalLoading, setModalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const fetchData = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n            console.log(response.data);\n            if (response.data && response.data.data) {\n                setData(response.data.data);\n                setPagination({\n                    page: response.data.page,\n                    pageSize: response.data.pageSize,\n                    totalCount: response.data.totalCount,\n                    totalPages: response.data.totalPages\n                });\n            } else {\n                setData([]);\n                setPagination({\n                    page: 1,\n                    pageSize: 20,\n                    totalCount: 0,\n                    totalPages: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch cash voucher report data\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchData();\n    }, []);\n    const fetchVoucherDetails = async (voucherNo)=>{\n        try {\n            setModalLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/voucher-details/\".concat(voucherNo));\n            if (response.data) {\n                setVoucherDetails(response.data.voucher_details);\n                setPaymentHistory(response.data.payment_history || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching voucher details: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch voucher details\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setModalLoading(false);\n        }\n    };\n    const handleOpenModal = async (voucher)=>{\n        setSelectedVoucher(voucher);\n        setIsModalOpen(true);\n        await fetchVoucherDetails(voucher.voucher_no);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedVoucher(null);\n        setVoucherDetails(null);\n        setPaymentHistory([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"voucher_no\"\n        },\n        {\n            header: \"Quotation No (adj)\",\n            field: \"quotation_no\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"client_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"client_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"gross_amount\",\n            render: (item)=>formatCurrency(item.gross_amount)\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"paid_amount\",\n            render: (item)=>formatCurrency(item.paid_amount)\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"remaining_amount\",\n            render: (item)=>formatCurrency(item.remaining_amount)\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        },\n        {\n            header: \"Actions\",\n            field: \"actions\",\n            render: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    colorScheme: \"blue\",\n                    size: \"sm\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleOpenModal(item);\n                    },\n                    children: \"View Details\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 170,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    const getRowCursor = (task)=>task.status === \"pending\" ? \"pointer\" : \"auto\";\n    const handleRowClick = (task)=>{\n        if (task.status === \"pending\") {\n            router.push(\"/forms/audit-report/follow-up?purpose_no=\".concat(task.ID));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 195,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-inner\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"row\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bgWhite\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                margin: \"0\",\n                                                textAlign: \"center\",\n                                                color: \"#2B6CB0\",\n                                                fontSize: \"24px\",\n                                                fontWeight: \"bold\",\n                                                padding: \"10px\"\n                                            },\n                                            children: \"Audit Reports\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"row\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_ReportTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        data: tasks,\n                                        columns: columns,\n                                        onRowClick: handleRowClick,\n                                        getRowCursor: getRowCursor,\n                                        getBadgeColor: getStatusColor,\n                                        dateField: \"assessmentTime\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 201,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 200,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 199,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                lineNumber: 198,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"qpqJ1ZYh2Yxi5koS9WCjAkO1iWI=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ })

});