-- Safe Notification System Setup Script
-- This script creates tables without foreign keys first, then adds them after checking data types

PRINT 'Starting Notification System Setup...';

-- Step 1: Create the notification tables WITHOUT foreign keys first
PRINT 'Creating notification tables...';

-- Notification Types Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NotificationTypes' AND xtype='U')
BEGIN
    CREATE TABLE NotificationTypes (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        TypeName NVARCHAR(50) NOT NULL UNIQUE,
        DisplayName NVARCHAR(100) NOT NULL,
        Description NVARCHAR(255),
        IconName NVARCHAR(50),
        ColorScheme NVARCHAR(20),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UpdatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT 'NotificationTypes table created.';
END
ELSE
BEGIN
    PRINT 'NotificationTypes table already exists.';
END

-- Notification Templates Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NotificationTemplates' AND xtype='U')
BEGIN
    CREATE TABLE NotificationTemplates (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        TypeID INT NOT NULL,
        TemplateName NVARCHAR(100) NOT NULL,
        TitleTemplate NVARCHAR(255) NOT NULL,
        MessageTemplate NVARCHAR(1000) NOT NULL,
        ActionButtonText NVARCHAR(50),
        ActionUrl NVARCHAR(255),
        Priority TINYINT DEFAULT 1,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UpdatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT 'NotificationTemplates table created.';
END
ELSE
BEGIN
    PRINT 'NotificationTemplates table already exists.';
END

-- Get the actual data type of users.id column
DECLARE @UsersIdDataType NVARCHAR(50);
DECLARE @UsersIdLength INT;
DECLARE @EmployeeIdDataType NVARCHAR(50);
DECLARE @EmployeeIdLength INT;

SELECT 
    @UsersIdDataType = DATA_TYPE,
    @UsersIdLength = ISNULL(CHARACTER_MAXIMUM_LENGTH, 0)
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'id';

SELECT 
    @EmployeeIdDataType = DATA_TYPE,
    @EmployeeIdLength = ISNULL(CHARACTER_MAXIMUM_LENGTH, 0)
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'EmployeeDetails' AND COLUMN_NAME = 'ID';

PRINT 'Users.id data type: ' + ISNULL(@UsersIdDataType, 'NOT FOUND') + 
      CASE WHEN @UsersIdLength > 0 THEN '(' + CAST(@UsersIdLength AS NVARCHAR(10)) + ')' ELSE '' END;
PRINT 'EmployeeDetails.ID data type: ' + ISNULL(@EmployeeIdDataType, 'NOT FOUND') + 
      CASE WHEN @EmployeeIdLength > 0 THEN '(' + CAST(@EmployeeIdLength AS NVARCHAR(10)) + ')' ELSE '' END;

-- Notifications Table - using dynamic SQL to match the users.id data type
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications' AND xtype='U')
BEGIN
    DECLARE @NotificationsSql NVARCHAR(MAX);
    DECLARE @UserIdColumnDef NVARCHAR(100);
    DECLARE @EmployeeIdColumnDef NVARCHAR(100);
    
    -- Build column definitions based on actual data types
    IF @UsersIdDataType = 'varchar'
        SET @UserIdColumnDef = 'VARCHAR(' + CAST(@UsersIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @UsersIdDataType = 'nvarchar'
        SET @UserIdColumnDef = 'NVARCHAR(' + CAST(@UsersIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @UsersIdDataType = 'char'
        SET @UserIdColumnDef = 'CHAR(' + CAST(@UsersIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @UsersIdDataType = 'nchar'
        SET @UserIdColumnDef = 'NCHAR(' + CAST(@UsersIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @UsersIdDataType = 'int'
        SET @UserIdColumnDef = 'INT';
    ELSE IF @UsersIdDataType = 'bigint'
        SET @UserIdColumnDef = 'BIGINT';
    ELSE
        SET @UserIdColumnDef = 'NVARCHAR(32)'; -- Default fallback
    
    IF @EmployeeIdDataType = 'varchar'
        SET @EmployeeIdColumnDef = 'VARCHAR(' + CAST(@EmployeeIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @EmployeeIdDataType = 'nvarchar'
        SET @EmployeeIdColumnDef = 'NVARCHAR(' + CAST(@EmployeeIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @EmployeeIdDataType = 'char'
        SET @EmployeeIdColumnDef = 'CHAR(' + CAST(@EmployeeIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @EmployeeIdDataType = 'nchar'
        SET @EmployeeIdColumnDef = 'NCHAR(' + CAST(@EmployeeIdLength AS NVARCHAR(10)) + ')';
    ELSE IF @EmployeeIdDataType = 'int'
        SET @EmployeeIdColumnDef = 'INT';
    ELSE IF @EmployeeIdDataType = 'bigint'
        SET @EmployeeIdColumnDef = 'BIGINT';
    ELSE
        SET @EmployeeIdColumnDef = 'NVARCHAR(32)'; -- Default fallback

    SET @NotificationsSql = '
    CREATE TABLE Notifications (
        ID BIGINT IDENTITY(1,1) PRIMARY KEY,
        TypeID INT NOT NULL,
        TemplateID INT,
        Title NVARCHAR(255) NOT NULL,
        Message NVARCHAR(1000) NOT NULL,
        ActionButtonText NVARCHAR(50),
        ActionUrl NVARCHAR(255),
        Priority TINYINT DEFAULT 1,
        SenderUserID ' + @UserIdColumnDef + ',
        SenderEmployeeID ' + @EmployeeIdColumnDef + ',
        ContextData NVARCHAR(MAX),
        ReferenceID NVARCHAR(50),
        ReferenceType NVARCHAR(50),
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        ExpiresAt DATETIME2
    );';
    
    EXEC sp_executesql @NotificationsSql;
    PRINT 'Notifications table created with matching data types.';
END
ELSE
BEGIN
    PRINT 'Notifications table already exists.';
END

-- Notification Recipients Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NotificationRecipients' AND xtype='U')
BEGIN
    DECLARE @RecipientsSql NVARCHAR(MAX);
    
    SET @RecipientsSql = '
    CREATE TABLE NotificationRecipients (
        ID BIGINT IDENTITY(1,1) PRIMARY KEY,
        NotificationID BIGINT NOT NULL,
        RecipientUserID ' + @UserIdColumnDef + ',
        RecipientRoleID INT,
        RecipientEmployeeID ' + @EmployeeIdColumnDef + ',
        IsRead BIT DEFAULT 0,
        ReadAt DATETIME2,
        IsArchived BIT DEFAULT 0,
        ArchivedAt DATETIME2,
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );';
    
    EXEC sp_executesql @RecipientsSql;
    PRINT 'NotificationRecipients table created with matching data types.';
END
ELSE
BEGIN
    PRINT 'NotificationRecipients table already exists.';
END

-- Notification Settings Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NotificationSettings' AND xtype='U')
BEGIN
    DECLARE @SettingsSql NVARCHAR(MAX);
    
    SET @SettingsSql = '
    CREATE TABLE NotificationSettings (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        UserID ' + @UserIdColumnDef + ' NOT NULL,
        TypeID INT NOT NULL,
        IsEnabled BIT DEFAULT 1,
        EmailNotification BIT DEFAULT 0,
        PushNotification BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UpdatedAt DATETIME2 DEFAULT GETDATE()
    );';
    
    EXEC sp_executesql @SettingsSql;
    PRINT 'NotificationSettings table created with matching data types.';
END
ELSE
BEGIN
    PRINT 'NotificationSettings table already exists.';
END

PRINT 'All notification tables created successfully!';
PRINT 'Next, run the foreign key and index creation script.';
PRINT 'Data types used:';
PRINT '  SenderUserID/RecipientUserID: ' + @UserIdColumnDef;
PRINT '  SenderEmployeeID/RecipientEmployeeID: ' + @EmployeeIdColumnDef;
