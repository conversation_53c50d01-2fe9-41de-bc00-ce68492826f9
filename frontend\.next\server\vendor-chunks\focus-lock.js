"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/focus-lock";
exports.ids = ["vendor-chunks/focus-lock"];
exports.modules = {

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/commands.js":
/*!*********************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/commands.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusOn: () => (/* binding */ focusOn)\n/* harmony export */ });\nvar focusOn = function (target, focusOptions) {\n    if (!target) {\n        // not clear how, but is possible https://github.com/theKashey/focus-lock/issues/53\n        return;\n    }\n    if ('focus' in target) {\n        target.focus(focusOptions);\n    }\n    if ('contentWindow' in target && target.contentWindow) {\n        target.contentWindow.focus();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9jb21tYW5kcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvY29tbWFuZHMuanM/ZjljNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIGZvY3VzT24gPSBmdW5jdGlvbiAodGFyZ2V0LCBmb2N1c09wdGlvbnMpIHtcbiAgICBpZiAoIXRhcmdldCkge1xuICAgICAgICAvLyBub3QgY2xlYXIgaG93LCBidXQgaXMgcG9zc2libGUgaHR0cHM6Ly9naXRodWIuY29tL3RoZUthc2hleS9mb2N1cy1sb2NrL2lzc3Vlcy81M1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmICgnZm9jdXMnIGluIHRhcmdldCkge1xuICAgICAgICB0YXJnZXQuZm9jdXMoZm9jdXNPcHRpb25zKTtcbiAgICB9XG4gICAgaWYgKCdjb250ZW50V2luZG93JyBpbiB0YXJnZXQgJiYgdGFyZ2V0LmNvbnRlbnRXaW5kb3cpIHtcbiAgICAgICAgdGFyZ2V0LmNvbnRlbnRXaW5kb3cuZm9jdXMoKTtcbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/commands.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/constants.js":
/*!**********************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/constants.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FOCUS_ALLOW: () => (/* binding */ FOCUS_ALLOW),\n/* harmony export */   FOCUS_AUTO: () => (/* binding */ FOCUS_AUTO),\n/* harmony export */   FOCUS_DISABLED: () => (/* binding */ FOCUS_DISABLED),\n/* harmony export */   FOCUS_GROUP: () => (/* binding */ FOCUS_GROUP),\n/* harmony export */   FOCUS_NO_AUTOFOCUS: () => (/* binding */ FOCUS_NO_AUTOFOCUS)\n/* harmony export */ });\n/**\n * defines a focus group\n */\nvar FOCUS_GROUP = 'data-focus-lock';\n/**\n * disables element discovery inside a group marked by key\n */\nvar FOCUS_DISABLED = 'data-focus-lock-disabled';\n/**\n * allows uncontrolled focus within the marked area, effectively disabling focus lock for it's content\n */\nvar FOCUS_ALLOW = 'data-no-focus-lock';\n/**\n * instructs autofocus engine to pick default autofocus inside a given node\n * can be set on the element or container\n */\nvar FOCUS_AUTO = 'data-autofocus-inside';\n/**\n * instructs autofocus to ignore elements within a given node\n * can be set on the element or container\n */\nvar FOCUS_NO_AUTOFOCUS = 'data-no-autofocus';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L2NvbnN0YW50cy5qcz9kM2Y4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogZGVmaW5lcyBhIGZvY3VzIGdyb3VwXG4gKi9cbmV4cG9ydCB2YXIgRk9DVVNfR1JPVVAgPSAnZGF0YS1mb2N1cy1sb2NrJztcbi8qKlxuICogZGlzYWJsZXMgZWxlbWVudCBkaXNjb3ZlcnkgaW5zaWRlIGEgZ3JvdXAgbWFya2VkIGJ5IGtleVxuICovXG5leHBvcnQgdmFyIEZPQ1VTX0RJU0FCTEVEID0gJ2RhdGEtZm9jdXMtbG9jay1kaXNhYmxlZCc7XG4vKipcbiAqIGFsbG93cyB1bmNvbnRyb2xsZWQgZm9jdXMgd2l0aGluIHRoZSBtYXJrZWQgYXJlYSwgZWZmZWN0aXZlbHkgZGlzYWJsaW5nIGZvY3VzIGxvY2sgZm9yIGl0J3MgY29udGVudFxuICovXG5leHBvcnQgdmFyIEZPQ1VTX0FMTE9XID0gJ2RhdGEtbm8tZm9jdXMtbG9jayc7XG4vKipcbiAqIGluc3RydWN0cyBhdXRvZm9jdXMgZW5naW5lIHRvIHBpY2sgZGVmYXVsdCBhdXRvZm9jdXMgaW5zaWRlIGEgZ2l2ZW4gbm9kZVxuICogY2FuIGJlIHNldCBvbiB0aGUgZWxlbWVudCBvciBjb250YWluZXJcbiAqL1xuZXhwb3J0IHZhciBGT0NVU19BVVRPID0gJ2RhdGEtYXV0b2ZvY3VzLWluc2lkZSc7XG4vKipcbiAqIGluc3RydWN0cyBhdXRvZm9jdXMgdG8gaWdub3JlIGVsZW1lbnRzIHdpdGhpbiBhIGdpdmVuIG5vZGVcbiAqIGNhbiBiZSBzZXQgb24gdGhlIGVsZW1lbnQgb3IgY29udGFpbmVyXG4gKi9cbmV4cG9ydCB2YXIgRk9DVVNfTk9fQVVUT0ZPQ1VTID0gJ2RhdGEtbm8tYXV0b2ZvY3VzJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/focusInside.js":
/*!************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/focusInside.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusInside: () => (/* binding */ focusInside)\n/* harmony export */ });\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/all-affected */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n\n\n\n\nvar focusInFrame = function (frame, activeElement) { return frame === activeElement; };\nvar focusInsideIframe = function (topNode, activeElement) {\n    return Boolean((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(topNode.querySelectorAll('iframe')).some(function (node) { return focusInFrame(node, activeElement); }));\n};\n/**\n * @returns {Boolean} true, if the current focus is inside given node or nodes.\n * Supports nodes hidden inside shadowDom\n */\nvar focusInside = function (topNode, activeElement) {\n    // const activeElement = document && getActiveElement();\n    if (activeElement === void 0) { activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__.getActiveElement)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.getFirst)(topNode).ownerDocument); }\n    if (!activeElement || (activeElement.dataset && activeElement.dataset.focusGuard)) {\n        return false;\n    }\n    return (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_2__.getAllAffectedNodes)(topNode).some(function (node) {\n        return (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__.contains)(node, activeElement) || focusInsideIframe(node, activeElement);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/focusInside.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/focusIsHidden.js":
/*!**************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/focusIsHidden.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusIsHidden: () => (/* binding */ focusIsHidden)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n\n\n\n\n/**\n * checks if focus is hidden FROM the focus-lock\n * ie contained inside a node focus-lock shall ignore\n *\n * This is a utility function coupled with {@link FOCUS_ALLOW} constant\n *\n * @returns {boolean} focus is currently is in \"allow\" area\n */\nvar focusIsHidden = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    var activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_0__.getActiveElement)(inDocument);\n    if (!activeElement) {\n        return false;\n    }\n    // this does not support setting FOCUS_ALLOW within shadow dom\n    return (0,_utils_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(inDocument.querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_2__.FOCUS_ALLOW, \"]\"))).some(function (node) { return (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__.contains)(node, activeElement); });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9mb2N1c0lzSGlkZGVuLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBDO0FBQ0U7QUFDSjtBQUNvQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxtQkFBbUI7QUFDL0Q7QUFDQSxhQUFhLFNBQVM7QUFDdEI7QUFDTztBQUNQLGlDQUFpQztBQUNqQyx3QkFBd0IseUVBQWdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxxREFBTyx3Q0FBd0MsbURBQVcsZ0NBQWdDLE9BQU8seURBQVEsd0JBQXdCO0FBQzVJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvZm9jdXNJc0hpZGRlbi5qcz8xNDU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZPQ1VTX0FMTE9XIH0gZnJvbSAnLi9jb25zdGFudHMnO1xuaW1wb3J0IHsgY29udGFpbnMgfSBmcm9tICcuL3V0aWxzL0RPTXV0aWxzJztcbmltcG9ydCB7IHRvQXJyYXkgfSBmcm9tICcuL3V0aWxzL2FycmF5JztcbmltcG9ydCB7IGdldEFjdGl2ZUVsZW1lbnQgfSBmcm9tICcuL3V0aWxzL2dldEFjdGl2ZUVsZW1lbnQnO1xuLyoqXG4gKiBjaGVja3MgaWYgZm9jdXMgaXMgaGlkZGVuIEZST00gdGhlIGZvY3VzLWxvY2tcbiAqIGllIGNvbnRhaW5lZCBpbnNpZGUgYSBub2RlIGZvY3VzLWxvY2sgc2hhbGwgaWdub3JlXG4gKlxuICogVGhpcyBpcyBhIHV0aWxpdHkgZnVuY3Rpb24gY291cGxlZCB3aXRoIHtAbGluayBGT0NVU19BTExPV30gY29uc3RhbnRcbiAqXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn0gZm9jdXMgaXMgY3VycmVudGx5IGlzIGluIFwiYWxsb3dcIiBhcmVhXG4gKi9cbmV4cG9ydCB2YXIgZm9jdXNJc0hpZGRlbiA9IGZ1bmN0aW9uIChpbkRvY3VtZW50KSB7XG4gICAgaWYgKGluRG9jdW1lbnQgPT09IHZvaWQgMCkgeyBpbkRvY3VtZW50ID0gZG9jdW1lbnQ7IH1cbiAgICB2YXIgYWN0aXZlRWxlbWVudCA9IGdldEFjdGl2ZUVsZW1lbnQoaW5Eb2N1bWVudCk7XG4gICAgaWYgKCFhY3RpdmVFbGVtZW50KSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgLy8gdGhpcyBkb2VzIG5vdCBzdXBwb3J0IHNldHRpbmcgRk9DVVNfQUxMT1cgd2l0aGluIHNoYWRvdyBkb21cbiAgICByZXR1cm4gdG9BcnJheShpbkRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoXCJbXCIuY29uY2F0KEZPQ1VTX0FMTE9XLCBcIl1cIikpKS5zb21lKGZ1bmN0aW9uIChub2RlKSB7IHJldHVybiBjb250YWlucyhub2RlLCBhY3RpdmVFbGVtZW50KTsgfSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/focusIsHidden.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/focusSolver.js":
/*!************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/focusSolver.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusSolver: () => (/* binding */ focusSolver)\n/* harmony export */ });\n/* harmony import */ var _solver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./solver */ \"(ssr)/./node_modules/focus-lock/dist/es2015/solver.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/all-affected */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/auto-focus */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/auto-focus.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _utils_parenting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/parenting */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/parenting.js\");\n\n\n\n\n\n\n\n\nvar reorderNodes = function (srcNodes, dstNodes) {\n    var remap = new Map();\n    // no Set(dstNodes) for IE11 :(\n    dstNodes.forEach(function (entity) { return remap.set(entity.node, entity); });\n    // remap to dstNodes\n    return srcNodes.map(function (node) { return remap.get(node); }).filter(_utils_is__WEBPACK_IMPORTED_MODULE_0__.isDefined);\n};\n/**\n * contains the main logic of the `focus-lock` package.\n *\n * ! you probably dont need this function !\n *\n * given top node(s) and the last active element returns the element to be focused next\n * @returns element which should be focused to move focus inside\n * @param topNode\n * @param lastNode\n */\nvar focusSolver = function (topNode, lastNode) {\n    var activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__.getActiveElement)((0,_utils_array__WEBPACK_IMPORTED_MODULE_2__.asArray)(topNode).length > 0 ? document : (0,_utils_array__WEBPACK_IMPORTED_MODULE_2__.getFirst)(topNode).ownerDocument);\n    var entries = (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_3__.getAllAffectedNodes)(topNode).filter(_utils_is__WEBPACK_IMPORTED_MODULE_0__.isNotAGuard);\n    var commonParent = (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.getTopCommonParent)(activeElement || topNode, topNode, entries);\n    var visibilityCache = new Map();\n    var anyFocusable = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__.getFocusableNodes)(entries, visibilityCache);\n    var innerElements = anyFocusable.filter(function (_a) {\n        var node = _a.node;\n        return (0,_utils_is__WEBPACK_IMPORTED_MODULE_0__.isNotAGuard)(node);\n    });\n    if (!innerElements[0]) {\n        return undefined;\n    }\n    var outerNodes = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__.getFocusableNodes)([commonParent], visibilityCache).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var orderedInnerElements = reorderNodes(outerNodes, innerElements);\n    // collect inner focusable and separately tabbables\n    var innerFocusables = orderedInnerElements.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var innerTabbable = orderedInnerElements.filter(function (_a) {\n        var tabIndex = _a.tabIndex;\n        return tabIndex >= 0;\n    }).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var newId = (0,_solver__WEBPACK_IMPORTED_MODULE_6__.newFocus)(innerFocusables, innerTabbable, outerNodes, activeElement, lastNode);\n    if (newId === _solver__WEBPACK_IMPORTED_MODULE_6__.NEW_FOCUS) {\n        var focusNode = \n        // first try only tabbable, and the fallback to all focusable, as long as at least one element should be picked for focus\n        (0,_utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__.pickAutofocus)(anyFocusable, innerTabbable, (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.allParentAutofocusables)(entries, visibilityCache)) ||\n            (0,_utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__.pickAutofocus)(anyFocusable, innerFocusables, (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.allParentAutofocusables)(entries, visibilityCache));\n        if (focusNode) {\n            return { node: focusNode };\n        }\n        else {\n            console.warn('focus-lock: cannot find any node to move focus into');\n            return undefined;\n        }\n    }\n    if (newId === undefined) {\n        return newId;\n    }\n    return orderedInnerElements[newId];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/focusSolver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/focusables.js":
/*!***********************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/focusables.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expandFocusableNodes: () => (/* binding */ expandFocusableNodes)\n/* harmony export */ });\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/all-affected */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/is */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _utils_parenting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/parenting */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/parenting.js\");\n/* harmony import */ var _utils_tabOrder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/tabOrder */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabOrder.js\");\n/* harmony import */ var _utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/tabUtils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabUtils.js\");\n\n\n\n\n\n/**\n * traverses all related nodes (including groups) returning a list of all nodes(outer and internal) with meta information\n * This is low-level API!\n * @returns list of focusable elements inside a given top(!) node.\n * @see {@link getFocusableNodes} providing a simpler API\n */\nvar expandFocusableNodes = function (topNode) {\n    var entries = (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_0__.getAllAffectedNodes)(topNode).filter(_utils_is__WEBPACK_IMPORTED_MODULE_1__.isNotAGuard);\n    var commonParent = (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_2__.getTopCommonParent)(topNode, topNode, entries);\n    var outerNodes = (0,_utils_tabOrder__WEBPACK_IMPORTED_MODULE_3__.orderByTabIndex)((0,_utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__.getFocusables)([commonParent], true), true, true);\n    var innerElements = (0,_utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__.getFocusables)(entries, false);\n    return outerNodes.map(function (_a) {\n        var node = _a.node, index = _a.index;\n        return ({\n            node: node,\n            index: index,\n            lockItem: innerElements.indexOf(node) >= 0,\n            guard: (0,_utils_is__WEBPACK_IMPORTED_MODULE_1__.isGuard)(node),\n        });\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/focusables.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/moveFocusInside.js":
/*!****************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/moveFocusInside.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   moveFocusInside: () => (/* binding */ moveFocusInside)\n/* harmony export */ });\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands */ \"(ssr)/./node_modules/focus-lock/dist/es2015/commands.js\");\n/* harmony import */ var _focusSolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./focusSolver */ \"(ssr)/./node_modules/focus-lock/dist/es2015/focusSolver.js\");\n\n\nvar guardCount = 0;\nvar lockDisabled = false;\n/**\n * The main functionality of the focus-lock package\n *\n * Contains focus at a given node.\n * The last focused element will help to determine which element(first or last) should be focused.\n * The found element will be focused.\n *\n * This is one time action (move), not a persistent focus-lock\n *\n * HTML markers (see {@link import('./constants').FOCUS_AUTO} constants) can control autofocus\n * @see {@link focusSolver} for the same functionality without autofocus\n */\nvar moveFocusInside = function (topNode, lastNode, options) {\n    if (options === void 0) { options = {}; }\n    var focusable = (0,_focusSolver__WEBPACK_IMPORTED_MODULE_0__.focusSolver)(topNode, lastNode);\n    // global local side effect to countain recursive lock activation and resolve focus-fighting\n    if (lockDisabled) {\n        return;\n    }\n    if (focusable) {\n        /** +FOCUS-FIGHTING prevention **/\n        if (guardCount > 2) {\n            // we have recursive entered back the lock activation\n            console.error('FocusLock: focus-fighting detected. Only one focus management system could be active. ' +\n                'See https://github.com/theKashey/focus-lock/#focus-fighting');\n            lockDisabled = true;\n            setTimeout(function () {\n                lockDisabled = false;\n            }, 1);\n            return;\n        }\n        guardCount++;\n        (0,_commands__WEBPACK_IMPORTED_MODULE_1__.focusOn)(focusable.node, options.focusOptions);\n        guardCount--;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/moveFocusInside.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/return-focus.js":
/*!*************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/return-focus.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   captureFocusRestore: () => (/* binding */ captureFocusRestore),\n/* harmony export */   recordElementLocation: () => (/* binding */ recordElementLocation)\n/* harmony export */ });\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n\nfunction weakRef(value) {\n    if (!value)\n        return null;\n    // #68 Safari 14.1 dont have it yet\n    // FIXME: remove in 2025\n    if (typeof WeakRef === 'undefined') {\n        return function () { return value || null; };\n    }\n    var w = value ? new WeakRef(value) : null;\n    return function () { return (w === null || w === void 0 ? void 0 : w.deref()) || null; };\n}\nvar recordElementLocation = function (element) {\n    if (!element) {\n        return null;\n    }\n    var stack = [];\n    var currentElement = element;\n    while (currentElement && currentElement !== document.body) {\n        stack.push({\n            current: weakRef(currentElement),\n            parent: weakRef(currentElement.parentElement),\n            left: weakRef(currentElement.previousElementSibling),\n            right: weakRef(currentElement.nextElementSibling),\n        });\n        currentElement = currentElement.parentElement;\n    }\n    return {\n        element: weakRef(element),\n        stack: stack,\n        ownerDocument: element.ownerDocument,\n    };\n};\nvar restoreFocusTo = function (location) {\n    var _a, _b, _c, _d, _e;\n    if (!location) {\n        return undefined;\n    }\n    var stack = location.stack, ownerDocument = location.ownerDocument;\n    var visibilityCache = new Map();\n    for (var _i = 0, stack_1 = stack; _i < stack_1.length; _i++) {\n        var line = stack_1[_i];\n        var parent_1 = (_a = line.parent) === null || _a === void 0 ? void 0 : _a.call(line);\n        // is it still here?\n        if (parent_1 && ownerDocument.contains(parent_1)) {\n            var left = (_b = line.left) === null || _b === void 0 ? void 0 : _b.call(line);\n            var savedCurrent = line.current();\n            var current = parent_1.contains(savedCurrent) ? savedCurrent : undefined;\n            var right = (_c = line.right) === null || _c === void 0 ? void 0 : _c.call(line);\n            var focusables = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_0__.getTabbableNodes)([parent_1], visibilityCache);\n            var aim = \n            // that is element itself\n            (_e = (_d = current !== null && current !== void 0 ? current : \n            // or something in it's place\n            left === null || left === void 0 ? void 0 : left.nextElementSibling) !== null && _d !== void 0 ? _d : \n            // or somebody to the right, still close enough\n            right) !== null && _e !== void 0 ? _e : \n            // or somebody to the left, something?\n            left;\n            while (aim) {\n                for (var _f = 0, focusables_1 = focusables; _f < focusables_1.length; _f++) {\n                    var focusable = focusables_1[_f];\n                    if (aim === null || aim === void 0 ? void 0 : aim.contains(focusable.node)) {\n                        return focusable.node;\n                    }\n                }\n                aim = aim.nextElementSibling;\n            }\n            if (focusables.length) {\n                // if parent contains a focusable - move there\n                return focusables[0].node;\n            }\n        }\n    }\n    // nothing matched\n    return undefined;\n};\n/**\n * Captures the current focused element to restore focus as close as possible in the future\n * Handles situations where the focused element is removed from the DOM or no longer focusable\n * moving focus to the closest focusable element\n * @param targetElement - element where focus should be restored\n * @returns a function returning a new element to focus\n */\nvar captureFocusRestore = function (targetElement) {\n    var location = recordElementLocation(targetElement);\n    return function () {\n        return restoreFocusTo(location);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/return-focus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/sibling.js":
/*!********************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/sibling.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusFirstElement: () => (/* binding */ focusFirstElement),\n/* harmony export */   focusLastElement: () => (/* binding */ focusLastElement),\n/* harmony export */   focusNextElement: () => (/* binding */ focusNextElement),\n/* harmony export */   focusPrevElement: () => (/* binding */ focusPrevElement),\n/* harmony export */   getRelativeFocusable: () => (/* binding */ getRelativeFocusable)\n/* harmony export */ });\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands */ \"(ssr)/./node_modules/focus-lock/dist/es2015/commands.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n\n/**\n * for a given `element` in a given `scope` returns focusable siblings\n * @param element - base element\n * @param scope - common parent. Can be document, but better to narrow it down for performance reasons\n * @returns {prev,next} - references to a focusable element before and after\n * @returns undefined - if operation is not applicable\n */\nvar getRelativeFocusable = function (element, scope, useTabbables) {\n    if (!element || !scope) {\n        console.error('no element or scope given');\n        return {};\n    }\n    var shards = (0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(scope);\n    if (shards.every(function (shard) { return !(0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.contains)(shard, element); })) {\n        console.error('Active element is not contained in the scope');\n        return {};\n    }\n    var focusables = useTabbables\n        ? (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getTabbableNodes)(shards, new Map())\n        : (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getFocusableNodes)(shards, new Map());\n    var current = focusables.findIndex(function (_a) {\n        var node = _a.node;\n        return node === element;\n    });\n    if (current === -1) {\n        // an edge case, when anchor element is not found\n        return undefined;\n    }\n    return {\n        prev: focusables[current - 1],\n        next: focusables[current + 1],\n        first: focusables[0],\n        last: focusables[focusables.length - 1],\n    };\n};\nvar getBoundary = function (shards, useTabbables) {\n    var set = useTabbables\n        ? (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getTabbableNodes)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(shards), new Map())\n        : (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getFocusableNodes)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(shards), new Map());\n    return {\n        first: set[0],\n        last: set[set.length - 1],\n    };\n};\nvar defaultOptions = function (options) {\n    return Object.assign({\n        scope: document.body,\n        cycle: true,\n        onlyTabbable: true,\n    }, options);\n};\nvar moveFocus = function (fromElement, options, cb) {\n    if (options === void 0) { options = {}; }\n    var newOptions = defaultOptions(options);\n    var solution = getRelativeFocusable(fromElement, newOptions.scope, newOptions.onlyTabbable);\n    if (!solution) {\n        return;\n    }\n    var target = cb(solution, newOptions.cycle);\n    if (target) {\n        (0,_commands__WEBPACK_IMPORTED_MODULE_2__.focusOn)(target.node, newOptions.focusOptions);\n    }\n};\n/**\n * focuses next element in the tab-order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusNextElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var next = _a.next, first = _a.first;\n        return next || (cycle && first);\n    });\n};\n/**\n * focuses prev element in the tab order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusPrevElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var prev = _a.prev, last = _a.last;\n        return prev || (cycle && last);\n    });\n};\nvar pickBoundary = function (scope, options, what) {\n    var _a;\n    var boundary = getBoundary(scope, (_a = options.onlyTabbable) !== null && _a !== void 0 ? _a : true);\n    var node = boundary[what];\n    if (node) {\n        (0,_commands__WEBPACK_IMPORTED_MODULE_2__.focusOn)(node.node, options.focusOptions);\n    }\n};\n/**\n * focuses first element in the tab-order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusFirstElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'first');\n};\n/**\n * focuses last element in the tab order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusLastElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'last');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/sibling.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/solver.js":
/*!*******************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/solver.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NEW_FOCUS: () => (/* binding */ NEW_FOCUS),\n/* harmony export */   newFocus: () => (/* binding */ newFocus)\n/* harmony export */ });\n/* harmony import */ var _utils_correctFocus__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/correctFocus */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/correctFocus.js\");\n/* harmony import */ var _utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/firstFocus */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/firstFocus.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js\");\n\n\n\nvar NEW_FOCUS = 'NEW_FOCUS';\n/**\n * Main solver for the \"find next focus\" question\n * @param innerNodes - used to control \"return focus\"\n * @param innerTabbables - used to control \"autofocus\"\n * @param outerNodes\n * @param activeElement\n * @param lastNode\n * @returns {number|string|undefined|*}\n */\nvar newFocus = function (innerNodes, innerTabbables, outerNodes, activeElement, lastNode) {\n    var cnt = innerNodes.length;\n    var firstFocus = innerNodes[0];\n    var lastFocus = innerNodes[cnt - 1];\n    var isOnGuard = (0,_utils_is__WEBPACK_IMPORTED_MODULE_0__.isGuard)(activeElement);\n    // focus is inside\n    if (activeElement && innerNodes.indexOf(activeElement) >= 0) {\n        return undefined;\n    }\n    var activeIndex = activeElement !== undefined ? outerNodes.indexOf(activeElement) : -1;\n    var lastIndex = lastNode ? outerNodes.indexOf(lastNode) : activeIndex;\n    var lastNodeInside = lastNode ? innerNodes.indexOf(lastNode) : -1;\n    // no active focus (or focus is on the body)\n    if (activeIndex === -1) {\n        // known fallback\n        if (lastNodeInside !== -1) {\n            return lastNodeInside;\n        }\n        return NEW_FOCUS;\n    }\n    // new focus, nothing to calculate\n    if (lastNodeInside === -1) {\n        return NEW_FOCUS;\n    }\n    var indexDiff = activeIndex - lastIndex;\n    var firstNodeIndex = outerNodes.indexOf(firstFocus);\n    var lastNodeIndex = outerNodes.indexOf(lastFocus);\n    var correctedNodes = (0,_utils_correctFocus__WEBPACK_IMPORTED_MODULE_1__.correctNodes)(outerNodes);\n    var currentFocusableIndex = activeElement !== undefined ? correctedNodes.indexOf(activeElement) : -1;\n    var previousFocusableIndex = lastNode ? correctedNodes.indexOf(lastNode) : currentFocusableIndex;\n    var tabbableNodes = correctedNodes.filter(function (node) { return node.tabIndex >= 0; });\n    var currentTabbableIndex = activeElement !== undefined ? tabbableNodes.indexOf(activeElement) : -1;\n    var previousTabbableIndex = lastNode ? tabbableNodes.indexOf(lastNode) : currentTabbableIndex;\n    var focusIndexDiff = currentTabbableIndex >= 0 && previousTabbableIndex >= 0\n        ? // old/new are tabbables, measure distance in tabbable space\n            previousTabbableIndex - currentTabbableIndex\n        : // or else measure in focusable space\n            previousFocusableIndex - currentFocusableIndex;\n    // old focus\n    if (!indexDiff && lastNodeInside >= 0) {\n        return lastNodeInside;\n    }\n    // no tabbable elements, autofocus is not possible\n    if (innerTabbables.length === 0) {\n        // an edge case with no tabbable elements\n        // return the last focusable one\n        // with some probability this will prevent focus from cycling across the lock, but there is no tabbale elements to cycle to\n        return lastNodeInside;\n    }\n    var returnFirstNode = (0,_utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFocusable)(innerNodes, innerTabbables[0]);\n    var returnLastNode = (0,_utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFocusable)(innerNodes, innerTabbables[innerTabbables.length - 1]);\n    // first element\n    if (activeIndex <= firstNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnLastNode;\n    }\n    // last element\n    if (activeIndex >= lastNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnFirstNode;\n    }\n    // jump out, but not on the guard\n    if (indexDiff && Math.abs(focusIndexDiff) > 1) {\n        return lastNodeInside;\n    }\n    // focus above lock\n    if (activeIndex <= firstNodeIndex) {\n        return returnLastNode;\n    }\n    // focus below lock\n    if (activeIndex > lastNodeIndex) {\n        return returnFirstNode;\n    }\n    // index is inside tab order, but outside Lock\n    if (indexDiff) {\n        if (Math.abs(indexDiff) > 1) {\n            return lastNodeInside;\n        }\n        return (cnt + lastNodeInside + indexDiff) % cnt;\n    }\n    // do nothing\n    return undefined;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/solver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js":
/*!***************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/DOMutils.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contains: () => (/* binding */ contains),\n/* harmony export */   filterAutoFocusable: () => (/* binding */ filterAutoFocusable),\n/* harmony export */   filterFocusable: () => (/* binding */ filterFocusable),\n/* harmony export */   getFocusableNodes: () => (/* binding */ getFocusableNodes),\n/* harmony export */   getTabbableNodes: () => (/* binding */ getTabbableNodes),\n/* harmony export */   parentAutofocusables: () => (/* binding */ parentAutofocusables)\n/* harmony export */ });\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _tabOrder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tabOrder */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabOrder.js\");\n/* harmony import */ var _tabUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabUtils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabUtils.js\");\n\n\n\n\n/**\n * given list of focusable elements keeps the ones user can interact with\n * @param nodes\n * @param visibilityCache\n */\nvar filterFocusable = function (nodes, visibilityCache) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes)\n        .filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.isVisibleCached)(visibilityCache, node); })\n        .filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.notHiddenInput)(node); });\n};\nvar filterAutoFocusable = function (nodes, cache) {\n    if (cache === void 0) { cache = new Map(); }\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes).filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.isAutoFocusAllowedCached)(cache, node); });\n};\n/**\n * !__WARNING__! Low level API.\n * @returns all tabbable nodes\n *\n * @see {@link getFocusableNodes} to get any focusable element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getTabbableNodes = function (topNodes, visibilityCache, withGuards) {\n    return (0,_tabOrder__WEBPACK_IMPORTED_MODULE_2__.orderByTabIndex)(filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getFocusables)(topNodes, withGuards), visibilityCache), true, withGuards);\n};\n/**\n * !__WARNING__! Low level API.\n *\n * @returns anything \"focusable\", not only tabbable. The difference is in `tabIndex=-1`\n * (without guards, as long as they are not expected to be ever focused)\n *\n * @see {@link getTabbableNodes} to get only tabble nodes element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getFocusableNodes = function (topNodes, visibilityCache) {\n    return (0,_tabOrder__WEBPACK_IMPORTED_MODULE_2__.orderByTabIndex)(filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getFocusables)(topNodes), visibilityCache), false);\n};\n/**\n * return list of nodes which are expected to be auto-focused\n * @param topNode\n * @param visibilityCache\n */\nvar parentAutofocusables = function (topNode, visibilityCache) {\n    return filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getParentAutofocusables)(topNode), visibilityCache);\n};\n/*\n * Determines if element is contained in scope, including nested shadow DOMs\n */\nvar contains = function (scope, element) {\n    if (scope.shadowRoot) {\n        return contains(scope.shadowRoot, element);\n    }\n    else {\n        if (Object.getPrototypeOf(scope).contains !== undefined &&\n            Object.getPrototypeOf(scope).contains.call(scope, element)) {\n            return true;\n        }\n        return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(scope.children).some(function (child) {\n            var _a;\n            if (child instanceof HTMLIFrameElement) {\n                var iframeBody = (_a = child.contentDocument) === null || _a === void 0 ? void 0 : _a.body;\n                if (iframeBody) {\n                    return contains(iframeBody, element);\n                }\n                return false;\n            }\n            return contains(child, element);\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/all-affected.js":
/*!*******************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/all-affected.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllAffectedNodes: () => (/* binding */ getAllAffectedNodes)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n/**\n * in case of multiple nodes nested inside each other\n * keeps only top ones\n * this is O(nlogn)\n * @param nodes\n * @returns {*}\n */\nvar filterNested = function (nodes) {\n    var contained = new Set();\n    var l = nodes.length;\n    for (var i = 0; i < l; i += 1) {\n        for (var j = i + 1; j < l; j += 1) {\n            var position = nodes[i].compareDocumentPosition(nodes[j]);\n            /* eslint-disable no-bitwise */\n            if ((position & Node.DOCUMENT_POSITION_CONTAINED_BY) > 0) {\n                contained.add(j);\n            }\n            if ((position & Node.DOCUMENT_POSITION_CONTAINS) > 0) {\n                contained.add(i);\n            }\n            /* eslint-enable */\n        }\n    }\n    return nodes.filter(function (_, index) { return !contained.has(index); });\n};\n/**\n * finds top most parent for a node\n * @param node\n * @returns {*}\n */\nvar getTopParent = function (node) {\n    return node.parentNode ? getTopParent(node.parentNode) : node;\n};\n/**\n * returns all \"focus containers\" inside a given node\n * @param node - node or nodes to look inside\n * @returns Element[]\n */\nvar getAllAffectedNodes = function (node) {\n    var nodes = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(node);\n    return nodes.filter(Boolean).reduce(function (acc, currentNode) {\n        var group = currentNode.getAttribute(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_GROUP);\n        acc.push.apply(acc, (group\n            ? filterNested((0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(getTopParent(currentNode).querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_GROUP, \"=\\\"\").concat(group, \"\\\"]:not([\").concat(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_DISABLED, \"=\\\"disabled\\\"])\"))))\n            : [currentNode]));\n        return acc;\n    }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/all-affected.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js":
/*!************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/array.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asArray: () => (/* binding */ asArray),\n/* harmony export */   getFirst: () => (/* binding */ getFirst),\n/* harmony export */   toArray: () => (/* binding */ toArray)\n/* harmony export */ });\n/*\nIE11 support\n */\nvar toArray = function (a) {\n    var ret = Array(a.length);\n    for (var i = 0; i < a.length; ++i) {\n        ret[i] = a[i];\n    }\n    return ret;\n};\nvar asArray = function (a) { return (Array.isArray(a) ? a : [a]); };\nvar getFirst = function (a) { return (Array.isArray(a) ? a[0] : a); };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9hcnJheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0Esb0JBQW9CLGNBQWM7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDTyw2QkFBNkI7QUFDN0IsOEJBQThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvYXJyYXkuanM/ZjY2NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuSUUxMSBzdXBwb3J0XG4gKi9cbmV4cG9ydCB2YXIgdG9BcnJheSA9IGZ1bmN0aW9uIChhKSB7XG4gICAgdmFyIHJldCA9IEFycmF5KGEubGVuZ3RoKTtcbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGEubGVuZ3RoOyArK2kpIHtcbiAgICAgICAgcmV0W2ldID0gYVtpXTtcbiAgICB9XG4gICAgcmV0dXJuIHJldDtcbn07XG5leHBvcnQgdmFyIGFzQXJyYXkgPSBmdW5jdGlvbiAoYSkgeyByZXR1cm4gKEFycmF5LmlzQXJyYXkoYSkgPyBhIDogW2FdKTsgfTtcbmV4cG9ydCB2YXIgZ2V0Rmlyc3QgPSBmdW5jdGlvbiAoYSkgeyByZXR1cm4gKEFycmF5LmlzQXJyYXkoYSkgPyBhWzBdIDogYSk7IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/auto-focus.js":
/*!*****************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/auto-focus.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickAutofocus: () => (/* binding */ pickAutofocus)\n/* harmony export */ });\n/* harmony import */ var _DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMutils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _firstFocus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firstFocus */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/firstFocus.js\");\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js\");\n\n\n\nvar findAutoFocused = function (autoFocusables) {\n    return function (node) {\n        var _a;\n        var autofocus = (_a = (0,_is__WEBPACK_IMPORTED_MODULE_0__.getDataset)(node)) === null || _a === void 0 ? void 0 : _a.autofocus;\n        return (\n        // @ts-expect-error\n        node.autofocus ||\n            //\n            (autofocus !== undefined && autofocus !== 'false') ||\n            //\n            autoFocusables.indexOf(node) >= 0);\n    };\n};\nvar pickAutofocus = function (nodesIndexes, orderedNodes, groups) {\n    var nodes = nodesIndexes.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var autoFocusable = (0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.filterAutoFocusable)(nodes.filter(findAutoFocused(groups)));\n    if (autoFocusable && autoFocusable.length) {\n        return (0,_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFirstFocus)(autoFocusable);\n    }\n    return (0,_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFirstFocus)((0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.filterAutoFocusable)(orderedNodes));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9hdXRvLWZvY3VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUQ7QUFDSDtBQUNaO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwrQ0FBVTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCx3QkFBd0IsOERBQW1CO0FBQzNDO0FBQ0EsZUFBZSwyREFBYztBQUM3QjtBQUNBLFdBQVcsMkRBQWMsQ0FBQyw4REFBbUI7QUFDN0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9hdXRvLWZvY3VzLmpzPzRhZDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZmlsdGVyQXV0b0ZvY3VzYWJsZSB9IGZyb20gJy4vRE9NdXRpbHMnO1xuaW1wb3J0IHsgcGlja0ZpcnN0Rm9jdXMgfSBmcm9tICcuL2ZpcnN0Rm9jdXMnO1xuaW1wb3J0IHsgZ2V0RGF0YXNldCB9IGZyb20gJy4vaXMnO1xudmFyIGZpbmRBdXRvRm9jdXNlZCA9IGZ1bmN0aW9uIChhdXRvRm9jdXNhYmxlcykge1xuICAgIHJldHVybiBmdW5jdGlvbiAobm9kZSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHZhciBhdXRvZm9jdXMgPSAoX2EgPSBnZXREYXRhc2V0KG5vZGUpKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuYXV0b2ZvY3VzO1xuICAgICAgICByZXR1cm4gKFxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yXG4gICAgICAgIG5vZGUuYXV0b2ZvY3VzIHx8XG4gICAgICAgICAgICAvL1xuICAgICAgICAgICAgKGF1dG9mb2N1cyAhPT0gdW5kZWZpbmVkICYmIGF1dG9mb2N1cyAhPT0gJ2ZhbHNlJykgfHxcbiAgICAgICAgICAgIC8vXG4gICAgICAgICAgICBhdXRvRm9jdXNhYmxlcy5pbmRleE9mKG5vZGUpID49IDApO1xuICAgIH07XG59O1xuZXhwb3J0IHZhciBwaWNrQXV0b2ZvY3VzID0gZnVuY3Rpb24gKG5vZGVzSW5kZXhlcywgb3JkZXJlZE5vZGVzLCBncm91cHMpIHtcbiAgICB2YXIgbm9kZXMgPSBub2Rlc0luZGV4ZXMubWFwKGZ1bmN0aW9uIChfYSkge1xuICAgICAgICB2YXIgbm9kZSA9IF9hLm5vZGU7XG4gICAgICAgIHJldHVybiBub2RlO1xuICAgIH0pO1xuICAgIHZhciBhdXRvRm9jdXNhYmxlID0gZmlsdGVyQXV0b0ZvY3VzYWJsZShub2Rlcy5maWx0ZXIoZmluZEF1dG9Gb2N1c2VkKGdyb3VwcykpKTtcbiAgICBpZiAoYXV0b0ZvY3VzYWJsZSAmJiBhdXRvRm9jdXNhYmxlLmxlbmd0aCkge1xuICAgICAgICByZXR1cm4gcGlja0ZpcnN0Rm9jdXMoYXV0b0ZvY3VzYWJsZSk7XG4gICAgfVxuICAgIHJldHVybiBwaWNrRmlyc3RGb2N1cyhmaWx0ZXJBdXRvRm9jdXNhYmxlKG9yZGVyZWROb2RlcykpO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/auto-focus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/correctFocus.js":
/*!*******************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/correctFocus.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   correctNode: () => (/* binding */ correctNode),\n/* harmony export */   correctNodes: () => (/* binding */ correctNodes)\n/* harmony export */ });\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js\");\n\nvar findSelectedRadio = function (node, nodes) {\n    return nodes\n        .filter(_is__WEBPACK_IMPORTED_MODULE_0__.isRadioElement)\n        .filter(function (el) { return el.name === node.name; })\n        .filter(function (el) { return el.checked; })[0] || node;\n};\nvar correctNode = function (node, nodes) {\n    if ((0,_is__WEBPACK_IMPORTED_MODULE_0__.isRadioElement)(node) && node.name) {\n        return findSelectedRadio(node, nodes);\n    }\n    return node;\n};\n/**\n * giving a set of radio inputs keeps only selected (tabbable) ones\n * @param nodes\n */\nvar correctNodes = function (nodes) {\n    // IE11 has no Set(array) constructor\n    var resultSet = new Set();\n    nodes.forEach(function (node) { return resultSet.add(correctNode(node, nodes)); });\n    // using filter to support IE11\n    return nodes.filter(function (node) { return resultSet.has(node); });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9jb3JyZWN0Rm9jdXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNDO0FBQ3RDO0FBQ0E7QUFDQSxnQkFBZ0IsK0NBQWM7QUFDOUIsZ0NBQWdDLCtCQUErQjtBQUMvRCxnQ0FBZ0Msb0JBQW9CO0FBQ3BEO0FBQ087QUFDUCxRQUFRLG1EQUFjO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxvQ0FBb0MsaURBQWlEO0FBQ3JGO0FBQ0EsMENBQTBDLDZCQUE2QjtBQUN2RSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWxzL2NvcnJlY3RGb2N1cy5qcz85YTIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzUmFkaW9FbGVtZW50IH0gZnJvbSAnLi9pcyc7XG52YXIgZmluZFNlbGVjdGVkUmFkaW8gPSBmdW5jdGlvbiAobm9kZSwgbm9kZXMpIHtcbiAgICByZXR1cm4gbm9kZXNcbiAgICAgICAgLmZpbHRlcihpc1JhZGlvRWxlbWVudClcbiAgICAgICAgLmZpbHRlcihmdW5jdGlvbiAoZWwpIHsgcmV0dXJuIGVsLm5hbWUgPT09IG5vZGUubmFtZTsgfSlcbiAgICAgICAgLmZpbHRlcihmdW5jdGlvbiAoZWwpIHsgcmV0dXJuIGVsLmNoZWNrZWQ7IH0pWzBdIHx8IG5vZGU7XG59O1xuZXhwb3J0IHZhciBjb3JyZWN0Tm9kZSA9IGZ1bmN0aW9uIChub2RlLCBub2Rlcykge1xuICAgIGlmIChpc1JhZGlvRWxlbWVudChub2RlKSAmJiBub2RlLm5hbWUpIHtcbiAgICAgICAgcmV0dXJuIGZpbmRTZWxlY3RlZFJhZGlvKG5vZGUsIG5vZGVzKTtcbiAgICB9XG4gICAgcmV0dXJuIG5vZGU7XG59O1xuLyoqXG4gKiBnaXZpbmcgYSBzZXQgb2YgcmFkaW8gaW5wdXRzIGtlZXBzIG9ubHkgc2VsZWN0ZWQgKHRhYmJhYmxlKSBvbmVzXG4gKiBAcGFyYW0gbm9kZXNcbiAqL1xuZXhwb3J0IHZhciBjb3JyZWN0Tm9kZXMgPSBmdW5jdGlvbiAobm9kZXMpIHtcbiAgICAvLyBJRTExIGhhcyBubyBTZXQoYXJyYXkpIGNvbnN0cnVjdG9yXG4gICAgdmFyIHJlc3VsdFNldCA9IG5ldyBTZXQoKTtcbiAgICBub2Rlcy5mb3JFYWNoKGZ1bmN0aW9uIChub2RlKSB7IHJldHVybiByZXN1bHRTZXQuYWRkKGNvcnJlY3ROb2RlKG5vZGUsIG5vZGVzKSk7IH0pO1xuICAgIC8vIHVzaW5nIGZpbHRlciB0byBzdXBwb3J0IElFMTFcbiAgICByZXR1cm4gbm9kZXMuZmlsdGVyKGZ1bmN0aW9uIChub2RlKSB7IHJldHVybiByZXN1bHRTZXQuaGFzKG5vZGUpOyB9KTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/correctFocus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/firstFocus.js":
/*!*****************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/firstFocus.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickFirstFocus: () => (/* binding */ pickFirstFocus),\n/* harmony export */   pickFocusable: () => (/* binding */ pickFocusable)\n/* harmony export */ });\n/* harmony import */ var _correctFocus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./correctFocus */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/correctFocus.js\");\n\nvar pickFirstFocus = function (nodes) {\n    if (nodes[0] && nodes.length > 1) {\n        return (0,_correctFocus__WEBPACK_IMPORTED_MODULE_0__.correctNode)(nodes[0], nodes);\n    }\n    return nodes[0];\n};\nvar pickFocusable = function (nodes, node) {\n    return nodes.indexOf((0,_correctFocus__WEBPACK_IMPORTED_MODULE_0__.correctNode)(node, nodes));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9maXJzdEZvY3VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUN0QztBQUNQO0FBQ0EsZUFBZSwwREFBVztBQUMxQjtBQUNBO0FBQ0E7QUFDTztBQUNQLHlCQUF5QiwwREFBVztBQUNwQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWxzL2ZpcnN0Rm9jdXMuanM/MzAxOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb3JyZWN0Tm9kZSB9IGZyb20gJy4vY29ycmVjdEZvY3VzJztcbmV4cG9ydCB2YXIgcGlja0ZpcnN0Rm9jdXMgPSBmdW5jdGlvbiAobm9kZXMpIHtcbiAgICBpZiAobm9kZXNbMF0gJiYgbm9kZXMubGVuZ3RoID4gMSkge1xuICAgICAgICByZXR1cm4gY29ycmVjdE5vZGUobm9kZXNbMF0sIG5vZGVzKTtcbiAgICB9XG4gICAgcmV0dXJuIG5vZGVzWzBdO1xufTtcbmV4cG9ydCB2YXIgcGlja0ZvY3VzYWJsZSA9IGZ1bmN0aW9uIChub2Rlcywgbm9kZSkge1xuICAgIHJldHVybiBub2Rlcy5pbmRleE9mKGNvcnJlY3ROb2RlKG5vZGUsIG5vZGVzKSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/firstFocus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/getActiveElement.js":
/*!***********************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/getActiveElement.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement)\n/* harmony export */ });\n/* harmony import */ var _safe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./safe */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/safe.js\");\n/**\n * returns active element from document or from nested shadowdoms\n */\n\n/**\n * returns current active element. If the active element is a \"container\" itself(shadowRoot or iframe) returns active element inside it\n * @param [inDocument]\n */\nvar getActiveElement = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    if (!inDocument || !inDocument.activeElement) {\n        return undefined;\n    }\n    var activeElement = inDocument.activeElement;\n    return (activeElement.shadowRoot\n        ? getActiveElement(activeElement.shadowRoot)\n        : activeElement instanceof HTMLIFrameElement && (0,_safe__WEBPACK_IMPORTED_MODULE_0__.safeProbe)(function () { return activeElement.contentWindow.document; })\n            ? getActiveElement(activeElement.contentWindow.document)\n            : activeElement);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9nZXRBY3RpdmVFbGVtZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ21DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELGdEQUFTLGVBQWUsOENBQThDO0FBQzlIO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWxzL2dldEFjdGl2ZUVsZW1lbnQuanM/ZDllNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIHJldHVybnMgYWN0aXZlIGVsZW1lbnQgZnJvbSBkb2N1bWVudCBvciBmcm9tIG5lc3RlZCBzaGFkb3dkb21zXG4gKi9cbmltcG9ydCB7IHNhZmVQcm9iZSB9IGZyb20gJy4vc2FmZSc7XG4vKipcbiAqIHJldHVybnMgY3VycmVudCBhY3RpdmUgZWxlbWVudC4gSWYgdGhlIGFjdGl2ZSBlbGVtZW50IGlzIGEgXCJjb250YWluZXJcIiBpdHNlbGYoc2hhZG93Um9vdCBvciBpZnJhbWUpIHJldHVybnMgYWN0aXZlIGVsZW1lbnQgaW5zaWRlIGl0XG4gKiBAcGFyYW0gW2luRG9jdW1lbnRdXG4gKi9cbmV4cG9ydCB2YXIgZ2V0QWN0aXZlRWxlbWVudCA9IGZ1bmN0aW9uIChpbkRvY3VtZW50KSB7XG4gICAgaWYgKGluRG9jdW1lbnQgPT09IHZvaWQgMCkgeyBpbkRvY3VtZW50ID0gZG9jdW1lbnQ7IH1cbiAgICBpZiAoIWluRG9jdW1lbnQgfHwgIWluRG9jdW1lbnQuYWN0aXZlRWxlbWVudCkge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbiAgICB2YXIgYWN0aXZlRWxlbWVudCA9IGluRG9jdW1lbnQuYWN0aXZlRWxlbWVudDtcbiAgICByZXR1cm4gKGFjdGl2ZUVsZW1lbnQuc2hhZG93Um9vdFxuICAgICAgICA/IGdldEFjdGl2ZUVsZW1lbnQoYWN0aXZlRWxlbWVudC5zaGFkb3dSb290KVxuICAgICAgICA6IGFjdGl2ZUVsZW1lbnQgaW5zdGFuY2VvZiBIVE1MSUZyYW1lRWxlbWVudCAmJiBzYWZlUHJvYmUoZnVuY3Rpb24gKCkgeyByZXR1cm4gYWN0aXZlRWxlbWVudC5jb250ZW50V2luZG93LmRvY3VtZW50OyB9KVxuICAgICAgICAgICAgPyBnZXRBY3RpdmVFbGVtZW50KGFjdGl2ZUVsZW1lbnQuY29udGVudFdpbmRvdy5kb2N1bWVudClcbiAgICAgICAgICAgIDogYWN0aXZlRWxlbWVudCk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js":
/*!*********************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/is.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDataset: () => (/* binding */ getDataset),\n/* harmony export */   isAutoFocusAllowed: () => (/* binding */ isAutoFocusAllowed),\n/* harmony export */   isAutoFocusAllowedCached: () => (/* binding */ isAutoFocusAllowedCached),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isGuard: () => (/* binding */ isGuard),\n/* harmony export */   isHTMLButtonElement: () => (/* binding */ isHTMLButtonElement),\n/* harmony export */   isHTMLInputElement: () => (/* binding */ isHTMLInputElement),\n/* harmony export */   isNotAGuard: () => (/* binding */ isNotAGuard),\n/* harmony export */   isRadioElement: () => (/* binding */ isRadioElement),\n/* harmony export */   isVisibleCached: () => (/* binding */ isVisibleCached),\n/* harmony export */   notHiddenInput: () => (/* binding */ notHiddenInput)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n\nvar isElementHidden = function (node) {\n    // we can measure only \"elements\"\n    // consider others as \"visible\"\n    if (node.nodeType !== Node.ELEMENT_NODE) {\n        return false;\n    }\n    var computedStyle = window.getComputedStyle(node, null);\n    if (!computedStyle || !computedStyle.getPropertyValue) {\n        return false;\n    }\n    return (computedStyle.getPropertyValue('display') === 'none' || computedStyle.getPropertyValue('visibility') === 'hidden');\n};\nvar getParentNode = function (node) {\n    // DOCUMENT_FRAGMENT_NODE can also point on ShadowRoot. In this case .host will point on the next node\n    return node.parentNode && node.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE\n        ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            node.parentNode.host\n        : node.parentNode;\n};\nvar isTopNode = function (node) {\n    // @ts-ignore\n    return node === document || (node && node.nodeType === Node.DOCUMENT_NODE);\n};\nvar isInert = function (node) { return node.hasAttribute('inert'); };\n/**\n * @see https://github.com/testing-library/jest-dom/blob/main/src/to-be-visible.js\n */\nvar isVisibleUncached = function (node, checkParent) {\n    return !node || isTopNode(node) || (!isElementHidden(node) && !isInert(node) && checkParent(getParentNode(node)));\n};\nvar isVisibleCached = function (visibilityCache, node) {\n    var cached = visibilityCache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isVisibleUncached(node, isVisibleCached.bind(undefined, visibilityCache));\n    visibilityCache.set(node, result);\n    return result;\n};\nvar isAutoFocusAllowedUncached = function (node, checkParent) {\n    return node && !isTopNode(node) ? (isAutoFocusAllowed(node) ? checkParent(getParentNode(node)) : false) : true;\n};\nvar isAutoFocusAllowedCached = function (cache, node) {\n    var cached = cache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isAutoFocusAllowedUncached(node, isAutoFocusAllowedCached.bind(undefined, cache));\n    cache.set(node, result);\n    return result;\n};\nvar getDataset = function (node) {\n    // @ts-ignore\n    return node.dataset;\n};\nvar isHTMLButtonElement = function (node) { return node.tagName === 'BUTTON'; };\nvar isHTMLInputElement = function (node) { return node.tagName === 'INPUT'; };\nvar isRadioElement = function (node) {\n    return isHTMLInputElement(node) && node.type === 'radio';\n};\nvar notHiddenInput = function (node) {\n    return !((isHTMLInputElement(node) || isHTMLButtonElement(node)) && (node.type === 'hidden' || node.disabled));\n};\nvar isAutoFocusAllowed = function (node) {\n    var attribute = node.getAttribute(_constants__WEBPACK_IMPORTED_MODULE_0__.FOCUS_NO_AUTOFOCUS);\n    return ![true, 'true', ''].includes(attribute);\n};\nvar isGuard = function (node) { var _a; return Boolean(node && ((_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.focusGuard)); };\nvar isNotAGuard = function (node) { return !isGuard(node); };\nvar isDefined = function (x) { return Boolean(x); };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/is.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/parenting.js":
/*!****************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/parenting.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allParentAutofocusables: () => (/* binding */ allParentAutofocusables),\n/* harmony export */   getCommonParent: () => (/* binding */ getCommonParent),\n/* harmony export */   getTopCommonParent: () => (/* binding */ getTopCommonParent)\n/* harmony export */ });\n/* harmony import */ var _DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMutils */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n\nvar getParents = function (node, parents) {\n    if (parents === void 0) { parents = []; }\n    parents.push(node);\n    if (node.parentNode) {\n        getParents(node.parentNode.host || node.parentNode, parents);\n    }\n    return parents;\n};\n/**\n * finds a parent for both nodeA and nodeB\n * @param nodeA\n * @param nodeB\n * @returns {boolean|*}\n */\nvar getCommonParent = function (nodeA, nodeB) {\n    var parentsA = getParents(nodeA);\n    var parentsB = getParents(nodeB);\n    // tslint:disable-next-line:prefer-for-of\n    for (var i = 0; i < parentsA.length; i += 1) {\n        var currentParent = parentsA[i];\n        if (parentsB.indexOf(currentParent) >= 0) {\n            return currentParent;\n        }\n    }\n    return false;\n};\nvar getTopCommonParent = function (baseActiveElement, leftEntry, rightEntries) {\n    var activeElements = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(baseActiveElement);\n    var leftEntries = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(leftEntry);\n    var activeElement = activeElements[0];\n    var topCommon = false;\n    leftEntries.filter(Boolean).forEach(function (entry) {\n        topCommon = getCommonParent(topCommon || entry, entry) || topCommon;\n        rightEntries.filter(Boolean).forEach(function (subEntry) {\n            var common = getCommonParent(activeElement, subEntry);\n            if (common) {\n                if (!topCommon || (0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.contains)(common, topCommon)) {\n                    topCommon = common;\n                }\n                else {\n                    topCommon = getCommonParent(common, topCommon);\n                }\n            }\n        });\n    });\n    // TODO: add assert here?\n    return topCommon;\n};\n/**\n * return list of nodes which are expected to be autofocused inside a given top nodes\n * @param entries\n * @param visibilityCache\n */\nvar allParentAutofocusables = function (entries, visibilityCache) {\n    return entries.reduce(function (acc, node) { return acc.concat((0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.parentAutofocusables)(node, visibilityCache)); }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/parenting.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/safe.js":
/*!***********************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/safe.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safeProbe: () => (/* binding */ safeProbe)\n/* harmony export */ });\nvar safeProbe = function (cb) {\n    try {\n        return cb();\n    }\n    catch (e) {\n        return undefined;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9zYWZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvc2FmZS5qcz83ZDUxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgc2FmZVByb2JlID0gZnVuY3Rpb24gKGNiKSB7XG4gICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIGNiKCk7XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/safe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabOrder.js":
/*!***************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/tabOrder.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orderByTabIndex: () => (/* binding */ orderByTabIndex),\n/* harmony export */   tabSort: () => (/* binding */ tabSort)\n/* harmony export */ });\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n\nvar tabSort = function (a, b) {\n    var aTab = Math.max(0, a.tabIndex);\n    var bTab = Math.max(0, b.tabIndex);\n    var tabDiff = aTab - bTab;\n    var indexDiff = a.index - b.index;\n    if (tabDiff) {\n        if (!aTab) {\n            return 1;\n        }\n        if (!bTab) {\n            return -1;\n        }\n    }\n    return tabDiff || indexDiff;\n};\nvar getTabIndex = function (node) {\n    if (node.tabIndex < 0) {\n        // all \"focusable\" elements are already preselected\n        // but some might have implicit negative tabIndex\n        // return 0 for <audio without tabIndex attribute - it is \"tabbable\"\n        if (!node.hasAttribute('tabindex')) {\n            return 0;\n        }\n    }\n    return node.tabIndex;\n};\nvar orderByTabIndex = function (nodes, filterNegative, keepGuards) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes)\n        .map(function (node, index) {\n        var tabIndex = getTabIndex(node);\n        return {\n            node: node,\n            index: index,\n            tabIndex: keepGuards && tabIndex === -1 ? ((node.dataset || {}).focusGuard ? 0 : -1) : tabIndex,\n        };\n    })\n        .filter(function (data) { return !filterNegative || data.tabIndex >= 0; })\n        .sort(tabSort);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabOrder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabUtils.js":
/*!***************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/tabUtils.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFocusables: () => (/* binding */ getFocusables),\n/* harmony export */   getParentAutofocusables: () => (/* binding */ getParentAutofocusables)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./array */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _tabbables__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tabbables */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabbables.js\");\n\n\n\nvar queryTabbables = _tabbables__WEBPACK_IMPORTED_MODULE_0__.tabbables.join(',');\nvar queryGuardTabbables = \"\".concat(queryTabbables, \", [data-focus-guard]\");\nvar getFocusablesWithShadowDom = function (parent, withGuards) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)((parent.shadowRoot || parent).children).reduce(function (acc, child) {\n        return acc.concat(child.matches(withGuards ? queryGuardTabbables : queryTabbables) ? [child] : [], getFocusablesWithShadowDom(child));\n    }, []);\n};\nvar getFocusablesWithIFrame = function (parent, withGuards) {\n    var _a;\n    // contentDocument of iframe will be null if current origin cannot access it\n    if (parent instanceof HTMLIFrameElement && ((_a = parent.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n        return getFocusables([parent.contentDocument.body], withGuards);\n    }\n    return [parent];\n};\nvar getFocusables = function (parents, withGuards) {\n    return parents.reduce(function (acc, parent) {\n        var _a;\n        var focusableWithShadowDom = getFocusablesWithShadowDom(parent, withGuards);\n        var focusableWithIframes = (_a = []).concat.apply(_a, focusableWithShadowDom.map(function (node) { return getFocusablesWithIFrame(node, withGuards); }));\n        return acc.concat(\n        // add all tabbables inside and within shadow DOMs in DOM order\n        focusableWithIframes, \n        // add if node is tabbable itself\n        parent.parentNode\n            ? (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(parent.parentNode.querySelectorAll(queryTabbables)).filter(function (node) { return node === parent; })\n            : []);\n    }, []);\n};\n/**\n * return a list of focusable nodes within an area marked as \"auto-focusable\"\n * @param parent\n */\nvar getParentAutofocusables = function (parent) {\n    var parentFocus = parent.querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_2__.FOCUS_AUTO, \"]\"));\n    return (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(parentFocus)\n        .map(function (node) { return getFocusables([node]); })\n        .reduce(function (acc, nodes) { return acc.concat(nodes); }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy90YWJVdGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQztBQUNSO0FBQ007QUFDeEMscUJBQXFCLGlEQUFTO0FBQzlCO0FBQ0E7QUFDQSxXQUFXLCtDQUFPO0FBQ2xCO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsMkdBQTJHLG1EQUFtRDtBQUM5SjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYywrQ0FBTyw4RUFBOEUseUJBQXlCO0FBQzVIO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLHlEQUF5RCxrREFBVTtBQUNuRSxXQUFXLCtDQUFPO0FBQ2xCLCtCQUErQiwrQkFBK0I7QUFDOUQsd0NBQXdDLDJCQUEyQjtBQUNuRSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWxzL3RhYlV0aWxzLmpzPzVlOGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRk9DVVNfQVVUTyB9IGZyb20gJy4uL2NvbnN0YW50cyc7XG5pbXBvcnQgeyB0b0FycmF5IH0gZnJvbSAnLi9hcnJheSc7XG5pbXBvcnQgeyB0YWJiYWJsZXMgfSBmcm9tICcuL3RhYmJhYmxlcyc7XG52YXIgcXVlcnlUYWJiYWJsZXMgPSB0YWJiYWJsZXMuam9pbignLCcpO1xudmFyIHF1ZXJ5R3VhcmRUYWJiYWJsZXMgPSBcIlwiLmNvbmNhdChxdWVyeVRhYmJhYmxlcywgXCIsIFtkYXRhLWZvY3VzLWd1YXJkXVwiKTtcbnZhciBnZXRGb2N1c2FibGVzV2l0aFNoYWRvd0RvbSA9IGZ1bmN0aW9uIChwYXJlbnQsIHdpdGhHdWFyZHMpIHtcbiAgICByZXR1cm4gdG9BcnJheSgocGFyZW50LnNoYWRvd1Jvb3QgfHwgcGFyZW50KS5jaGlsZHJlbikucmVkdWNlKGZ1bmN0aW9uIChhY2MsIGNoaWxkKSB7XG4gICAgICAgIHJldHVybiBhY2MuY29uY2F0KGNoaWxkLm1hdGNoZXMod2l0aEd1YXJkcyA/IHF1ZXJ5R3VhcmRUYWJiYWJsZXMgOiBxdWVyeVRhYmJhYmxlcykgPyBbY2hpbGRdIDogW10sIGdldEZvY3VzYWJsZXNXaXRoU2hhZG93RG9tKGNoaWxkKSk7XG4gICAgfSwgW10pO1xufTtcbnZhciBnZXRGb2N1c2FibGVzV2l0aElGcmFtZSA9IGZ1bmN0aW9uIChwYXJlbnQsIHdpdGhHdWFyZHMpIHtcbiAgICB2YXIgX2E7XG4gICAgLy8gY29udGVudERvY3VtZW50IG9mIGlmcmFtZSB3aWxsIGJlIG51bGwgaWYgY3VycmVudCBvcmlnaW4gY2Fubm90IGFjY2VzcyBpdFxuICAgIGlmIChwYXJlbnQgaW5zdGFuY2VvZiBIVE1MSUZyYW1lRWxlbWVudCAmJiAoKF9hID0gcGFyZW50LmNvbnRlbnREb2N1bWVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmJvZHkpKSB7XG4gICAgICAgIHJldHVybiBnZXRGb2N1c2FibGVzKFtwYXJlbnQuY29udGVudERvY3VtZW50LmJvZHldLCB3aXRoR3VhcmRzKTtcbiAgICB9XG4gICAgcmV0dXJuIFtwYXJlbnRdO1xufTtcbmV4cG9ydCB2YXIgZ2V0Rm9jdXNhYmxlcyA9IGZ1bmN0aW9uIChwYXJlbnRzLCB3aXRoR3VhcmRzKSB7XG4gICAgcmV0dXJuIHBhcmVudHMucmVkdWNlKGZ1bmN0aW9uIChhY2MsIHBhcmVudCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHZhciBmb2N1c2FibGVXaXRoU2hhZG93RG9tID0gZ2V0Rm9jdXNhYmxlc1dpdGhTaGFkb3dEb20ocGFyZW50LCB3aXRoR3VhcmRzKTtcbiAgICAgICAgdmFyIGZvY3VzYWJsZVdpdGhJZnJhbWVzID0gKF9hID0gW10pLmNvbmNhdC5hcHBseShfYSwgZm9jdXNhYmxlV2l0aFNoYWRvd0RvbS5tYXAoZnVuY3Rpb24gKG5vZGUpIHsgcmV0dXJuIGdldEZvY3VzYWJsZXNXaXRoSUZyYW1lKG5vZGUsIHdpdGhHdWFyZHMpOyB9KSk7XG4gICAgICAgIHJldHVybiBhY2MuY29uY2F0KFxuICAgICAgICAvLyBhZGQgYWxsIHRhYmJhYmxlcyBpbnNpZGUgYW5kIHdpdGhpbiBzaGFkb3cgRE9NcyBpbiBET00gb3JkZXJcbiAgICAgICAgZm9jdXNhYmxlV2l0aElmcmFtZXMsIFxuICAgICAgICAvLyBhZGQgaWYgbm9kZSBpcyB0YWJiYWJsZSBpdHNlbGZcbiAgICAgICAgcGFyZW50LnBhcmVudE5vZGVcbiAgICAgICAgICAgID8gdG9BcnJheShwYXJlbnQucGFyZW50Tm9kZS5xdWVyeVNlbGVjdG9yQWxsKHF1ZXJ5VGFiYmFibGVzKSkuZmlsdGVyKGZ1bmN0aW9uIChub2RlKSB7IHJldHVybiBub2RlID09PSBwYXJlbnQ7IH0pXG4gICAgICAgICAgICA6IFtdKTtcbiAgICB9LCBbXSk7XG59O1xuLyoqXG4gKiByZXR1cm4gYSBsaXN0IG9mIGZvY3VzYWJsZSBub2RlcyB3aXRoaW4gYW4gYXJlYSBtYXJrZWQgYXMgXCJhdXRvLWZvY3VzYWJsZVwiXG4gKiBAcGFyYW0gcGFyZW50XG4gKi9cbmV4cG9ydCB2YXIgZ2V0UGFyZW50QXV0b2ZvY3VzYWJsZXMgPSBmdW5jdGlvbiAocGFyZW50KSB7XG4gICAgdmFyIHBhcmVudEZvY3VzID0gcGFyZW50LnF1ZXJ5U2VsZWN0b3JBbGwoXCJbXCIuY29uY2F0KEZPQ1VTX0FVVE8sIFwiXVwiKSk7XG4gICAgcmV0dXJuIHRvQXJyYXkocGFyZW50Rm9jdXMpXG4gICAgICAgIC5tYXAoZnVuY3Rpb24gKG5vZGUpIHsgcmV0dXJuIGdldEZvY3VzYWJsZXMoW25vZGVdKTsgfSlcbiAgICAgICAgLnJlZHVjZShmdW5jdGlvbiAoYWNjLCBub2RlcykgeyByZXR1cm4gYWNjLmNvbmNhdChub2Rlcyk7IH0sIFtdKTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabbables.js":
/*!****************************************************************!*\
  !*** ./node_modules/focus-lock/dist/es2015/utils/tabbables.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tabbables: () => (/* binding */ tabbables)\n/* harmony export */ });\n/**\n * list of the object to be considered as focusable\n */\nvar tabbables = [\n    'button:enabled',\n    'select:enabled',\n    'textarea:enabled',\n    'input:enabled',\n    // elements with explicit roles will also use explicit tabindex\n    // '[role=\"button\"]',\n    'a[href]',\n    'area[href]',\n    'summary',\n    'iframe',\n    'object',\n    'embed',\n    'audio[controls]',\n    'video[controls]',\n    '[tabindex]',\n    '[contenteditable]',\n    '[autofocus]',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy90YWJiYWJsZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvdGFiYmFibGVzLmpzP2U3NDgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBsaXN0IG9mIHRoZSBvYmplY3QgdG8gYmUgY29uc2lkZXJlZCBhcyBmb2N1c2FibGVcbiAqL1xuZXhwb3J0IHZhciB0YWJiYWJsZXMgPSBbXG4gICAgJ2J1dHRvbjplbmFibGVkJyxcbiAgICAnc2VsZWN0OmVuYWJsZWQnLFxuICAgICd0ZXh0YXJlYTplbmFibGVkJyxcbiAgICAnaW5wdXQ6ZW5hYmxlZCcsXG4gICAgLy8gZWxlbWVudHMgd2l0aCBleHBsaWNpdCByb2xlcyB3aWxsIGFsc28gdXNlIGV4cGxpY2l0IHRhYmluZGV4XG4gICAgLy8gJ1tyb2xlPVwiYnV0dG9uXCJdJyxcbiAgICAnYVtocmVmXScsXG4gICAgJ2FyZWFbaHJlZl0nLFxuICAgICdzdW1tYXJ5JyxcbiAgICAnaWZyYW1lJyxcbiAgICAnb2JqZWN0JyxcbiAgICAnZW1iZWQnLFxuICAgICdhdWRpb1tjb250cm9sc10nLFxuICAgICd2aWRlb1tjb250cm9sc10nLFxuICAgICdbdGFiaW5kZXhdJyxcbiAgICAnW2NvbnRlbnRlZGl0YWJsZV0nLFxuICAgICdbYXV0b2ZvY3VzXScsXG5dO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/focus-lock/dist/es2015/utils/tabbables.js\n");

/***/ })

};
;