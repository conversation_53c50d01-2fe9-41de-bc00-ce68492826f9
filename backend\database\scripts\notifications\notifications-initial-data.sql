-- Initial Data for Notification System
-- Insert default notification types and templates

-- Insert Notification Types
INSERT INTO NotificationTypes (TypeName, DisplayName, Description, IconName, ColorScheme) VALUES
('TIME_UPDATE', 'Time Update', 'Notifications for delivery/installer time updates', 'FiClock', 'blue'),
('FORM_SUBMISSION', 'Form Submission', 'Notifications for form submissions', 'FiFileText', 'green'),
('STATUS_CHANGE', 'Status Change', 'Notifications for status changes', 'FiRefreshCw', 'orange'),
('SYSTEM_ALERT', 'System Alert', 'System-wide alerts and announcements', 'FiAlertTriangle', 'red'),
('TASK_ASSIGNMENT', 'Task Assignment', 'Notifications for task assignments', 'FiUserPlus', 'purple'),
('DEADLINE_REMINDER', 'Deadline Reminder', 'Reminders for upcoming deadlines', 'FiBell', 'yellow');

-- Insert Notification Templates for Time Updates
INSERT INTO NotificationTemplates (TypeID, TemplateName, TitleTemplate, MessageTemplate, ActionButtonText, ActionUrl, Priority) VALUES
(
    (SELECT ID FROM NotificationTypes WHERE TypeName = 'TIME_UPDATE'),
    'DELIVERY_TIME_UPDATE',
    'Delivery Time Updated - {quotationNo}',
    '{employeeName} has updated the delivery time for quotation {quotationNo} (Client: {clientName}). New time: {newTime}. Reason: {reason}',
    'View Details',
    '/forms/delivery-report/follow-up?voucher_No={quotationNo}',
    2
),
(
    (SELECT ID FROM NotificationTypes WHERE TypeName = 'TIME_UPDATE'),
    'INSTALLER_TIME_UPDATE',
    'Installation Time Updated - {quotationNo}',
    '{employeeName} has updated the installation time for quotation {quotationNo} (Client: {clientName}). New time: {newTime}. Reason: {reason}',
    'View Details',
    '/forms/installer-report/follow-up?voucher_No={quotationNo}',
    2
);

-- Insert Templates for Future Use Cases
INSERT INTO NotificationTemplates (TypeID, TemplateName, TitleTemplate, MessageTemplate, ActionButtonText, ActionUrl, Priority) VALUES
(
    (SELECT ID FROM NotificationTypes WHERE TypeName = 'FORM_SUBMISSION'),
    'BUSINESS_LEAD_SUBMITTED',
    'New Business Lead Submitted',
    'A new business lead has been submitted by {employeeName} for client {clientName}.',
    'Review Lead',
    '/forms/business-leads',
    2
),
(
    (SELECT ID FROM NotificationTypes WHERE TypeName = 'STATUS_CHANGE'),
    'TASK_STATUS_CHANGED',
    'Task Status Changed - {quotationNo}',
    'Task status for quotation {quotationNo} has been changed from {oldStatus} to {newStatus} by {employeeName}.',
    'View Task',
    '/dashboard',
    1
),
(
    (SELECT ID FROM NotificationTypes WHERE TypeName = 'SYSTEM_ALERT'),
    'SYSTEM_MAINTENANCE',
    'System Maintenance Alert',
    'System maintenance is scheduled for {maintenanceDate}. Expected downtime: {duration}.',
    'Learn More',
    '/system-alerts',
    3
),
(
    (SELECT ID FROM NotificationTypes WHERE TypeName = 'TASK_ASSIGNMENT'),
    'NEW_TASK_ASSIGNED',
    'New Task Assigned - {quotationNo}',
    'You have been assigned a new {taskType} task for quotation {quotationNo} (Client: {clientName}). Due date: {dueDate}',
    'View Task',
    '/dashboard',
    2
),
(
    (SELECT ID FROM NotificationTypes WHERE TypeName = 'DEADLINE_REMINDER'),
    'TASK_DEADLINE_REMINDER',
    'Task Deadline Reminder - {quotationNo}',
    'Reminder: Your {taskType} task for quotation {quotationNo} is due {timeRemaining}.',
    'View Task',
    '/dashboard',
    2
);

-- Create a view for easy notification querying
CREATE VIEW vw_NotificationDetails AS
SELECT 
    n.ID,
    n.Title,
    n.Message,
    n.ActionButtonText,
    n.ActionUrl,
    n.Priority,
    n.SenderUserID,
    n.SenderEmployeeID,
    n.ContextData,
    n.ReferenceID,
    n.ReferenceType,
    n.CreatedAt,
    n.ExpiresAt,
    nt.TypeName,
    nt.DisplayName as TypeDisplayName,
    nt.IconName,
    nt.ColorScheme,
    sender_user.user_name as SenderUserName,
    sender_emp.Title as SenderEmployeeName,
    nr.RecipientUserID,
    nr.RecipientRoleID,
    nr.RecipientEmployeeID,
    nr.IsRead,
    nr.ReadAt,
    nr.IsArchived,
    nr.ArchivedAt,
    recipient_user.user_name as RecipientUserName,
    recipient_role.RoleName as RecipientRoleName,
    recipient_emp.Title as RecipientEmployeeName
FROM Notifications n
INNER JOIN NotificationTypes nt ON n.TypeID = nt.ID
LEFT JOIN users sender_user ON n.SenderUserID = sender_user.id
LEFT JOIN EmployeeDetails sender_emp ON n.SenderEmployeeID = sender_emp.ID
INNER JOIN NotificationRecipients nr ON n.ID = nr.NotificationID
LEFT JOIN users recipient_user ON nr.RecipientUserID = recipient_user.id
LEFT JOIN User_Role recipient_role ON nr.RecipientRoleID = recipient_role.RoleID
LEFT JOIN EmployeeDetails recipient_emp ON nr.RecipientEmployeeID = recipient_emp.ID
WHERE nt.IsActive = 1;
