import { Box, Table, Thead, Tbody, Tr, Th, Td, Text, Divider, Flex, Spacer } from "@chakra-ui/react";
import { useUser } from "@src/app/provider/UserContext";
import dayjs from "dayjs";

const ComponentToPrint = ({ innerRef, data }) => {
    const { user } = useUser();
    const offerMap = new Map(data?.offers.map(offer => [offer.voucherNo, offer.total]));

    const calculateTotals = () => {
        if (!data?.items || !data?.offers) return { totalOfferAmount: 0, amountReceived: 0, remainingAmount: 0 };
        const totalOfferAmount = data.items.reduce((acc, item) => {
            if (offerMap.has(item.adj_VoucherNo)) {
                const offerAmount = offerMap.get(item.adj_VoucherNo) || 0;
                return acc + offerAmount;
            }
            return acc;
        }, 0);

        const totalAmount = data?.items?.map(item => item.TotalAmt).reduce((acc, cur) => acc + cur, 0) || 0;
        const remainingAmount = totalOfferAmount - totalAmount;

        return { totalOfferAmount, totalAmount, remainingAmount };
    };

    const { totalOfferAmount, totalAmount, remainingAmount } = calculateTotals();

    return (
        <Box ref={innerRef} p={8} maxW="800px" mx="auto">

            <Text><strong>Voucher No:</strong> {data?.Voucher_No}</Text>
            <Text><strong>Voucher Date:</strong> {dayjs(data?.Dated).format('DD MMM, YYYY')}</Text>
            <Text><strong>Cash Account:</strong> {data?.bc_name + ` (${data?.bc_id})` || "N/A"}</Text>

            <Table variant="simple" size="sm" marginTop={'40px !important'} marginBottom={'20px !important'}>
                <Thead>
                    <Tr>
                        <Th>Account Code</Th>
                        <Th>Account Title</Th>
                        <Th>Quotation No.</Th>
                        <Th>Amt Total</Th>
                        <Th>Amt Received</Th>
                        <Th>Amt Pending</Th>
                    </Tr>
                </Thead>
                <Tbody>
                    {data?.items?.map((item, index) => (
                        <Tr key={index}>
                            <Td>{item?.acc_id || "N/A"}</Td>
                            <Td>{item?.acc_name || "N/A"}</Td>
                            <Td>{item?.adj_VoucherNo || "N/A"}</Td>
                            <Td>{offerMap.get(item.adj_VoucherNo) || 0.00}</Td>
                            <Td>{item?.TotalAmt || 0.00}</Td>
                            <Td>{(offerMap.get(item.adj_VoucherNo) || 0.00) - (item?.TotalAmt || 0.00)}</Td>
                        </Tr>
                    ))}
                    <Tr>
                        <Td colSpan={3}>Total Amount</Td>
                        <Td>{totalOfferAmount.toFixed(2)}</Td>
                        <Td>{totalAmount.toFixed(2)}</Td>
                        <Td>{remainingAmount.toFixed(2)}</Td>
                    </Tr>
                </Tbody>
            </Table>
            <Text><strong>Narration:</strong> {data?.nara || "N/A"}</Text>

            <Flex marginTop={'50px !important'}>
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Prepared by</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Checked by</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Approved by</Text>
                </Flex>
            </Flex>
            <Flex marginTop={'30px !important'} alignItems={'flex-end'}>
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text textTransform={'capitalize'}>{user?.userName || 'N/A'}</Text>
                    <Text>_____________________</Text>
                    <Text>User</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Receipient Signature</Text>
                </Flex>
            </Flex>
        </Box>
    );
};

export default ComponentToPrint;
