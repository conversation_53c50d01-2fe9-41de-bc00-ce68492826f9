"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useUser } from '@src/app/provider/UserContext';
import { capitalizeWords, getData } from "@src/app/utils/functions";
import {
  Box,
  Grid,
  Card,
  CardBody,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  useColorModeValue,
  Icon,
  Button,
  Select,
  Flex,
  Container,
  Heading,
  Badge,
} from "@chakra-ui/react";
import { FiUsers, FiFileText, FiUserCheck, FiTrendingUp, FiCheckCircle, FiXCircle, FiClock } from "react-icons/fi";
import { keyframes } from "@emotion/react";
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, Legend
} from 'recharts';

// Define animations
const fadeInUp = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

const scaleIn = keyframes`
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
`;

const shimmer = keyframes`
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
`;

const MainDashboard = () => {
  const { user } = useUser();
  const [lead, setLead] = useState('');
  const [stats, setStats] = useState({
    totalContacts: 0,
    totalPurposes: 0,
    totalUsers: 0,
    approvedPurposes: 0,
    rejectedPurposes: 0,
    pendingPurposes: 0,
  });

  const cardBg = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("gray.700", "white");
  const gradientBg = useColorModeValue(
    "linear-gradient(135deg, #f6f8ff 0%, #ffffff 100%)",
    "linear-gradient(135deg, #2d3748 0%, #1a202c 100%)"
  );

  // Mock data for charts
  const userGrowthData = [
    { month: 'Jan', users: 5 },
    { month: 'Feb', users: 8 },
    { month: 'Mar', users: 12 },
    { month: 'Apr', users: 15 },
    { month: 'May', users: 17 },
    { month: 'Jun', users: 19 },
  ];

  const purposesStatusData = [
    { name: 'Approved', value: 18 },
    { name: 'Rejected', value: 1 },
    { name: 'Pending', value: 14 },
  ];
  const purposesColors = ['#38A169', '#E53E3E', '#ECC94B'];

  const contactsData = [
    { month: 'Jan', contacts: 120 },
    { month: 'Feb', contacts: 150 },
    { month: 'Mar', contacts: 180 },
    { month: 'Apr', contacts: 210 },
    { month: 'May', contacts: 250 },
    { month: 'Jun', contacts: 917 },
  ];

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await getData("getRecords/stats");
        setStats(data[0]);
      } catch (error) {
        console.error("Error fetching stats:", error);
      }
    };

    fetchStats();
  }, []);

  const StatCard = ({ title, value, icon, helpText, delay }) => (
    <Card 
      bg={cardBg} 
      boxShadow="lg"
      borderRadius="2xl"
      overflow="hidden"
      position="relative"
      sx={{
        animation: `${fadeInUp} 0.6s ease-out forwards`,
        animationDelay: `${delay}s`,
        opacity: 0,
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: '2xl',
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '4px',
          background: 'linear-gradient(90deg, #3a866a 0%, #2d6651 100%)',
        },
        transition: 'all 0.3s ease'
      }}
    >
      <CardBody p={6}>
        <Stat>
          <Flex justify="space-between" align="center">
            <Box flex="1">
              <StatLabel 
                color="gray.500" 
                fontSize="sm" 
                fontWeight="medium"
                mb={2}
              >
                {title}
              </StatLabel>
              <StatNumber 
                color={textColor} 
                fontSize="3xl" 
                fontWeight="bold"
                bgGradient="linear(to-r, #3a866a, #2d6651)"
                bgClip="text"
                sx={{
                  animation: `${scaleIn} 0.5s ease-out forwards`,
                  animationDelay: `${delay + 0.2}s`,
                }}
              >
                {value}
              </StatNumber>
              {helpText && (
                <StatHelpText 
                  color="gray.500" 
                  mt={2}
                  display="flex"
                  gap={2}
                  flexWrap="wrap"
                >
                  {helpText.split('•').map((text, i) => (
                    <Badge
                      key={i}
                      colorScheme={
                        text.includes('Approved') ? 'green' : 
                        text.includes('Rejected') ? 'red' : 
                        'yellow'
                      }
                      variant="subtle"
                      px={2}
                      py={1}
                      borderRadius="full"
                    >
                      {text.trim()}
                    </Badge>
                  ))}
                </StatHelpText>
              )}
            </Box>
            <Box
              p={3}
              bg="rgba(58, 134, 106, 0.1)"
              borderRadius="xl"
              sx={{
                animation: `${scaleIn} 0.5s ease-out forwards`,
                animationDelay: `${delay + 0.4}s`,
              }}
            >
              <Icon 
                as={icon} 
                w={8} 
                h={8} 
                color="#3a866a"
              />
            </Box>
          </Flex>
        </Stat>
      </CardBody>
    </Card>
  );

  return (
    <Box 
      bgGradient="linear(to-br, gray.50, white)"
      minH="100vh"
      py={8}
    >
      <Container maxW="1400px">
        <Box
          bg={gradientBg}
          borderRadius="2xl"
          p={8}
          mb={8}
          boxShadow="lg"
          sx={{
            animation: `${fadeInUp} 0.6s ease-out`,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
              animation: `${shimmer} 2s infinite`,
            }
          }}
        >
          <Heading
            as="h1"
            size="lg"
            mb={4}
            bgGradient="linear(to-r, #3a866a, #2d6651)"
            bgClip="text"
          >
            Welcome to NexSol&apos;s ERP, {user?.userName ? capitalizeWords(user.userName) : 'Anonymous'}
          </Heading>
          <Text
            fontSize="md"
            color="gray.600"
            maxW="800px"
            lineHeight="tall"
          >
            Discover your tailored ERP application! Every feature is designed to show how NexSol 
            streamlines your daily operations, enhances business efficiency, and eliminates the 
            manual processes that slow you down.
          </Text>
        </Box>

        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
          <StatCard
            title="Total Contacts"
            value={stats?.totalContacts}
            icon={FiUsers}
            delay={0.2}
          />
          <StatCard
            title="Total Purposes"
            value={stats?.totalPurposes}
            icon={FiFileText}
            helpText={`${stats?.approvedPurposes} Approved • ${stats?.rejectedPurposes} Rejected • ${stats?.pendingPurposes} Pending`}
            delay={0.4}
          />
          <StatCard
            title="Total Users"
            value={stats?.totalUsers}
            icon={FiUserCheck}
            delay={0.6}
          />
        </SimpleGrid>
      </Container>
      {/* Charts Section */}
      <Container maxW="1400px" mt={12}>
        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
          {/* User Growth Line Chart */}
          <Card boxShadow="lg" borderRadius="2xl" p={6}>
            <Text fontWeight="bold" mb={4} fontSize="lg">User Growth (Last 6 Months)</Text>
            <ResponsiveContainer width="100%" height={220}>
              <LineChart data={userGrowthData} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="users" stroke="#3a866a" strokeWidth={3} dot={{ r: 5 }} />
              </LineChart>
            </ResponsiveContainer>
          </Card>
          {/* Purposes Status Pie Chart */}
          <Card boxShadow="lg" borderRadius="2xl" p={6}>
            <Text fontWeight="bold" mb={4} fontSize="lg">Purposes Status</Text>
            <ResponsiveContainer width="100%" height={220}>
              <PieChart>
                <Pie
                  data={purposesStatusData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={70}
                  label
                >
                  {purposesStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={purposesColors[index % purposesColors.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Card>
          {/* Contacts Bar Chart */}
          <Card boxShadow="lg" borderRadius="2xl" p={6}>
            <Text fontWeight="bold" mb={4} fontSize="lg">Contacts Added Per Month</Text>
            <ResponsiveContainer width="100%" height={220}>
              <BarChart data={contactsData} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="contacts" fill="#2d6651" barSize={30} />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </SimpleGrid>
      </Container>
    </Box>
  );
};

export default MainDashboard;
