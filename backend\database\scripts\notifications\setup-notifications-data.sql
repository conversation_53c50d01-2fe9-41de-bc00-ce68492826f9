-- Notification System Initial Data Setup Script
-- Run this script AFTER running setup-notifications-safe.sql and setup-notifications-constraints.sql

PRINT 'Inserting initial data for notification system...';

-- Step 1: Insert Notification Types
PRINT 'Inserting notification types...';

IF NOT EXISTS (SELECT * FROM NotificationTypes WHERE TypeName = 'TIME_UPDATE')
BEGIN
    INSERT INTO NotificationTypes (TypeName, DisplayName, Description, IconName, ColorScheme) VALUES
    ('TIME_UPDATE', 'Time Update', 'Notifications for delivery/installer time updates', 'FiClock', 'blue'),
    ('FORM_SUBMISSION', 'Form Submission', 'Notifications for form submissions', 'FiFileText', 'green'),
    ('STATUS_CHANGE', 'Status Change', 'Notifications for status changes', 'FiRefreshCw', 'orange'),
    ('SYSTEM_ALERT', 'System Alert', 'System-wide alerts and announcements', 'FiAlertTriangle', 'red'),
    ('TASK_ASSIGNMENT', 'Task Assignment', 'Notifications for task assignments', 'FiUserPlus', 'purple'),
    ('DEADLINE_REMINDER', 'Deadline Reminder', 'Reminders for upcoming deadlines', 'FiBell', 'yellow');

    PRINT 'Notification types inserted successfully.';
END
ELSE
BEGIN
    PRINT 'Notification types already exist.';
END

-- Step 2: Insert Notification Templates for Time Updates
PRINT 'Inserting notification templates...';

IF NOT EXISTS (SELECT * FROM NotificationTemplates WHERE TemplateName = 'DELIVERY_TIME_UPDATE')
BEGIN
    INSERT INTO NotificationTemplates (TypeID, TemplateName, TitleTemplate, MessageTemplate, ActionButtonText, ActionUrl, Priority) VALUES
    (
        (SELECT ID FROM NotificationTypes WHERE TypeName = 'TIME_UPDATE'),
        'DELIVERY_TIME_UPDATE',
        'Delivery Time Updated - {quotationNo}',
        '{employeeName} has updated the delivery time for quotation {quotationNo} (Client: {clientName}). New time: {newTime}. Reason: {reason}',
        'View Details',
        '/forms/delivery-report/follow-up?voucher_No={quotationNo}',
        2
    ),
    (
        (SELECT ID FROM NotificationTypes WHERE TypeName = 'TIME_UPDATE'),
        'INSTALLER_TIME_UPDATE',
        'Installation Time Updated - {quotationNo}',
        '{employeeName} has updated the installation time for quotation {quotationNo} (Client: {clientName}). New time: {newTime}. Reason: {reason}',
        'View Details',
        '/forms/installer-report/follow-up?voucher_No={quotationNo}',
        2
    );

    PRINT 'Time update templates inserted successfully.';
END
ELSE
BEGIN
    PRINT 'Time update templates already exist.';
END

-- Step 3: Insert Templates for Future Use Cases
PRINT 'Inserting additional notification templates for future use...';

IF NOT EXISTS (SELECT * FROM NotificationTemplates WHERE TemplateName = 'BUSINESS_LEAD_SUBMITTED')
BEGIN
    INSERT INTO NotificationTemplates (TypeID, TemplateName, TitleTemplate, MessageTemplate, ActionButtonText, ActionUrl, Priority) VALUES
    (
        (SELECT ID FROM NotificationTypes WHERE TypeName = 'FORM_SUBMISSION'),
        'BUSINESS_LEAD_SUBMITTED',
        'New Business Lead Submitted',
        'A new business lead has been submitted by {employeeName} for client {clientName}.',
        'Review Lead',
        '/forms/business-leads',
        2
    ),
    (
        (SELECT ID FROM NotificationTypes WHERE TypeName = 'STATUS_CHANGE'),
        'TASK_STATUS_CHANGED',
        'Task Status Changed - {quotationNo}',
        'Task status for quotation {quotationNo} has been changed from {oldStatus} to {newStatus} by {employeeName}.',
        'View Task',
        '/dashboard',
        1
    ),
    (
        (SELECT ID FROM NotificationTypes WHERE TypeName = 'SYSTEM_ALERT'),
        'SYSTEM_MAINTENANCE',
        'System Maintenance Alert',
        'System maintenance is scheduled for {maintenanceDate}. Expected downtime: {duration}.',
        'Learn More',
        '/system-alerts',
        3
    ),
    (
        (SELECT ID FROM NotificationTypes WHERE TypeName = 'TASK_ASSIGNMENT'),
        'NEW_TASK_ASSIGNED',
        'New Task Assigned - {quotationNo}',
        'You have been assigned a new {taskType} task for quotation {quotationNo} (Client: {clientName}). Due date: {dueDate}',
        'View Task',
        '/dashboard',
        2
    ),
    (
        (SELECT ID FROM NotificationTypes WHERE TypeName = 'DEADLINE_REMINDER'),
        'TASK_DEADLINE_REMINDER',
        'Task Deadline Reminder - {quotationNo}',
        'Reminder: Your {taskType} task for quotation {quotationNo} is due {timeRemaining}.',
        'View Task',
        '/dashboard',
        2
    );

    PRINT 'Additional notification templates inserted successfully.';
END
ELSE
BEGIN
    PRINT 'Additional notification templates already exist.';
END

-- Step 4: Create the notification details view
PRINT 'Creating notification details view...';

IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_NotificationDetails')
BEGIN
    DROP VIEW vw_NotificationDetails;
    PRINT 'Dropped existing vw_NotificationDetails view.';
END

-- Create view with explicit column list for better compatibility
DECLARE @CreateViewSQL NVARCHAR(MAX);
SET @CreateViewSQL = '
CREATE VIEW vw_NotificationDetails AS
SELECT
    n.ID,
    n.Title,
    n.Message,
    n.ActionButtonText,
    n.ActionUrl,
    n.Priority,
    n.SenderUserID,
    n.SenderEmployeeID,
    n.ContextData,
    n.ReferenceID,
    n.ReferenceType,
    n.CreatedAt,
    n.ExpiresAt,
    nt.TypeName,
    nt.DisplayName as TypeDisplayName,
    nt.IconName,
    nt.ColorScheme,
    ISNULL(sender_user.user_name, '''') as SenderUserName,
    ISNULL(sender_emp.Title, '''') as SenderEmployeeName,
    nr.RecipientUserID,
    nr.RecipientRoleID,
    nr.RecipientEmployeeID,
    nr.IsRead,
    nr.ReadAt,
    nr.IsArchived,
    nr.ArchivedAt,
    ISNULL(recipient_user.user_name, '''') as RecipientUserName,
    ISNULL(recipient_role.RoleName, '''') as RecipientRoleName,
    ISNULL(recipient_emp.Title, '''') as RecipientEmployeeName
FROM Notifications n
INNER JOIN NotificationTypes nt ON n.TypeID = nt.ID
LEFT JOIN users sender_user ON n.SenderUserID = sender_user.id
LEFT JOIN EmployeeDetails sender_emp ON n.SenderEmployeeID = sender_emp.ID
INNER JOIN NotificationRecipients nr ON n.ID = nr.NotificationID
LEFT JOIN users recipient_user ON nr.RecipientUserID = recipient_user.id
LEFT JOIN User_Role recipient_role ON nr.RecipientRoleID = recipient_role.RoleID
LEFT JOIN EmployeeDetails recipient_emp ON nr.RecipientEmployeeID = recipient_emp.ID
WHERE nt.IsActive = 1
';

EXEC sp_executesql @CreateViewSQL;

PRINT 'vw_NotificationDetails view created successfully.';

-- Step 5: Add columns to ClientPurpose table if they don't exist
PRINT 'Checking and adding columns to ClientPurpose table...';

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'ClientPurpose')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ClientPurpose') AND name = 'deliveryTimeUpdateReason')
    BEGIN
        ALTER TABLE ClientPurpose ADD deliveryTimeUpdateReason NVARCHAR(500);
        PRINT 'Added deliveryTimeUpdateReason column to ClientPurpose table.';
    END
    ELSE
    BEGIN
        PRINT 'deliveryTimeUpdateReason column already exists in ClientPurpose table.';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ClientPurpose') AND name = 'installerTimeUpdateReason')
    BEGIN
        ALTER TABLE ClientPurpose ADD installerTimeUpdateReason NVARCHAR(500);
        PRINT 'Added installerTimeUpdateReason column to ClientPurpose table.';
    END
    ELSE
    BEGIN
        PRINT 'installerTimeUpdateReason column already exists in ClientPurpose table.';
    END
END
ELSE
BEGIN
    PRINT 'WARNING: ClientPurpose table not found. Time update reason columns not added.';
END

-- Step 6: Display summary
PRINT '';
PRINT '=== NOTIFICATION SYSTEM SETUP COMPLETE ===';
PRINT 'Tables created:';
PRINT '  - NotificationTypes (6 types)';
PRINT '  - NotificationTemplates (7 templates)';
PRINT '  - Notifications';
PRINT '  - NotificationRecipients';
PRINT '  - NotificationSettings';
PRINT '';
PRINT 'Views created:';
PRINT '  - vw_NotificationDetails';
PRINT '';
PRINT 'Columns added to ClientPurpose:';
PRINT '  - deliveryTimeUpdateReason';
PRINT '  - installerTimeUpdateReason';
PRINT '';
PRINT 'The notification system is now ready to use!';
PRINT 'Time update notifications will be sent to all admin users (RoleID = 1).';
PRINT '';

-- Display current notification types
PRINT 'Available notification types:';
SELECT TypeName, DisplayName, ColorScheme FROM NotificationTypes WHERE IsActive = 1;

-- Display current templates
PRINT 'Available notification templates:';
SELECT nt.TypeName, ntp.TemplateName, ntp.Priority
FROM NotificationTemplates ntp
INNER JOIN NotificationTypes nt ON ntp.TypeID = nt.ID
WHERE ntp.IsActive = 1
ORDER BY nt.TypeName, ntp.TemplateName;
