const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');

router.use(authorization);

// ------------------- Menu APIS ------------------- //
router.get('/', async (req, res) => {
    const query = 'SELECT TOP 10 * FROM menus';
    const pool = await getPool();
    const request = new sql.Request(pool);
    request.query(query, (err, result) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.status(200).json(result.recordset);
    });
});

module.exports = router;