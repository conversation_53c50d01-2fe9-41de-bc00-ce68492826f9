import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Select,
  useToast,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
  Box,
  Text,
} from "@chakra-ui/react";
import { keyframes } from "@emotion/react";
import axiosInstance from "@src/app/axios";
import React, { useState } from 'react';
import { FiSave, FiX } from 'react-icons/fi';

// Define animations
const slideIn = keyframes`
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
`;

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

interface FormField {
  name: string;
  label: string;
  type: string;
  placeholder?: string;
  isRequired?: boolean;
  options?: { value: string; label: string; }[];
}

interface AddClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const formFields: FormField[] = [
  {
    name: 'title',
    label: 'Title',
    type: 'select',
    isRequired: true,
    options: [
      { value: 'Mr', label: 'Mr' },
      { value: 'Mrs', label: 'Mrs' },
      { value: 'Ms', label: 'Ms' },
      { value: 'Miss', label: 'Miss' },
    ],
  },
  {
    name: 'firstName',
    label: 'First Name',
    type: 'text',
    placeholder: 'Enter first name',
    isRequired: true,
  },
  {
    name: 'surName',
    label: 'SurName',
    type: 'text',
    placeholder: 'Enter last name',
    isRequired: true,
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter email',
    isRequired: true,
  },
  {
    name: 'mobileNo',
    label: 'Mobile No.',
    type: 'tel',
    placeholder: 'Enter mobile no.',
    isRequired: true,
  },
  {
    name: 'landline',
    label: 'Landline',
    type: 'tel',
    placeholder: 'Enter landline number',
    isRequired: true,
  },
  {
    name: 'address',
    label: 'Address',
    type: 'text',
    placeholder: 'Enter Location',
    isRequired: true,
  },
  {
    name: 'url',
    label: 'Website',
    type: 'url',
    placeholder: 'Enter website link',
    isRequired: true,
  },
];

const initialFormState = {
  title: '',
  firstName: '',
  surName: '',
  email: '',
  mobileNo: '',
  landline: '',
  address: '',
  url: ''
};

const AddClientModal = ({ isOpen, onClose, onSave }: AddClientModalProps) => {
  const toast = useToast();
  const [formData, setFormData] = useState(initialFormState);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagInput(e.target.value);
  };

  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && tagInput.trim() !== '') {
      e.preventDefault();
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (indexToRemove: number) => {
    setTags(tags.filter((_, index) => index !== indexToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const submissionData = {
      title: `${formData.title} ${formData.firstName} ${formData.surName}`,
      formalTitle: formData.title,
      surName: formData.surName,
      firstName: formData.firstName,
      add1: formData.address,
      tel: formData.landline,
      Mobile: formData.mobileNo,
      email: formData.email,
      url: formData.url,
      Mode: tags.join(','),
    };

    try {
      await axiosInstance.post('/client', submissionData);
      onSave();
      toast({
        title: "Client added.",
        description: "The client has been added successfully.",
        status: "success",
        duration: 5000,
        isClosable: true,
      });
      onClose();
      setFormData(initialFormState);
      setTags([]);
      setTagInput('');
    } catch (error) {
      toast({
        title: "An error occurred.",
        description: "Unable to add client.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      scrollBehavior="inside"
      motionPreset="scale"
      size="xl"
      isCentered
    >
      <ModalOverlay 
        bg="blackAlpha.300"
        backdropFilter="blur(10px)"
        sx={{
          animation: `${fadeIn} 0.2s ease-out`
        }}
      />
      <ModalContent 
        maxH="85vh"
        my={4}
        sx={{
          animation: `${slideIn} 0.3s ease-out`,
          bg: "white",
          boxShadow: "xl",
          display: "flex",
          flexDirection: "column"
        }}
      >
        <ModalHeader
          bgGradient="linear(to-r, #3a866a, #2d6651)"
          color="white"
          borderTopRadius="md"
          px={6}
          py={4}
          position="sticky"
          top={0}
          zIndex={1}
          flexShrink={0}
        >
          <Text fontSize="xl" fontWeight="bold">Add New Client</Text>
        </ModalHeader>
        <ModalCloseButton 
          color="white" 
          _hover={{
            bg: "whiteAlpha.300",
            transform: "rotate(90deg)"
          }}
          transition="all 0.2s"
        />
        
        <form onSubmit={handleSubmit} style={{ margin: 0, display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
          <ModalBody
            px={6}
            py={4}
            flex="1"
            overflowY="auto"
            sx={{
              "&::-webkit-scrollbar": {
                width: "6px",
              },
              "&::-webkit-scrollbar-track": {
                background: "#f1f1f1",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "#3a866a",
                borderRadius: "4px",
                "&:hover": {
                  background: "#2d6651",
                },
              },
            }}
          >
            <VStack spacing={5} align="stretch">
              {formFields.map((field) => (
                <FormControl key={field.name} isRequired={field.isRequired}>
                  <FormLabel 
                    color="#2d6651" 
                    fontWeight="600"
                    fontSize="sm"
                    mb={2}
                  >
                    {field.label}
                  </FormLabel>
                  {field.type === 'select' ? (
                    <Select
                      name={field.name}
                      value={formData[field.name as keyof typeof formData]}
                      onChange={handleChange}
                      bg="white"
                      borderColor="gray.200"
                      _hover={{ borderColor: "#3a866a" }}
                      _focus={{ 
                        borderColor: "#3a866a",
                        boxShadow: "0 0 0 1px #3a866a"
                      }}
                      transition="all 0.2s"
                      h="40px"
                    >
                      <option value="">Please Select</option>
                      {field.options?.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </Select>
                  ) : (
                    <Input
                      type={field.type}
                      name={field.name}
                      value={formData[field.name as keyof typeof formData]}
                      onChange={handleChange}
                      placeholder={field.placeholder}
                      bg="white"
                      borderColor="gray.200"
                      _hover={{ borderColor: "#3a866a" }}
                      _focus={{ 
                        borderColor: "#3a866a",
                        boxShadow: "0 0 0 1px #3a866a"
                      }}
                      transition="all 0.2s"
                      h="40px"
                    />
                  )}
                </FormControl>
              ))}
              <FormControl>
                <FormLabel 
                  color="#2d6651" 
                  fontWeight="600"
                  fontSize="sm"
                  mb={2}
                >
                  Tags
                </FormLabel>
                <Input
                  placeholder="Enter a tag and press Enter"
                  value={tagInput}
                  onChange={handleTagInputChange}
                  onKeyDown={handleTagKeyDown}
                  bg="white"
                  borderColor="gray.200"
                  _hover={{ borderColor: "#3a866a" }}
                  _focus={{ 
                    borderColor: "#3a866a",
                    boxShadow: "0 0 0 1px #3a866a"
                  }}
                  transition="all 0.2s"
                  h="40px"
                />
                <Box mt={3}>
                  <Wrap spacing={2}>
                    {tags.map((tag, index) => (
                      <WrapItem key={index}>
                        <Tag 
                          size="md" 
                          borderRadius="full" 
                          variant="subtle" 
                          bgGradient="linear(to-r, #3a866a, #2d6651)"
                          color="white"
                          px={3}
                          py={1}
                          sx={{
                            animation: `${fadeIn} 0.2s ease-out`,
                          }}
                        >
                          <TagLabel>{tag}</TagLabel>
                          <TagCloseButton 
                            onClick={() => removeTag(index)}
                            _hover={{
                              bg: "whiteAlpha.300",
                              transform: "rotate(90deg)"
                            }}
                            transition="all 0.2s"
                          />
                        </Tag>
                      </WrapItem>
                    ))}
                  </Wrap>
                </Box>
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter 
            borderTop="1px" 
            borderColor="gray.100"
            gap={3}
            bg="white"
            zIndex={1}
            px={6}
            py={4}
            flexShrink={0}
          >
            <Button
              leftIcon={<FiSave />}
              type="submit"
              bgGradient="linear(to-r, #3a866a, #2d6651)"
              color="white"
              h="40px"
              px={6}
              _hover={{
                bgGradient: "linear(to-r, #2d6651, #3a866a)",
                transform: "translateY(-1px)",
                boxShadow: "lg"
              }}
              transition="all 0.2s"
            >
              Save Client
            </Button>
            <Button
              leftIcon={<FiX />}
              onClick={onClose}
              variant="outline"
              borderColor="#3a866a"
              color="#3a866a"
              h="40px"
              _hover={{
                bg: "rgba(58, 134, 106, 0.1)",
                transform: "translateY(-1px)"
              }}
              transition="all 0.2s"
            >
              Cancel
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default AddClientModal; 