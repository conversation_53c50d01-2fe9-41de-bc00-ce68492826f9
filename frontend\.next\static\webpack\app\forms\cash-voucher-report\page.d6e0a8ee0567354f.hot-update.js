"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _src_components_Custom_ReportTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @src/components/Custom/ReportTable */ \"(app-pages-browser)/./src/components/Custom/ReportTable/index.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedTask, setSelectedTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const fetchTasks = async ()=>{\n        try {\n            const { data } = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report\");\n            console.log(data);\n            if (Array.isArray(data) && data.length > 0) {\n                setTasks(data);\n            } else {\n                setTasks([]);\n            }\n        } catch (error) {\n            console.error(\"Error: \", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchTasks();\n    }, []);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"rejected\":\n                return \"red\";\n            case \"pending\":\n                return \"yellow\";\n            case \"approved\":\n                return \"green\";\n            default:\n                return \"gray\";\n        }\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"Voucher_No\"\n        },\n        {\n            header: \"Adj. Voucher No\",\n            field: \"adj_VoucherNo\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"acc_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"acc_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"GrossAmount\"\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"TotalAmt\"\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"RemainingAmount\"\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        }\n    ];\n    const getRowCursor = (task)=>task.status === \"pending\" ? \"pointer\" : \"auto\";\n    const handleRowClick = (task)=>{\n        if (task.status === \"pending\") {\n            router.push(\"/forms/audit-report/follow-up?purpose_no=\".concat(task.ID));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 105,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-inner\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"row\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bgWhite\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                margin: \"0\",\n                                                textAlign: \"center\",\n                                                color: \"#2B6CB0\",\n                                                fontSize: \"24px\",\n                                                fontWeight: \"bold\",\n                                                padding: \"10px\"\n                                            },\n                                            children: \"Audit Reports\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"row\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_ReportTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        data: tasks,\n                                        columns: columns,\n                                        onRowClick: handleRowClick,\n                                        getRowCursor: getRowCursor,\n                                        getBadgeColor: getStatusColor,\n                                        dateField: \"assessmentTime\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 111,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 110,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 109,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                lineNumber: 108,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"Nbcapvg82WRiqvScNAPGW3jP5S4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ })

});