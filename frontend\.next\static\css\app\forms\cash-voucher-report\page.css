/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/dashboard/dashboard.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
.main-panel {
    position: relative;
    width: calc(100% - 200px);
    float: right;
    transition: all .3s;
}

@media screen and (max-width: 991.5px) {
    .main-panel {
        width: calc(100% - 200px) !important;
        transition: all .5s;
    }
}

@media screen and (max-width: 767px) {
    .main-panel {
        width: 100% !important;
        transition: all .5s;
    }
}

/* Calendar View Styles */
.rbc-calendar {
    font-family: inherit;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
}

.rbc-toolbar {
    padding: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.rbc-toolbar button {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    color: #333;
    border-radius: 4px;
    padding: 5px 10px;
}

.rbc-toolbar button.rbc-active {
    background-color: #3275bb;
    color: white;
    border-color: #3275bb;
}

.rbc-event {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
}

.rbc-today {
    background-color: rgba(50, 117, 187, 0.1);
}

.rbc-header {
    padding: 8px 0;
    font-weight: 600;
    border-bottom: 1px solid #eee;
}

.rbc-day-bg+.rbc-day-bg {
    border-left: 1px solid #f0f0f0;
}

.rbc-month-view {
    border: 1px solid #eee;
    border-radius: 8px;
}

/* Day View Styles */
.rbc-time-view {
    border: 1px solid #eee;
    border-radius: 8px;
}

.rbc-time-header {
    border-bottom: 1px solid #eee;
}

.rbc-time-header-content {
    border-left: 1px solid #eee;
}

.rbc-time-content {
    border-top: 1px solid #eee;
}

.rbc-time-slot {
    border-top: 1px solid #f0f0f0;
}

.rbc-day-slot .rbc-time-slot {
    border-top: 1px solid #f0f0f0;
}

.rbc-timeslot-group {
    border-bottom: 1px solid #f0f0f0;
}

.rbc-time-gutter {
    background-color: #f8f9fa;
}

/* Week View Styles */
.rbc-time-header-cell {
    padding: 8px 0;
}

.rbc-header+.rbc-header {
    border-left: 1px solid #f0f0f0;
}

/* Agenda View Styles */
.rbc-agenda-view {
    border: 1px solid #eee;
    border-radius: 8px;
}

.rbc-agenda-view table {
    border: none;
}

.rbc-agenda-view table thead {
    background-color: #f8f9fa;
}

.rbc-agenda-view table.rbc-agenda-table tbody>tr>td {
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.rbc-agenda-view table.rbc-agenda-table tbody>tr:last-child>td {
    border-bottom: none;
}

.rbc-agenda-time-cell {
    font-weight: 500;
}

.rbc-agenda-date-cell {
    font-weight: 500;
}

/* Popup Styles */
.rbc-overlay {
    background-color: white;
    border: 1px solid #eee;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

.rbc-overlay-header {
    padding: 8px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.rbc-allday-cell {
    display: none !important;
}

.rbc-header {
    border-bottom: none !important;
    padding: 10px 20px !important;
}

.rbc-row.rbc-time-header-cell {
    padding: 0 !important;
}

.rbc-event.rbc-event-continues-earlier {
    height: auto !important;
}

.rbc-month-row {
    overflow: initial !important;
}

.rbc-row-segment .rbc-event .rbc-event-content {
    overflow: auto !important;
    text-overflow: initial !important;
    white-space: normal !important;
    text-transform: capitalize !important;
}

/* Responsive Styles */
@media screen and (max-width: 768px) {
    .rbc-toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .rbc-toolbar>* {
        margin-bottom: 10px;
    }

    .rbc-toolbar>*:last-child {
        margin-bottom: 0;
    }
}
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/Loader/Loader.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
.loaderContainer {
    background-color: transparent;
    height: 100vh;
    display: grid;
    place-items: center;
    position: relative;
}

.scene {
    position: relative;
    z-index: 2123123;
    height: 220px;
    width: 220px;
    display: grid;
    place-items: center;
}

.cube-wrapper {
    transform-style: preserve-3d;
    animation: bouncing 2s infinite;
}

.cube {
    transform-style: preserve-3d;
    transform: rotateX(45deg) rotateZ(45deg);
    animation: rotation 2s infinite;
}

.cube-faces {
    transform-style: preserve-3d;
    height: 80px;
    width: 80px;
    position: relative;
    transform-origin: 0 0;
    transform: translateX(0) translateY(0) translateZ(-40px);
}

.cube-face {
    position: absolute;
    inset: 0;
    background: #0e2f1c;
    border: solid 2px #3bac6a;
}

.cube-face.shadow {
    transform: translateZ(-80px);
    animation: bouncing-shadow 2s infinite;
}

.cube-face.top {
    transform: translateZ(80px);
}

.cube-face.front {
    transform-origin: 0 50%;
    transform: rotateY(-90deg);
}

.cube-face.back {
    transform-origin: 0 50%;
    transform: rotateY(-90deg) translateZ(-80px);
}

.cube-face.right {
    transform-origin: 50% 0;
    transform: rotateX(-90deg) translateY(-80px);
}

.cube-face.left {
    transform-origin: 50% 0;
    transform: rotateX(-90deg) translateY(-80px) translateZ(80px);
}

@keyframes rotation {
    0% {
        transform: rotateX(45deg) rotateY(0) rotateZ(45deg);
        animation-timing-function: cubic-bezier(0.17, 0.84, 0.44, 1);
    }

    50% {
        transform: rotateX(45deg) rotateY(0) rotateZ(225deg);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
    }

    100% {
        transform: rotateX(45deg) rotateY(0) rotateZ(405deg);
        animation-timing-function: cubic-bezier(0.17, 0.84, 0.44, 1);
    }
}

@keyframes bouncing {
    0% {
        transform: translateY(-40px);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
    }

    45% {
        transform: translateY(40px);
        animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    }

    100% {
        transform: translateY(-40px);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
    }
}

@keyframes bouncing-shadow {
    0% {
        transform: translateZ(-80px) scale(1.3);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
        opacity: 0.05;
    }

    45% {
        transform: translateZ(0);
        animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
        opacity: 0.3;
    }

    100% {
        transform: translateZ(-80px) scale(1.3);
        animation-timing-function: cubic-bezier(0.76, 0.05, 0.86, 0.06);
        opacity: 0.05;
    }
}
