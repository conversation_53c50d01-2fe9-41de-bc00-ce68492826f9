const { sql, getPool } = require('../db');
const getFormattedDateTime = () => {
    const now = new Date();

    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = now.getFullYear();

    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
}

const checkAuthorization = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    const date = getFormattedDateTime();

    if (authHeader && authHeader.startsWith('Basic ')) {
        try {
            const base64Credentials = authHeader.split(' ')[1];
            const decodedCredentials = Buffer.from(base64Credentials, 'base64').toString('utf-8');
            const [user, password] = decodedCredentials.split(':');

            if (!user || !password) {
                return res.status(401).json({ message: 'Unauthorized: Invalid credentials format' });
            }
            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('ID', sql.VarChar(50), user);
            request.input('pwd', sql.VarChar(20), password);
            const query = `SELECT * FROM users WHERE id = @ID AND pwd = @pwd`;
            const result = await request.query(query);
            if (result.recordset.length === 0) {
                console.log(`[${req.method}: ${req.originalUrl}] BY [Unknown] AT [${date}]`)
                return res.status(401).json({ message: 'Unauthorized: Invalid credentials or no access' });
            }
            req.user = result.recordset[0];
            console.log(`[${req.method}: ${req.originalUrl}] BY [${user}] AT [${date}]`)
            next();
        } catch (err) {
            console.log(`[${req.method}: ${req.originalUrl}] BY [Unknown] AT [${date}]`)
            return res.status(401).json({ message: 'Unauthorized: Invalid Authorization header format' });
        }
    } else {
        console.log(`[${req.method}: ${req.originalUrl}] BY [Unknown] AT [${date}]`)
        return res.status(401).json({ message: 'Unauthorized: Missing Authorization header' });
    }
};

module.exports = checkAuthorization;
