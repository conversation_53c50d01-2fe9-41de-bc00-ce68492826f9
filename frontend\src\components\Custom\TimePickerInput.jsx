import React, { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  FormErrorMessage,
  HStack,
  Select,
  Input
} from '@chakra-ui/react';

const TimePickerInput = ({
  label,
  value,
  onChange,
  name,
  isRequired = false,
  isInvalid = false,
  errorMessage = '',
  placeholder = "Select date and time"
}) => {
  const [dateValue, setDateValue] = useState('');
  const [timeValue, setTimeValue] = useState('');

  // Generate time options with 15-minute intervals
  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const hourStr = hour.toString().padStart(2, '0');
        const minuteStr = minute.toString().padStart(2, '0');
        const timeStr = `${hourStr}:${minuteStr}`;
        const displayTime = hour === 0 ? `12:${minuteStr} AM` :
          hour < 12 ? `${hour}:${minuteStr} AM` :
            hour === 12 ? `12:${minuteStr} PM` :
              `${hour - 12}:${minuteStr} PM`;
        options.push({ value: timeStr, label: displayTime });
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();

  // Parse the datetime-local value when it changes from parent
  useEffect(() => {
    if (value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        // Extract date part (YYYY-MM-DD)
        const dateStr = date.toISOString().split('T')[0];
        setDateValue(dateStr);

        // Extract time part and round to nearest 15 minutes
        const hours = date.getHours();
        const minutes = date.getMinutes();
        const roundedMinutes = Math.round(minutes / 15) * 15;
        const finalHours = roundedMinutes >= 60 ? hours + 1 : hours;
        const finalMinutes = roundedMinutes >= 60 ? 0 : roundedMinutes;

        const timeStr = `${finalHours.toString().padStart(2, '0')}:${finalMinutes.toString().padStart(2, '0')}`;
        setTimeValue(timeStr);
      }
    }
  }, [value]);

  // Handle date change
  const handleDateChange = (e) => {
    const newDate = e.target.value;
    setDateValue(newDate);
    updateDateTime(newDate, timeValue);
  };

  // Handle time change
  const handleTimeChange = (e) => {
    const newTime = e.target.value;
    setTimeValue(newTime);
    updateDateTime(dateValue, newTime);
  };

  // Combine date and time and call parent onChange
  const updateDateTime = (date, time) => {
    if (date && time) {
      const dateTimeStr = `${date}T${time}:00`; // Add seconds as 00
      const dateTime = new Date(dateTimeStr);
      if (!isNaN(dateTime.getTime())) {
        // Create event-like object for compatibility
        const event = {
          target: {
            name: name,
            value: dateTimeStr
          }
        };
        onChange(event);
      }
    }
  };

  return (
    <FormControl isRequired={isRequired} isInvalid={isInvalid}>
      <FormLabel>{label}</FormLabel>
      <HStack spacing={2}>
        <Input
          type="date"
          value={dateValue}
          onChange={handleDateChange}
          placeholder="Select date"
          flex={1}
        />
        <Select
          value={timeValue}
          onChange={handleTimeChange}
          placeholder="Select time"
          flex={1}
        >
          {timeOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </HStack>
      <FormErrorMessage>{errorMessage}</FormErrorMessage>
    </FormControl>
  );
};

export default TimePickerInput;
