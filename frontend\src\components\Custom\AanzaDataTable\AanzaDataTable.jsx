"use client";
import "./AanzaDataTable.css";
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Box,
  IconButton,
  Text,
} from "@chakra-ui/react";
import { useState, useCallback, memo } from "react";
import { AddIcon, DeleteIcon } from "@chakra-ui/icons";
import PropTypes from "prop-types";

const AanzaDataTable = ({
  headers,
  tableData = [],
  tableWidth = "100%",
  tableHeight = "300px",
  fontSize = "md",
  responsive = true,
  showDeleteIcon = true,
  showAddIcon = true,
  styleTable = {},
  styleHead = {},
  styleBody = {},
  cellRender,
  isDisabled = false,
  calculation = () => { },
  setTableData = () => { },
}) => {
  const handleAddRow = () => {
    const newRow = {};
    headers.forEach((header) => {
      newRow[header.key] = "";
    });
    setTableData([...tableData, newRow]);
  };

  const handleDeleteRow = useCallback((rowIndex) => {
    setTableData((prevData) =>
      prevData.filter((_, index) => index !== rowIndex)
    );
  }, []);

  const handleDeleteAllRows = useCallback(() => {
    setTableData([]);
  }, []);
  return (
    <>
      <TableContainer
        width={tableWidth}
        height={tableHeight}
        {...styleTable}
        overflowX={responsive ? "auto" : "hidden"}
        className="TableContainer"
        overflowY="auto"
      >
        <Box>
          <Table
            variant="striped"
            colorScheme="gray"
            size="sm"
            fontSize={fontSize}
          >
            <Thead
              position="sticky"
              top={0}
              zIndex={1}
              bg="#071533 !important"
              style={{ background: "#071533 !important" }}
              {...styleHead}
            >
              <Tr>
                {showAddIcon && <Th style={{ textAlign: "center" }}>
                  <IconButton
                    icon={<AddIcon />}
                    onClick={handleAddRow}
                    size="sm"
                    aria-label="Add Row"
                    colorScheme="darkblue"
                    isDisabled={isDisabled}
                  />
                </Th>}
                {showDeleteIcon && <Th style={{ textAlign: "center" }}>
                  <IconButton
                    icon={<DeleteIcon />}
                    onClick={handleDeleteAllRows}
                    size="md"
                    aria-label="Delete Row"
                    colorScheme="darkblue"
                    isDisabled={isDisabled}
                  />
                </Th>}
                {headers.map((header, index) => (
                  <Th sx={{ paddingTop: "10px !important", paddingBottom: "10px !important" }} key={index}>
                    {header.label}
                  </Th>
                ))}
              </Tr>
            </Thead>
            <Tbody {...styleBody}>
              {tableData.length > 0 ? (
                tableData.map((row, rowIndex) => (
                  <Tr key={rowIndex}>
                    {(!showDeleteIcon && !showAddIcon) ? <></> : <Td style={{ textAlign: "center", fontSize: "14px" }}>
                      {rowIndex + 1}
                    </Td>}
                    {showDeleteIcon && <Td style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                      <IconButton
                        icon={<DeleteIcon />}
                        onClick={() => handleDeleteRow(rowIndex)}
                        size="md"
                        aria-label="Delete Row"
                        colorScheme="red"
                        isDisabled={isDisabled}
                      />
                    </Td>}
                    {headers.map((header, colIndex) => (
                      <Td key={colIndex}>
                        {cellRender
                          ? cellRender(
                            row[header.key],
                            header.key,
                            rowIndex,
                            colIndex,
                            header,
                            (value) => calculation(header, value, rowIndex)
                          )
                          : row[header.key]}
                      </Td>
                    ))}
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={headers.length + 2}>
                    <Text>No Record Found</Text>
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      </TableContainer>
    </>
  );
};

AanzaDataTable.propTypes = {
  headers: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      key: PropTypes.string.isRequired,
    })
  ).isRequired,
  tableWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  tableHeight: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  fontSize: PropTypes.string,
  responsive: PropTypes.bool,
  styleTable: PropTypes.object,
  styleHead: PropTypes.object,
  styleBody: PropTypes.object,
  cellRender: PropTypes.func,
};

export default memo(AanzaDataTable);
