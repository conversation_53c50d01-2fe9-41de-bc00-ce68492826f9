"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-focus-lock";
exports.ids = ["vendor-chunks/react-focus-lock"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar AutoFocusInside = function AutoFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    children = _ref.children,\n    _ref$className = _ref.className,\n    className = _ref$className === void 0 ? undefined : _ref$className;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_2__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_AUTO, !disabled), {\n    className: className\n  }), children);\n};\nAutoFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().node).isRequired,\n  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AutoFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9BdXRvRm9jdXNJbnNpZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDaEM7QUFDUztBQUNlO0FBQ2Q7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFtQixRQUFRLDhFQUFRLEdBQUcsRUFBRSxpREFBVSxDQUFDLDREQUFVO0FBQ25GO0FBQ0EsR0FBRztBQUNIO0FBQ0EsNEJBQTRCLEtBQXFDO0FBQ2pFLFlBQVksd0RBQWM7QUFDMUIsWUFBWSx3REFBYztBQUMxQixhQUFhLDBEQUFnQjtBQUM3QixFQUFFLEVBQUUsQ0FBRTtBQUNOLGlFQUFlLGVBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9BdXRvRm9jdXNJbnNpZGUuanM/YTc3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUHJvcFR5cGVzIGZyb20gJ3Byb3AtdHlwZXMnO1xuaW1wb3J0IHsgRk9DVVNfQVVUTyB9IGZyb20gJ2ZvY3VzLWxvY2svY29uc3RhbnRzJztcbmltcG9ydCB7IGlubGluZVByb3AgfSBmcm9tICcuL3V0aWwnO1xudmFyIEF1dG9Gb2N1c0luc2lkZSA9IGZ1bmN0aW9uIEF1dG9Gb2N1c0luc2lkZShfcmVmKSB7XG4gIHZhciBfcmVmJGRpc2FibGVkID0gX3JlZi5kaXNhYmxlZCxcbiAgICBkaXNhYmxlZCA9IF9yZWYkZGlzYWJsZWQgPT09IHZvaWQgMCA/IGZhbHNlIDogX3JlZiRkaXNhYmxlZCxcbiAgICBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgX3JlZiRjbGFzc05hbWUgPSBfcmVmLmNsYXNzTmFtZSxcbiAgICBjbGFzc05hbWUgPSBfcmVmJGNsYXNzTmFtZSA9PT0gdm9pZCAwID8gdW5kZWZpbmVkIDogX3JlZiRjbGFzc05hbWU7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCBfZXh0ZW5kcyh7fSwgaW5saW5lUHJvcChGT0NVU19BVVRPLCAhZGlzYWJsZWQpLCB7XG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVcbiAgfSksIGNoaWxkcmVuKTtcbn07XG5BdXRvRm9jdXNJbnNpZGUucHJvcFR5cGVzID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiID8ge1xuICBjaGlsZHJlbjogUHJvcFR5cGVzLm5vZGUuaXNSZXF1aXJlZCxcbiAgZGlzYWJsZWQ6IFByb3BUeXBlcy5ib29sLFxuICBjbGFzc05hbWU6IFByb3BUeXBlcy5zdHJpbmdcbn0gOiB7fTtcbmV4cG9ydCBkZWZhdWx0IEF1dG9Gb2N1c0luc2lkZTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/Combination.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/Combination.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Lock__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Lock */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/Lock.js\");\n/* harmony import */ var _Trap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Trap */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/Trap.js\");\n\n\n\n\n\nvar FocusLockCombination = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function FocusLockUICombination(props, ref) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().createElement(_Lock__WEBPACK_IMPORTED_MODULE_3__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    sideCar: _Trap__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    ref: ref\n  }, props));\n});\nvar _ref = _Lock__WEBPACK_IMPORTED_MODULE_3__[\"default\"].propTypes || {},\n  sideCar = _ref.sideCar,\n  propTypes = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, [\"sideCar\"]);\nFocusLockCombination.propTypes =  true ? propTypes : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FocusLockCombination);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9Db21iaW5hdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW9HO0FBQzFDO0FBQ2hCO0FBQ1Q7QUFDRjtBQUMvQix3Q0FBd0MsaURBQVU7QUFDbEQsc0JBQXNCLDBEQUFtQixDQUFDLDZDQUFXLEVBQUUsOEVBQVE7QUFDL0QsYUFBYSw2Q0FBUztBQUN0QjtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0QsV0FBVyw2Q0FBVyxnQkFBZ0I7QUFDdEM7QUFDQSxjQUFjLG1HQUE2QjtBQUMzQyxpQ0FBaUMsS0FBcUMsZUFBZSxDQUFFO0FBQ3ZGLGlFQUFlLG9CQUFvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L0NvbWJpbmF0aW9uLmpzPzJmNGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlXCI7XG5pbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBSZWFjdCwgeyBmb3J3YXJkUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEZvY3VzTG9ja1VJIGZyb20gJy4vTG9jayc7XG5pbXBvcnQgRm9jdXNUcmFwIGZyb20gJy4vVHJhcCc7XG52YXIgRm9jdXNMb2NrQ29tYmluYXRpb24gPSAvKiNfX1BVUkVfXyovZm9yd2FyZFJlZihmdW5jdGlvbiBGb2N1c0xvY2tVSUNvbWJpbmF0aW9uKHByb3BzLCByZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEZvY3VzTG9ja1VJLCBfZXh0ZW5kcyh7XG4gICAgc2lkZUNhcjogRm9jdXNUcmFwLFxuICAgIHJlZjogcmVmXG4gIH0sIHByb3BzKSk7XG59KTtcbnZhciBfcmVmID0gRm9jdXNMb2NrVUkucHJvcFR5cGVzIHx8IHt9LFxuICBzaWRlQ2FyID0gX3JlZi5zaWRlQ2FyLFxuICBwcm9wVHlwZXMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShfcmVmLCBbXCJzaWRlQ2FyXCJdKTtcbkZvY3VzTG9ja0NvbWJpbmF0aW9uLnByb3BUeXBlcyA9IHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiA/IHByb3BUeXBlcyA6IHt9O1xuZXhwb3J0IGRlZmF1bHQgRm9jdXNMb2NrQ29tYmluYXRpb247Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/Combination.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/FocusGuard.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/FocusGuard.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hiddenGuard: () => (/* binding */ hiddenGuard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar hiddenGuard = {\n  width: '1px',\n  height: '0px',\n  padding: 0,\n  overflow: 'hidden',\n  position: 'fixed',\n  top: '1px',\n  left: '1px'\n};\nvar InFocusGuard = function InFocusGuard(_ref) {\n  var _ref$children = _ref.children,\n    children = _ref$children === void 0 ? null : _ref$children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }), children, children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    key: \"guard-last\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }));\n};\nInFocusGuard.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().node)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InFocusGuard);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9Gb2N1c0d1YXJkLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF3QztBQUNMO0FBQzVCO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUIsQ0FBQywyQ0FBUSxxQkFBcUIsMERBQW1CO0FBQzFGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxzQ0FBc0MsMERBQW1CO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EseUJBQXlCLEtBQXFDO0FBQzlELFlBQVksd0RBQWM7QUFDMUIsRUFBRSxFQUFFLENBQUU7QUFDTixpRUFBZSxZQUFZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvRm9jdXNHdWFyZC5qcz83OGE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyBGcmFnbWVudCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSAncHJvcC10eXBlcyc7XG5leHBvcnQgdmFyIGhpZGRlbkd1YXJkID0ge1xuICB3aWR0aDogJzFweCcsXG4gIGhlaWdodDogJzBweCcsXG4gIHBhZGRpbmc6IDAsXG4gIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgcG9zaXRpb246ICdmaXhlZCcsXG4gIHRvcDogJzFweCcsXG4gIGxlZnQ6ICcxcHgnXG59O1xudmFyIEluRm9jdXNHdWFyZCA9IGZ1bmN0aW9uIEluRm9jdXNHdWFyZChfcmVmKSB7XG4gIHZhciBfcmVmJGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICBjaGlsZHJlbiA9IF9yZWYkY2hpbGRyZW4gPT09IHZvaWQgMCA/IG51bGwgOiBfcmVmJGNoaWxkcmVuO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoRnJhZ21lbnQsIG51bGwsIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBrZXk6IFwiZ3VhcmQtZmlyc3RcIixcbiAgICBcImRhdGEtZm9jdXMtZ3VhcmRcIjogdHJ1ZSxcbiAgICBcImRhdGEtZm9jdXMtYXV0by1ndWFyZFwiOiB0cnVlLFxuICAgIHN0eWxlOiBoaWRkZW5HdWFyZFxuICB9KSwgY2hpbGRyZW4sIGNoaWxkcmVuICYmIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBrZXk6IFwiZ3VhcmQtbGFzdFwiLFxuICAgIFwiZGF0YS1mb2N1cy1ndWFyZFwiOiB0cnVlLFxuICAgIFwiZGF0YS1mb2N1cy1hdXRvLWd1YXJkXCI6IHRydWUsXG4gICAgc3R5bGU6IGhpZGRlbkd1YXJkXG4gIH0pKTtcbn07XG5JbkZvY3VzR3VhcmQucHJvcFR5cGVzID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiID8ge1xuICBjaGlsZHJlbjogUHJvcFR5cGVzLm5vZGVcbn0gOiB7fTtcbmV4cG9ydCBkZWZhdWx0IEluRm9jdXNHdWFyZDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/FocusGuard.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar FreeFocusInside = function FreeFocusInside(_ref) {\n  var children = _ref.children,\n    className = _ref.className;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_2__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_ALLOW, true), {\n    className: className\n  }), children);\n};\nFreeFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().node).isRequired,\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FreeFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9GcmVlRm9jdXNJbnNpZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDaEM7QUFDUztBQUNnQjtBQUNmO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUIsUUFBUSw4RUFBUSxHQUFHLEVBQUUsaURBQVUsQ0FBQyw2REFBVztBQUNwRjtBQUNBLEdBQUc7QUFDSDtBQUNBLDRCQUE0QixLQUFxQztBQUNqRSxZQUFZLHdEQUFjO0FBQzFCLGFBQWEsMERBQWdCO0FBQzdCLEVBQUUsRUFBRSxDQUFFO0FBQ04saUVBQWUsZUFBZSIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L0ZyZWVGb2N1c0luc2lkZS5qcz8zODYzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSAncHJvcC10eXBlcyc7XG5pbXBvcnQgeyBGT0NVU19BTExPVyB9IGZyb20gJ2ZvY3VzLWxvY2svY29uc3RhbnRzJztcbmltcG9ydCB7IGlubGluZVByb3AgfSBmcm9tICcuL3V0aWwnO1xudmFyIEZyZWVGb2N1c0luc2lkZSA9IGZ1bmN0aW9uIEZyZWVGb2N1c0luc2lkZShfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgY2xhc3NOYW1lID0gX3JlZi5jbGFzc05hbWU7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCBfZXh0ZW5kcyh7fSwgaW5saW5lUHJvcChGT0NVU19BTExPVywgdHJ1ZSksIHtcbiAgICBjbGFzc05hbWU6IGNsYXNzTmFtZVxuICB9KSwgY2hpbGRyZW4pO1xufTtcbkZyZWVGb2N1c0luc2lkZS5wcm9wVHlwZXMgPSBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIgPyB7XG4gIGNoaWxkcmVuOiBQcm9wVHlwZXMubm9kZS5pc1JlcXVpcmVkLFxuICBjbGFzc05hbWU6IFByb3BUeXBlcy5zdHJpbmdcbn0gOiB7fTtcbmV4cG9ydCBkZWZhdWx0IEZyZWVGb2N1c0luc2lkZTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/Lock.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/Lock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-callback-ref */ \"(ssr)/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js\");\n/* harmony import */ var _FocusGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FocusGuard */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/FocusGuard.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/medium.js\");\n/* harmony import */ var _scope__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./scope */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/scope.js\");\n\n\n\n\n\n\n\n\nvar emptyArray = [];\nvar FocusLock = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function FocusLockUI(props, parentRef) {\n  var _extends2;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n    realObserved = _useState[0],\n    setObserved = _useState[1];\n  var observed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  var isActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  var originalFocusedElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}),\n    update = _useState2[1];\n  var children = props.children,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    _props$noFocusGuards = props.noFocusGuards,\n    noFocusGuards = _props$noFocusGuards === void 0 ? false : _props$noFocusGuards,\n    _props$persistentFocu = props.persistentFocus,\n    persistentFocus = _props$persistentFocu === void 0 ? false : _props$persistentFocu,\n    _props$crossFrame = props.crossFrame,\n    crossFrame = _props$crossFrame === void 0 ? true : _props$crossFrame,\n    _props$autoFocus = props.autoFocus,\n    autoFocus = _props$autoFocus === void 0 ? true : _props$autoFocus,\n    allowTextSelection = props.allowTextSelection,\n    group = props.group,\n    className = props.className,\n    whiteList = props.whiteList,\n    hasPositiveIndices = props.hasPositiveIndices,\n    _props$shards = props.shards,\n    shards = _props$shards === void 0 ? emptyArray : _props$shards,\n    _props$as = props.as,\n    Container = _props$as === void 0 ? 'div' : _props$as,\n    _props$lockProps = props.lockProps,\n    containerProps = _props$lockProps === void 0 ? {} : _props$lockProps,\n    SideCar = props.sideCar,\n    _props$returnFocus = props.returnFocus,\n    shouldReturnFocus = _props$returnFocus === void 0 ? false : _props$returnFocus,\n    focusOptions = props.focusOptions,\n    onActivationCallback = props.onActivation,\n    onDeactivationCallback = props.onDeactivation;\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}),\n    id = _useState3[0];\n  var onActivation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (_ref) {\n    var captureFocusRestore = _ref.captureFocusRestore;\n    if (!originalFocusedElement.current) {\n      var _document;\n      var activeElement = (_document = document) == null ? void 0 : _document.activeElement;\n      originalFocusedElement.current = activeElement;\n      if (activeElement !== document.body) {\n        originalFocusedElement.current = captureFocusRestore(activeElement);\n      }\n    }\n    if (observed.current && onActivationCallback) {\n      onActivationCallback(observed.current);\n    }\n    isActive.current = true;\n    update();\n  }, [onActivationCallback]);\n  var onDeactivation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    isActive.current = false;\n    if (onDeactivationCallback) {\n      onDeactivationCallback(observed.current);\n    }\n    update();\n  }, [onDeactivationCallback]);\n  var returnFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (allowDefer) {\n    var focusRestore = originalFocusedElement.current;\n    if (focusRestore) {\n      var returnFocusTo = (typeof focusRestore === 'function' ? focusRestore() : focusRestore) || document.body;\n      var howToReturnFocus = typeof shouldReturnFocus === 'function' ? shouldReturnFocus(returnFocusTo) : shouldReturnFocus;\n      if (howToReturnFocus) {\n        var returnFocusOptions = typeof howToReturnFocus === 'object' ? howToReturnFocus : undefined;\n        originalFocusedElement.current = null;\n        if (allowDefer) {\n          Promise.resolve().then(function () {\n            return returnFocusTo.focus(returnFocusOptions);\n          });\n        } else {\n          returnFocusTo.focus(returnFocusOptions);\n        }\n      }\n    }\n  }, [shouldReturnFocus]);\n  var onFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (event) {\n    if (isActive.current) {\n      _medium__WEBPACK_IMPORTED_MODULE_2__.mediumFocus.useMedium(event);\n    }\n  }, []);\n  var onBlur = _medium__WEBPACK_IMPORTED_MODULE_2__.mediumBlur.useMedium;\n  var setObserveNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (newObserved) {\n    if (observed.current !== newObserved) {\n      observed.current = newObserved;\n      setObserved(newObserved);\n    }\n  }, []);\n  if (true) {\n    if (typeof allowTextSelection !== 'undefined') {\n      console.warn('React-Focus-Lock: allowTextSelection is deprecated and enabled by default');\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      if (!observed.current && typeof Container !== 'string') {\n        console.error('FocusLock: could not obtain ref to internal node');\n      }\n    }, []);\n  }\n  var lockProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((_extends2 = {}, _extends2[focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_DISABLED] = disabled && 'disabled', _extends2[focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_GROUP] = group, _extends2), containerProps);\n  var hasLeadingGuards = noFocusGuards !== true;\n  var hasTailingGuards = hasLeadingGuards && noFocusGuards !== 'tail';\n  var mergedRef = (0,use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useMergeRefs)([parentRef, setObserveNode]);\n  var focusScopeValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return {\n      observed: observed,\n      shards: shards,\n      enabled: !disabled,\n      active: isActive.current\n    };\n  }, [disabled, isActive.current, shards, realObserved]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, hasLeadingGuards && [\n  /*#__PURE__*/\n  react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 0,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }), hasPositiveIndices ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    key: \"guard-nearest\",\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 1,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }) : null], !disabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(SideCar, {\n    id: id,\n    sideCar: _medium__WEBPACK_IMPORTED_MODULE_2__.mediumSidecar,\n    observed: realObserved,\n    disabled: disabled,\n    persistentFocus: persistentFocus,\n    crossFrame: crossFrame,\n    autoFocus: autoFocus,\n    whiteList: whiteList,\n    shards: shards,\n    onActivation: onActivation,\n    onDeactivation: onDeactivation,\n    returnFocus: returnFocus,\n    focusOptions: focusOptions,\n    noFocusGuards: noFocusGuards\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(Container, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: mergedRef\n  }, lockProps, {\n    className: className,\n    onBlur: onBlur,\n    onFocus: onFocus\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_scope__WEBPACK_IMPORTED_MODULE_6__.focusScope.Provider, {\n    value: focusScopeValue\n  }, children)), hasTailingGuards && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 0,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }));\n});\nFocusLock.propTypes =  true ? {\n  children: prop_types__WEBPACK_IMPORTED_MODULE_7__.node,\n  disabled: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  returnFocus: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.oneOfType)([prop_types__WEBPACK_IMPORTED_MODULE_7__.bool, prop_types__WEBPACK_IMPORTED_MODULE_7__.object, prop_types__WEBPACK_IMPORTED_MODULE_7__.func]),\n  focusOptions: prop_types__WEBPACK_IMPORTED_MODULE_7__.object,\n  noFocusGuards: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  hasPositiveIndices: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  allowTextSelection: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  autoFocus: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  persistentFocus: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  crossFrame: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  group: prop_types__WEBPACK_IMPORTED_MODULE_7__.string,\n  className: prop_types__WEBPACK_IMPORTED_MODULE_7__.string,\n  whiteList: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  shards: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.arrayOf)(prop_types__WEBPACK_IMPORTED_MODULE_7__.any),\n  as: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.oneOfType)([prop_types__WEBPACK_IMPORTED_MODULE_7__.string, prop_types__WEBPACK_IMPORTED_MODULE_7__.func, prop_types__WEBPACK_IMPORTED_MODULE_7__.object]),\n  lockProps: prop_types__WEBPACK_IMPORTED_MODULE_7__.object,\n  onActivation: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  onDeactivation: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  sideCar: prop_types__WEBPACK_IMPORTED_MODULE_7__.any.isRequired\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FocusLock);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/Lock.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusInside: () => (/* binding */ useFocusInside)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/./node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/util.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/medium.js\");\n\n\n\n\n\n\nvar useFocusInside = function useFocusInside(observedRef) {\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var enabled = true;\n    _medium__WEBPACK_IMPORTED_MODULE_2__.mediumEffect.useMedium(function (car) {\n      var observed = observedRef && observedRef.current;\n      if (enabled && observed) {\n        if (!car.focusInside(observed)) {\n          car.moveFocusInside(observed, null);\n        }\n      }\n    });\n    return function () {\n      enabled = false;\n    };\n  }, [observedRef]);\n};\nfunction MoveFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n    isDisabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    className = _ref.className,\n    children = _ref.children;\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  useFocusInside(isDisabled ? undefined : ref);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_3__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_4__.FOCUS_AUTO, !isDisabled), {\n    ref: ref,\n    className: className\n  }), children);\n}\nMoveFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().node).isRequired,\n  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MoveFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/Trap.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/Trap.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react_clientside_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-clientside-effect */ \"(ssr)/./node_modules/react-clientside-effect/lib/index.es.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! focus-lock */ \"(ssr)/./node_modules/focus-lock/dist/es2015/focusIsHidden.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock */ \"(ssr)/./node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! focus-lock */ \"(ssr)/./node_modules/focus-lock/dist/es2015/focusInside.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! focus-lock */ \"(ssr)/./node_modules/focus-lock/dist/es2015/moveFocusInside.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! focus-lock */ \"(ssr)/./node_modules/focus-lock/dist/es2015/return-focus.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! focus-lock */ \"(ssr)/./node_modules/focus-lock/dist/es2015/focusables.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! focus-lock */ \"(ssr)/./node_modules/focus-lock/dist/es2015/sibling.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/util.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/medium.js\");\n\n\n\n\n\n\nvar focusOnBody = function focusOnBody() {\n  return document && document.activeElement === document.body;\n};\nvar isFreeFocus = function isFreeFocus() {\n  return focusOnBody() || (0,focus_lock__WEBPACK_IMPORTED_MODULE_2__.focusIsHidden)();\n};\nvar lastActiveTrap = null;\nvar lastActiveFocus = null;\nvar tryRestoreFocus = function tryRestoreFocus() {\n  return null;\n};\nvar lastPortaledElement = null;\nvar focusWasOutsideWindow = false;\nvar windowFocused = false;\nvar defaultWhitelist = function defaultWhitelist() {\n  return true;\n};\nvar focusWhitelisted = function focusWhitelisted(activeElement) {\n  return (lastActiveTrap.whiteList || defaultWhitelist)(activeElement);\n};\nvar recordPortal = function recordPortal(observerNode, portaledElement) {\n  lastPortaledElement = {\n    observerNode: observerNode,\n    portaledElement: portaledElement\n  };\n};\nvar focusIsPortaledPair = function focusIsPortaledPair(element) {\n  return lastPortaledElement && lastPortaledElement.portaledElement === element;\n};\nfunction autoGuard(startIndex, end, step, allNodes) {\n  var lastGuard = null;\n  var i = startIndex;\n  do {\n    var item = allNodes[i];\n    if (item.guard) {\n      if (item.node.dataset.focusAutoGuard) {\n        lastGuard = item;\n      }\n    } else if (item.lockItem) {\n      if (i !== startIndex) {\n        return;\n      }\n      lastGuard = null;\n    } else {\n      break;\n    }\n  } while ((i += step) !== end);\n  if (lastGuard) {\n    lastGuard.node.tabIndex = 0;\n  }\n}\nvar focusWasOutside = function focusWasOutside(crossFrameOption) {\n  if (crossFrameOption) {\n    return Boolean(focusWasOutsideWindow);\n  }\n  return focusWasOutsideWindow === 'meanwhile';\n};\nvar checkInHost = function checkInHost(check, el, boundary) {\n  return el && (el.host === check && (!el.activeElement || boundary.contains(el.activeElement)) || el.parentNode && checkInHost(check, el.parentNode, boundary));\n};\nvar withinHost = function withinHost(activeElement, workingArea) {\n  return workingArea.some(function (area) {\n    return checkInHost(activeElement, area, area);\n  });\n};\nvar getNodeFocusables = function getNodeFocusables(nodes) {\n  return (0,focus_lock__WEBPACK_IMPORTED_MODULE_3__.getFocusableNodes)(nodes, new Map());\n};\nvar isNotFocusable = function isNotFocusable(node) {\n  return !getNodeFocusables([node.parentNode]).some(function (el) {\n    return el.node === node;\n  });\n};\nvar activateTrap = function activateTrap() {\n  var result = false;\n  if (lastActiveTrap) {\n    var _lastActiveTrap = lastActiveTrap,\n      observed = _lastActiveTrap.observed,\n      persistentFocus = _lastActiveTrap.persistentFocus,\n      autoFocus = _lastActiveTrap.autoFocus,\n      shards = _lastActiveTrap.shards,\n      crossFrame = _lastActiveTrap.crossFrame,\n      focusOptions = _lastActiveTrap.focusOptions,\n      noFocusGuards = _lastActiveTrap.noFocusGuards;\n    var workingNode = observed || lastPortaledElement && lastPortaledElement.portaledElement;\n    if (focusOnBody() && lastActiveFocus && lastActiveFocus !== document.body) {\n      if (!document.body.contains(lastActiveFocus) || isNotFocusable(lastActiveFocus)) {\n        var newTarget = tryRestoreFocus();\n        if (newTarget) {\n          newTarget.focus();\n        }\n      }\n    }\n    var activeElement = document && document.activeElement;\n    if (workingNode) {\n      var workingArea = [workingNode].concat(shards.map(_util__WEBPACK_IMPORTED_MODULE_4__.extractRef).filter(Boolean));\n      var shouldForceRestoreFocus = function shouldForceRestoreFocus() {\n        if (!focusWasOutside(crossFrame) || !noFocusGuards || !lastActiveFocus || windowFocused) {\n          return false;\n        }\n        var nodes = getNodeFocusables(workingArea);\n        var lastIndex = nodes.findIndex(function (_ref) {\n          var node = _ref.node;\n          return node === lastActiveFocus;\n        });\n        return lastIndex === 0 || lastIndex === nodes.length - 1;\n      };\n      if (!activeElement || focusWhitelisted(activeElement)) {\n        if (persistentFocus || shouldForceRestoreFocus() || !isFreeFocus() || !lastActiveFocus && autoFocus) {\n          if (workingNode && !((0,focus_lock__WEBPACK_IMPORTED_MODULE_5__.focusInside)(workingArea) || activeElement && withinHost(activeElement, workingArea) || focusIsPortaledPair(activeElement, workingNode))) {\n            if (document && !lastActiveFocus && activeElement && !autoFocus) {\n              if (activeElement.blur) {\n                activeElement.blur();\n              }\n              document.body.focus();\n            } else {\n              result = (0,focus_lock__WEBPACK_IMPORTED_MODULE_6__.moveFocusInside)(workingArea, lastActiveFocus, {\n                focusOptions: focusOptions\n              });\n              lastPortaledElement = {};\n            }\n          }\n          lastActiveFocus = document && document.activeElement;\n          if (lastActiveFocus !== document.body) {\n            tryRestoreFocus = (0,focus_lock__WEBPACK_IMPORTED_MODULE_7__.captureFocusRestore)(lastActiveFocus);\n          }\n          focusWasOutsideWindow = false;\n        }\n      }\n      if (document && activeElement !== document.activeElement && document.querySelector('[data-focus-auto-guard]')) {\n        var newActiveElement = document && document.activeElement;\n        var allNodes = (0,focus_lock__WEBPACK_IMPORTED_MODULE_8__.expandFocusableNodes)(workingArea);\n        var focusedIndex = allNodes.map(function (_ref2) {\n          var node = _ref2.node;\n          return node;\n        }).indexOf(newActiveElement);\n        if (focusedIndex > -1) {\n          allNodes.filter(function (_ref3) {\n            var guard = _ref3.guard,\n              node = _ref3.node;\n            return guard && node.dataset.focusAutoGuard;\n          }).forEach(function (_ref4) {\n            var node = _ref4.node;\n            return node.removeAttribute('tabIndex');\n          });\n          autoGuard(focusedIndex, allNodes.length, +1, allNodes);\n          autoGuard(focusedIndex, -1, -1, allNodes);\n        }\n      }\n    }\n  }\n  return result;\n};\nvar onTrap = function onTrap(event) {\n  if (activateTrap() && event) {\n    event.stopPropagation();\n    event.preventDefault();\n  }\n};\nvar onBlur = function onBlur() {\n  return (0,_util__WEBPACK_IMPORTED_MODULE_4__.deferAction)(activateTrap);\n};\nvar onFocus = function onFocus(event) {\n  var source = event.target;\n  var currentNode = event.currentTarget;\n  if (!currentNode.contains(source)) {\n    recordPortal(currentNode, source);\n  }\n};\nvar FocusWatcher = function FocusWatcher() {\n  return null;\n};\nvar FocusTrap = function FocusTrap(_ref5) {\n  var children = _ref5.children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    onBlur: onBlur,\n    onFocus: onFocus\n  }, children);\n};\nFocusTrap.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_9___default().node).isRequired\n} : 0;\nvar onWindowFocus = function onWindowFocus() {\n  windowFocused = true;\n};\nvar onWindowBlur = function onWindowBlur() {\n  windowFocused = false;\n  focusWasOutsideWindow = 'just';\n  (0,_util__WEBPACK_IMPORTED_MODULE_4__.deferAction)(function () {\n    focusWasOutsideWindow = 'meanwhile';\n  });\n};\nvar attachHandler = function attachHandler() {\n  document.addEventListener('focusin', onTrap);\n  document.addEventListener('focusout', onBlur);\n  window.addEventListener('focus', onWindowFocus);\n  window.addEventListener('blur', onWindowBlur);\n};\nvar detachHandler = function detachHandler() {\n  document.removeEventListener('focusin', onTrap);\n  document.removeEventListener('focusout', onBlur);\n  window.removeEventListener('focus', onWindowFocus);\n  window.removeEventListener('blur', onWindowBlur);\n};\nfunction reducePropsToState(propsList) {\n  return propsList.filter(function (_ref6) {\n    var disabled = _ref6.disabled;\n    return !disabled;\n  });\n}\nvar focusLockAPI = {\n  moveFocusInside: focus_lock__WEBPACK_IMPORTED_MODULE_6__.moveFocusInside,\n  focusInside: focus_lock__WEBPACK_IMPORTED_MODULE_5__.focusInside,\n  focusNextElement: focus_lock__WEBPACK_IMPORTED_MODULE_10__.focusNextElement,\n  focusPrevElement: focus_lock__WEBPACK_IMPORTED_MODULE_10__.focusPrevElement,\n  focusFirstElement: focus_lock__WEBPACK_IMPORTED_MODULE_10__.focusFirstElement,\n  focusLastElement: focus_lock__WEBPACK_IMPORTED_MODULE_10__.focusLastElement,\n  captureFocusRestore: focus_lock__WEBPACK_IMPORTED_MODULE_7__.captureFocusRestore\n};\nfunction handleStateChangeOnClient(traps) {\n  var trap = traps.slice(-1)[0];\n  if (trap && !lastActiveTrap) {\n    attachHandler();\n  }\n  var lastTrap = lastActiveTrap;\n  var sameTrap = lastTrap && trap && trap.id === lastTrap.id;\n  lastActiveTrap = trap;\n  if (lastTrap && !sameTrap) {\n    lastTrap.onDeactivation();\n    if (!traps.filter(function (_ref7) {\n      var id = _ref7.id;\n      return id === lastTrap.id;\n    }).length) {\n      lastTrap.returnFocus(!trap);\n    }\n  }\n  if (trap) {\n    lastActiveFocus = null;\n    if (!sameTrap || lastTrap.observed !== trap.observed) {\n      trap.onActivation(focusLockAPI);\n    }\n    activateTrap(true);\n    (0,_util__WEBPACK_IMPORTED_MODULE_4__.deferAction)(activateTrap);\n  } else {\n    detachHandler();\n    lastActiveFocus = null;\n  }\n}\n_medium__WEBPACK_IMPORTED_MODULE_11__.mediumFocus.assignSyncMedium(onFocus);\n_medium__WEBPACK_IMPORTED_MODULE_11__.mediumBlur.assignMedium(onBlur);\n_medium__WEBPACK_IMPORTED_MODULE_11__.mediumEffect.assignMedium(function (cb) {\n  return cb(focusLockAPI);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_clientside_effect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(reducePropsToState, handleStateChangeOnClient)(FocusWatcher));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/Trap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/UI.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/UI.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoFocusInside: () => (/* reexport safe */ _AutoFocusInside__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   FocusLockUI: () => (/* reexport safe */ _Lock__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   FreeFocusInside: () => (/* reexport safe */ _FreeFocusInside__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   InFocusGuard: () => (/* reexport safe */ _FocusGuard__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MoveFocusInside: () => (/* reexport safe */ _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusController: () => (/* reexport safe */ _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__.useFocusController),\n/* harmony export */   useFocusInside: () => (/* reexport safe */ _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__.useFocusInside),\n/* harmony export */   useFocusScope: () => (/* reexport safe */ _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__.useFocusScope),\n/* harmony export */   useFocusState: () => (/* reexport safe */ _use_focus_state__WEBPACK_IMPORTED_MODULE_6__.useFocusState)\n/* harmony export */ });\n/* harmony import */ var _Lock__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Lock */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/Lock.js\");\n/* harmony import */ var _AutoFocusInside__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AutoFocusInside */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js\");\n/* harmony import */ var _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MoveFocusInside */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js\");\n/* harmony import */ var _FreeFocusInside__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FreeFocusInside */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js\");\n/* harmony import */ var _FocusGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FocusGuard */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/FocusGuard.js\");\n/* harmony import */ var _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./use-focus-scope */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/use-focus-scope.js\");\n/* harmony import */ var _use_focus_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./use-focus-state */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/use-focus-state.js\");\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Lock__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9VSS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNlO0FBQ29CO0FBQ3BCO0FBQ1I7QUFDOEI7QUFDcEI7QUFDd0c7QUFDMUosaUVBQWUsNkNBQVciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9VSS5qcz9jY2I5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBGb2N1c0xvY2tVSSBmcm9tICcuL0xvY2snO1xuaW1wb3J0IEF1dG9Gb2N1c0luc2lkZSBmcm9tICcuL0F1dG9Gb2N1c0luc2lkZSc7XG5pbXBvcnQgTW92ZUZvY3VzSW5zaWRlLCB7IHVzZUZvY3VzSW5zaWRlIH0gZnJvbSAnLi9Nb3ZlRm9jdXNJbnNpZGUnO1xuaW1wb3J0IEZyZWVGb2N1c0luc2lkZSBmcm9tICcuL0ZyZWVGb2N1c0luc2lkZSc7XG5pbXBvcnQgSW5Gb2N1c0d1YXJkIGZyb20gJy4vRm9jdXNHdWFyZCc7XG5pbXBvcnQgeyB1c2VGb2N1c0NvbnRyb2xsZXIsIHVzZUZvY3VzU2NvcGUgfSBmcm9tICcuL3VzZS1mb2N1cy1zY29wZSc7XG5pbXBvcnQgeyB1c2VGb2N1c1N0YXRlIH0gZnJvbSAnLi91c2UtZm9jdXMtc3RhdGUnO1xuZXhwb3J0IHsgQXV0b0ZvY3VzSW5zaWRlLCBNb3ZlRm9jdXNJbnNpZGUsIEZyZWVGb2N1c0luc2lkZSwgSW5Gb2N1c0d1YXJkLCBGb2N1c0xvY2tVSSwgdXNlRm9jdXNJbnNpZGUsIHVzZUZvY3VzQ29udHJvbGxlciwgdXNlRm9jdXNTY29wZSwgdXNlRm9jdXNTdGF0ZSB9O1xuZXhwb3J0IGRlZmF1bHQgRm9jdXNMb2NrVUk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/UI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.AutoFocusInside),\n/* harmony export */   FocusLockUI: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.FocusLockUI),\n/* harmony export */   FreeFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.FreeFocusInside),\n/* harmony export */   InFocusGuard: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.InFocusGuard),\n/* harmony export */   MoveFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.MoveFocusInside),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusController: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusController),\n/* harmony export */   useFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusInside),\n/* harmony export */   useFocusScope: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusScope),\n/* harmony export */   useFocusState: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusState)\n/* harmony export */ });\n/* harmony import */ var _Combination__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Combination */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/Combination.js\");\n/* harmony import */ var _UI__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UI */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/UI.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Combination__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0M7QUFDakI7QUFDckIsaUVBQWUsb0RBQVMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9pbmRleC5qcz9iZjlkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBGb2N1c0xvY2sgZnJvbSAnLi9Db21iaW5hdGlvbic7XG5leHBvcnQgKiBmcm9tICcuL1VJJztcbmV4cG9ydCBkZWZhdWx0IEZvY3VzTG9jazsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/medium.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/medium.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mediumBlur: () => (/* binding */ mediumBlur),\n/* harmony export */   mediumEffect: () => (/* binding */ mediumEffect),\n/* harmony export */   mediumFocus: () => (/* binding */ mediumFocus),\n/* harmony export */   mediumSidecar: () => (/* binding */ mediumSidecar)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(ssr)/./node_modules/use-sidecar/dist/es2015/medium.js\");\n\nvar mediumFocus = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)({}, function (_ref) {\n  var target = _ref.target,\n    currentTarget = _ref.currentTarget;\n  return {\n    target: target,\n    currentTarget: currentTarget\n  };\n});\nvar mediumBlur = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)();\nvar mediumEffect = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)();\nvar mediumSidecar = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createSidecarMedium)({\n  async: true,\n  ssr: typeof document !== 'undefined'\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9tZWRpdW0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0U7QUFDekQsa0JBQWtCLHlEQUFZLEdBQUc7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNLGlCQUFpQix5REFBWTtBQUM3QixtQkFBbUIseURBQVk7QUFDL0Isb0JBQW9CLGdFQUFtQjtBQUM5QztBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NoYWtyYS8uL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L21lZGl1bS5qcz8wODFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZU1lZGl1bSwgY3JlYXRlU2lkZWNhck1lZGl1bSB9IGZyb20gJ3VzZS1zaWRlY2FyJztcbmV4cG9ydCB2YXIgbWVkaXVtRm9jdXMgPSBjcmVhdGVNZWRpdW0oe30sIGZ1bmN0aW9uIChfcmVmKSB7XG4gIHZhciB0YXJnZXQgPSBfcmVmLnRhcmdldCxcbiAgICBjdXJyZW50VGFyZ2V0ID0gX3JlZi5jdXJyZW50VGFyZ2V0O1xuICByZXR1cm4ge1xuICAgIHRhcmdldDogdGFyZ2V0LFxuICAgIGN1cnJlbnRUYXJnZXQ6IGN1cnJlbnRUYXJnZXRcbiAgfTtcbn0pO1xuZXhwb3J0IHZhciBtZWRpdW1CbHVyID0gY3JlYXRlTWVkaXVtKCk7XG5leHBvcnQgdmFyIG1lZGl1bUVmZmVjdCA9IGNyZWF0ZU1lZGl1bSgpO1xuZXhwb3J0IHZhciBtZWRpdW1TaWRlY2FyID0gY3JlYXRlU2lkZWNhck1lZGl1bSh7XG4gIGFzeW5jOiB0cnVlLFxuICBzc3I6IHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCdcbn0pOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/medium.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/nano-events.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/nano-events.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNanoEvents: () => (/* binding */ createNanoEvents)\n/* harmony export */ });\nvar createNanoEvents = function createNanoEvents() {\n  return {\n    emit: function emit(event) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      for (var i = 0, callbacks = this.events[event] || [], length = callbacks.length; i < length; i++) {\n        callbacks[i].apply(callbacks, args);\n      }\n    },\n    events: {},\n    on: function on(event, cb) {\n      var _this$events,\n        _this = this;\n      ((_this$events = this.events)[event] || (_this$events[event] = [])).push(cb);\n      return function () {\n        var _this$events$event;\n        _this.events[event] = (_this$events$event = _this.events[event]) == null ? void 0 : _this$events$event.filter(function (i) {\n          return cb !== i;\n        });\n      };\n    }\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9uYW5vLWV2ZW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EsNkZBQTZGLGFBQWE7QUFDMUc7QUFDQTtBQUNBLHVGQUF1RixZQUFZO0FBQ25HO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9uYW5vLWV2ZW50cy5qcz9jMWEzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgY3JlYXRlTmFub0V2ZW50cyA9IGZ1bmN0aW9uIGNyZWF0ZU5hbm9FdmVudHMoKSB7XG4gIHJldHVybiB7XG4gICAgZW1pdDogZnVuY3Rpb24gZW1pdChldmVudCkge1xuICAgICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiA+IDEgPyBfbGVuIC0gMSA6IDApLCBfa2V5ID0gMTsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgICAgICBhcmdzW19rZXkgLSAxXSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICAgIH1cbiAgICAgIGZvciAodmFyIGkgPSAwLCBjYWxsYmFja3MgPSB0aGlzLmV2ZW50c1tldmVudF0gfHwgW10sIGxlbmd0aCA9IGNhbGxiYWNrcy5sZW5ndGg7IGkgPCBsZW5ndGg7IGkrKykge1xuICAgICAgICBjYWxsYmFja3NbaV0uYXBwbHkoY2FsbGJhY2tzLCBhcmdzKTtcbiAgICAgIH1cbiAgICB9LFxuICAgIGV2ZW50czoge30sXG4gICAgb246IGZ1bmN0aW9uIG9uKGV2ZW50LCBjYikge1xuICAgICAgdmFyIF90aGlzJGV2ZW50cyxcbiAgICAgICAgX3RoaXMgPSB0aGlzO1xuICAgICAgKChfdGhpcyRldmVudHMgPSB0aGlzLmV2ZW50cylbZXZlbnRdIHx8IChfdGhpcyRldmVudHNbZXZlbnRdID0gW10pKS5wdXNoKGNiKTtcbiAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBfdGhpcyRldmVudHMkZXZlbnQ7XG4gICAgICAgIF90aGlzLmV2ZW50c1tldmVudF0gPSAoX3RoaXMkZXZlbnRzJGV2ZW50ID0gX3RoaXMuZXZlbnRzW2V2ZW50XSkgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJGV2ZW50cyRldmVudC5maWx0ZXIoZnVuY3Rpb24gKGkpIHtcbiAgICAgICAgICByZXR1cm4gY2IgIT09IGk7XG4gICAgICAgIH0pO1xuICAgICAgfTtcbiAgICB9XG4gIH07XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/nano-events.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/scope.js":
/*!************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/scope.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusScope: () => (/* binding */ focusScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar focusScope = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9zY29wZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0IsOEJBQThCLG9EQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hha3JhLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvc2NvcGUuanM/NzJjYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBmb2N1c1Njb3BlID0gLyojX19QVVJFX18qL2NyZWF0ZUNvbnRleHQodW5kZWZpbmVkKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/scope.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/use-focus-scope.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/use-focus-scope.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusController: () => (/* binding */ useFocusController),\n/* harmony export */   useFocusScope: () => (/* binding */ useFocusScope)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _scope__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scope */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/scope.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/medium.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar collapseRefs = function collapseRefs(shards) {\n  return shards.map(_util__WEBPACK_IMPORTED_MODULE_2__.extractRef).filter(Boolean);\n};\nvar withMedium = function withMedium(fn) {\n  return new Promise(function (resolve) {\n    return _medium__WEBPACK_IMPORTED_MODULE_3__.mediumEffect.useMedium(function () {\n      resolve(fn.apply(void 0, arguments));\n    });\n  });\n};\nvar useFocusController = function useFocusController() {\n  for (var _len = arguments.length, shards = new Array(_len), _key = 0; _key < _len; _key++) {\n    shards[_key] = arguments[_key];\n  }\n  if (!shards.length) {\n    throw new Error('useFocusController requires at least one target element');\n  }\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(shards);\n  ref.current = shards;\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return {\n      autoFocus: function autoFocus(focusOptions) {\n        if (focusOptions === void 0) {\n          focusOptions = {};\n        }\n        return withMedium(function (car) {\n          return car.moveFocusInside(collapseRefs(ref.current), null, focusOptions);\n        });\n      },\n      focusNext: function focusNext(options) {\n        return withMedium(function (car) {\n          car.moveFocusInside(collapseRefs(ref.current), null);\n          car.focusNextElement(document.activeElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            scope: collapseRefs(ref.current)\n          }, options));\n        });\n      },\n      focusPrev: function focusPrev(options) {\n        return withMedium(function (car) {\n          car.moveFocusInside(collapseRefs(ref.current), null);\n          car.focusPrevElement(document.activeElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            scope: collapseRefs(ref.current)\n          }, options));\n        });\n      },\n      focusFirst: function focusFirst(options) {\n        return withMedium(function (car) {\n          car.focusFirstElement(collapseRefs(ref.current), options);\n        });\n      },\n      focusLast: function focusLast(options) {\n        return withMedium(function (car) {\n          car.focusLastElement(collapseRefs(ref.current), options);\n        });\n      }\n    };\n  }, []);\n};\nvar useFocusScope = function useFocusScope() {\n  var scope = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_scope__WEBPACK_IMPORTED_MODULE_4__.focusScope);\n  if (!scope) {\n    throw new Error('FocusLock is required to operate with FocusScope');\n  }\n  return useFocusController.apply(void 0, [scope.observed].concat(scope.shards));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/use-focus-scope.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/use-focus-state.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/use-focus-state.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusState: () => (/* binding */ useFocusState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nano_events__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nano-events */ \"(ssr)/./node_modules/react-focus-lock/dist/es2015/nano-events.js\");\n\n\nvar mainbus = (0,_nano_events__WEBPACK_IMPORTED_MODULE_1__.createNanoEvents)();\nvar subscribeCounter = 0;\nvar onFocusIn = function onFocusIn(event) {\n  return mainbus.emit('assign', event.target);\n};\nvar onFocusOut = function onFocusOut(event) {\n  return mainbus.emit('reset', event.target);\n};\nvar useDocumentFocusSubscribe = function useDocumentFocusSubscribe() {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (!subscribeCounter) {\n      document.addEventListener('focusin', onFocusIn);\n      document.addEventListener('focusout', onFocusOut);\n    }\n    subscribeCounter += 1;\n    return function () {\n      subscribeCounter -= 1;\n      if (!subscribeCounter) {\n        document.removeEventListener('focusin', onFocusIn);\n        document.removeEventListener('focusout', onFocusOut);\n      }\n    };\n  }, []);\n};\nvar getFocusState = function getFocusState(target, current) {\n  if (target === current) {\n    return 'self';\n  }\n  if (current.contains(target)) {\n    return 'within';\n  }\n  return 'within-boundary';\n};\nvar useFocusState = function useFocusState(callbacks) {\n  if (callbacks === void 0) {\n    callbacks = {};\n  }\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),\n    active = _useState[0],\n    setActive = _useState[1];\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(''),\n    state = _useState2[0],\n    setState = _useState2[1];\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  var focusState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  var stateTracker = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (ref.current) {\n      var isAlreadyFocused = ref.current === document.activeElement || ref.current.contains(document.activeElement);\n      setActive(isAlreadyFocused);\n      setState(getFocusState(document.activeElement, ref.current));\n      if (isAlreadyFocused && callbacks.onFocus) {\n        callbacks.onFocus();\n      }\n    }\n  }, []);\n  var onFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (e) {\n    focusState.current = {\n      focused: true,\n      state: getFocusState(e.target, e.currentTarget)\n    };\n  }, []);\n  useDocumentFocusSubscribe();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    var fout = mainbus.on('reset', function () {\n      focusState.current = {};\n    });\n    var fin = mainbus.on('assign', function () {\n      var newState = focusState.current.focused || false;\n      setActive(newState);\n      setState(focusState.current.state || '');\n      if (newState !== stateTracker.current) {\n        stateTracker.current = newState;\n        if (newState) {\n          callbacks.onFocus && callbacks.onFocus();\n        } else {\n          callbacks.onBlur && callbacks.onBlur();\n        }\n      }\n    });\n    return function () {\n      fout();\n      fin();\n    };\n  }, []);\n  return {\n    active: active,\n    state: state,\n    onFocus: onFocus,\n    ref: ref\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/use-focus-state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-focus-lock/dist/es2015/util.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-focus-lock/dist/es2015/util.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deferAction: () => (/* binding */ deferAction),\n/* harmony export */   extractRef: () => (/* binding */ extractRef),\n/* harmony export */   inlineProp: () => (/* binding */ inlineProp)\n/* harmony export */ });\nfunction deferAction(action) {\n  setTimeout(action, 1);\n}\nvar inlineProp = function inlineProp(name, value) {\n  var obj = {};\n  obj[name] = value;\n  return obj;\n};\nvar extractRef = function extractRef(ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaGFrcmEvLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlsLmpzPzIwNWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRlZmVyQWN0aW9uKGFjdGlvbikge1xuICBzZXRUaW1lb3V0KGFjdGlvbiwgMSk7XG59XG5leHBvcnQgdmFyIGlubGluZVByb3AgPSBmdW5jdGlvbiBpbmxpbmVQcm9wKG5hbWUsIHZhbHVlKSB7XG4gIHZhciBvYmogPSB7fTtcbiAgb2JqW25hbWVdID0gdmFsdWU7XG4gIHJldHVybiBvYmo7XG59O1xuZXhwb3J0IHZhciBleHRyYWN0UmVmID0gZnVuY3Rpb24gZXh0cmFjdFJlZihyZWYpIHtcbiAgcmV0dXJuIHJlZiAmJiAnY3VycmVudCcgaW4gcmVmID8gcmVmLmN1cnJlbnQgOiByZWY7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-focus-lock/dist/es2015/util.js\n");

/***/ })

};
;