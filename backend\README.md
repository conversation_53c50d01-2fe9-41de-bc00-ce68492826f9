# Nexsol Tech ERP Backend

## Overview
Welcome to the ERP application developed by **Nexsol Tech**. This project aims to streamline and automate various business processes within an organization, enhancing efficiency and productivity.

## Installation
To get started with the ERP application, follow these steps:

1. **Clone the repository**:
    ```bash
    git clone https://github.com/nexsoltech-official/ImpexGrace_BackEnd.git
    ```
2. **Navigate to the project directory**:
    ```bash
    cd ImpexGrace_BackEnd
    ```
3. **Node Version**:
    ```bash
    nvm use 20.12.0
    ```
4. **Install dependencies**:
    ```bash
    npm install
    ```

## Usage
To run the application locally, use the following command:
```bash
npm run start
```
The application will be available at http://localhost:5000.

Contact
For any inquiries or support, please contact <NAME_EMAIL>.

Thank you for using the Nexsol Tech ERP application!
