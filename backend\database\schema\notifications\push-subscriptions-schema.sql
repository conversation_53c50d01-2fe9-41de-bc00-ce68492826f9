-- Create PushSubscriptions table
CREATE TABLE PushSubscriptions (
    ID BIGINT IDENTITY(1,1) PRIMARY KEY,
    UserID NVARCHAR(32) NOT NULL,
    Subscription NVARCHAR(MAX) NOT NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME2 NULL,
    CONSTRAINT FK_PushSubscriptions_Users FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- Create index for faster lookups
CREATE INDEX IX_PushSubscriptions_UserID ON PushSubscriptions(UserID);
