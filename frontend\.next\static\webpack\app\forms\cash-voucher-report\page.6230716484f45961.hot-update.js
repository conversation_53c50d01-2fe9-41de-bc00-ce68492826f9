"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/spinner/spinner.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table-container.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @src/components/Custom/ServerPaginatedTable */ \"(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        totalCount: 0,\n        totalPages: 0\n    });\n    const [selectedVoucher, setSelectedVoucher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voucherDetails, setVoucherDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentHistory, setPaymentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalLoading, setModalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const fetchData = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n            console.log(response.data);\n            if (response.data && response.data.data) {\n                setData(response.data.data);\n                setPagination({\n                    page: response.data.page,\n                    pageSize: response.data.pageSize,\n                    totalCount: response.data.totalCount,\n                    totalPages: response.data.totalPages\n                });\n            } else {\n                setData([]);\n                setPagination({\n                    page: 1,\n                    pageSize: 20,\n                    totalCount: 0,\n                    totalPages: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch cash voucher report data\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchData();\n    }, []);\n    const fetchVoucherDetails = async (voucherNo)=>{\n        try {\n            setModalLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/voucher-details?voucherNo=\".concat(voucherNo));\n            if (response.data) {\n                setVoucherDetails(response.data.voucher_details);\n                setPaymentHistory(response.data.payment_history || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching voucher details: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch voucher details\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setModalLoading(false);\n        }\n    };\n    const handleOpenModal = async (voucher)=>{\n        setSelectedVoucher(voucher);\n        setIsModalOpen(true);\n        await fetchVoucherDetails(voucher.voucher_no);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedVoucher(null);\n        setVoucherDetails(null);\n        setPaymentHistory([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"voucher_no\"\n        },\n        {\n            header: \"Quotation No (adj)\",\n            field: \"quotation_no\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"client_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"client_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"gross_amount\",\n            render: (item)=>formatCurrency(item.gross_amount)\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"paid_amount\",\n            render: (item)=>formatCurrency(item.paid_amount)\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"remaining_amount\",\n            render: (item)=>formatCurrency(item.remaining_amount)\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        },\n        {\n            header: \"Actions\",\n            field: \"actions\",\n            render: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                    colorScheme: \"blue\",\n                    size: \"sm\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleOpenModal(item);\n                    },\n                    children: \"View Details\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 168,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    const getRowCursor = ()=>\"pointer\";\n    const handleRowClick = (item)=>{\n        handleOpenModal(item);\n    };\n    const handlePageChange = (newPage)=>{\n        fetchData(newPage, pagination.pageSize);\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        fetchData(1, newPageSize);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 199,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Cash Voucher Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            data: data,\n                                            columns: columns,\n                                            pagination: pagination,\n                                            onPageChange: handlePageChange,\n                                            onPageSizeChange: handlePageSizeChange,\n                                            onRowClick: handleRowClick,\n                                            getRowCursor: getRowCursor,\n                                            getBadgeColor: getStatusColor,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                lineNumber: 205,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 204,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 203,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 202,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Modal, {\n                    isOpen: isModalOpen,\n                    onClose: handleCloseModal,\n                    size: \"6xl\",\n                    scrollBehavior: \"inside\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalOverlay, {}, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalContent, {\n                            maxH: \"90vh\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalHeader, {\n                                    bg: \"blue.600\",\n                                    color: \"white\",\n                                    borderTopRadius: \"md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                        fontSize: \"xl\",\n                                        fontWeight: \"bold\",\n                                        children: [\n                                            \"Voucher Details - \",\n                                            selectedVoucher === null || selectedVoucher === void 0 ? void 0 : selectedVoucher.voucher_no\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.ModalCloseButton, {\n                                    color: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalBody, {\n                                    p: 6,\n                                    children: modalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        alignItems: \"center\",\n                                        minH: \"200px\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Spinner, {\n                                            size: \"xl\",\n                                            color: \"blue.500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 37\n                                    }, undefined) : voucherDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                        spacing: 6,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                p: 4,\n                                                bg: \"gray.50\",\n                                                borderRadius: \"md\",\n                                                border: \"1px solid\",\n                                                borderColor: \"gray.200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: \"Voucher Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Voucher No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.voucher_no\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Badge, {\n                                                                        colorScheme: getStatusColor(voucherDetails.status),\n                                                                        fontSize: \"sm\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        children: voucherDetails.status.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Divider, {\n                                                        my: 4\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Gross Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"blue.600\",\n                                                                        children: formatCurrency(voucherDetails.gross_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Paid Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"green.600\",\n                                                                        children: formatCurrency(voucherDetails.paid_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Remaining Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"red.600\",\n                                                                        children: formatCurrency(voucherDetails.remaining_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Created Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.created_at ? dayjs__WEBPACK_IMPORTED_MODULE_6___default()(voucherDetails.created_at).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: [\n                                                            \"Payment History (\",\n                                                            paymentHistory.length,\n                                                            \" payments)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    paymentHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.TableContainer, {\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        borderRadius: \"md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Table, {\n                                                            variant: \"simple\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.Thead, {\n                                                                    bg: \"gray.100\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.Tr, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Payment Voucher No\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Payment Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                isNumeric: true,\n                                                                                children: \"Amount\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 65\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 57\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Tbody, {\n                                                                    children: paymentHistory.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.Tr, {\n                                                                            _hover: {\n                                                                                bg: \"gray.50\"\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    fontWeight: \"semibold\",\n                                                                                    children: payment.payment_voucher_no\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 361,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Badge, {\n                                                                                        colorScheme: payment.payment_type === \"CR\" ? \"green\" : \"blue\",\n                                                                                        variant: \"subtle\",\n                                                                                        children: payment.payment_type === \"CR\" ? \"Cash Receipt\" : payment.payment_type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                        lineNumber: 365,\n                                                                                        columnNumber: 73\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 364,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    isNumeric: true,\n                                                                                    fontWeight: \"semibold\",\n                                                                                    color: \"green.600\",\n                                                                                    children: formatCurrency(payment.payment_amount)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 372,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    children: payment.payment_date ? dayjs__WEBPACK_IMPORTED_MODULE_6___default()(payment.payment_date).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 375,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    children: payment.payment_description || \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 381,\n                                                                                    columnNumber: 69\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 65\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 49\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                        p: 8,\n                                                        textAlign: \"center\",\n                                                        bg: \"gray.50\",\n                                                        borderRadius: \"md\",\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                color: \"gray.500\",\n                                                                fontSize: \"lg\",\n                                                                children: \"No payments found for this voucher\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                color: \"gray.400\",\n                                                                fontSize: \"sm\",\n                                                                mt: 1,\n                                                                children: \"This voucher is still pending payment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 37\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                        textAlign: \"center\",\n                                        py: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            color: \"gray.500\",\n                                            children: \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 248,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"to0F4F3T6vvp+5aeff+YHPpyvK4=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ })

});