-- Notification System Constraints and Indexes Setup Script
-- Run this script AFTER running setup-notifications-safe.sql

PRINT 'Adding foreign key constraints and indexes to notification tables...';

-- Step 1: Add foreign key constraints
PRINT 'Adding foreign key constraints...';

-- NotificationTemplates foreign key to NotificationTypes
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_NotificationTemplates_NotificationTypes')
BEGIN
    ALTER TABLE NotificationTemplates
    ADD CONSTRAINT FK_NotificationTemplates_NotificationTypes
    FOREIGN KEY (TypeID) REFERENCES NotificationTypes(ID);
    PRINT 'Added FK: NotificationTemplates -> NotificationTypes';
END

-- Notifications foreign key to NotificationTypes
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Notifications_NotificationTypes')
BEGIN
    ALTER TABLE Notifications
    ADD CONSTRAINT FK_Notifications_NotificationTypes
    FOREIGN KEY (TypeID) REFERENCES NotificationTypes(ID);
    PRINT 'Added FK: Notifications -> NotificationTypes';
END

-- Notifications foreign key to NotificationTemplates
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Notifications_NotificationTemplates')
BEGIN
    ALTER TABLE Notifications
    ADD CONSTRAINT FK_Notifications_NotificationTemplates
    FOREIGN KEY (TemplateID) REFERENCES NotificationTemplates(ID);
    PRINT 'Added FK: Notifications -> NotificationTemplates';
END

-- Notifications foreign key to users (SenderUserID)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Notifications_Users_Sender')
    BEGIN
        ALTER TABLE Notifications
        ADD CONSTRAINT FK_Notifications_Users_Sender
        FOREIGN KEY (SenderUserID) REFERENCES users(id);
        PRINT 'Added FK: Notifications -> users (SenderUserID)';
    END
END
ELSE
BEGIN
    PRINT 'WARNING: users table not found. Skipping SenderUserID foreign key.';
END

-- Notifications foreign key to EmployeeDetails (SenderEmployeeID)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'EmployeeDetails')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Notifications_EmployeeDetails_Sender')
    BEGIN
        ALTER TABLE Notifications
        ADD CONSTRAINT FK_Notifications_EmployeeDetails_Sender
        FOREIGN KEY (SenderEmployeeID) REFERENCES EmployeeDetails(ID);
        PRINT 'Added FK: Notifications -> EmployeeDetails (SenderEmployeeID)';
    END
END
ELSE
BEGIN
    PRINT 'WARNING: EmployeeDetails table not found. Skipping SenderEmployeeID foreign key.';
END

-- NotificationRecipients foreign key to Notifications
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_NotificationRecipients_Notifications')
BEGIN
    ALTER TABLE NotificationRecipients
    ADD CONSTRAINT FK_NotificationRecipients_Notifications
    FOREIGN KEY (NotificationID) REFERENCES Notifications(ID) ON DELETE CASCADE;
    PRINT 'Added FK: NotificationRecipients -> Notifications';
END

-- NotificationRecipients foreign key to users (RecipientUserID)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_NotificationRecipients_Users')
    BEGIN
        ALTER TABLE NotificationRecipients
        ADD CONSTRAINT FK_NotificationRecipients_Users
        FOREIGN KEY (RecipientUserID) REFERENCES users(id);
        PRINT 'Added FK: NotificationRecipients -> users (RecipientUserID)';
    END
END

-- NotificationRecipients foreign key to User_Role (RecipientRoleID)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'User_Role')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_NotificationRecipients_UserRole')
    BEGIN
        ALTER TABLE NotificationRecipients
        ADD CONSTRAINT FK_NotificationRecipients_UserRole
        FOREIGN KEY (RecipientRoleID) REFERENCES User_Role(RoleID);
        PRINT 'Added FK: NotificationRecipients -> User_Role (RecipientRoleID)';
    END
END
ELSE
BEGIN
    PRINT 'WARNING: User_Role table not found. Skipping RecipientRoleID foreign key.';
END

-- NotificationRecipients foreign key to EmployeeDetails (RecipientEmployeeID)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'EmployeeDetails')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_NotificationRecipients_EmployeeDetails')
    BEGIN
        ALTER TABLE NotificationRecipients
        ADD CONSTRAINT FK_NotificationRecipients_EmployeeDetails
        FOREIGN KEY (RecipientEmployeeID) REFERENCES EmployeeDetails(ID);
        PRINT 'Added FK: NotificationRecipients -> EmployeeDetails (RecipientEmployeeID)';
    END
END

-- NotificationSettings foreign key to users (UserID)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_NotificationSettings_Users')
    BEGIN
        ALTER TABLE NotificationSettings
        ADD CONSTRAINT FK_NotificationSettings_Users
        FOREIGN KEY (UserID) REFERENCES users(id);
        PRINT 'Added FK: NotificationSettings -> users (UserID)';
    END
END

-- NotificationSettings foreign key to NotificationTypes
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_NotificationSettings_NotificationTypes')
BEGIN
    ALTER TABLE NotificationSettings
    ADD CONSTRAINT FK_NotificationSettings_NotificationTypes
    FOREIGN KEY (TypeID) REFERENCES NotificationTypes(ID);
    PRINT 'Added FK: NotificationSettings -> NotificationTypes';
END

-- NotificationSettings unique constraint
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_NotificationSettings_UserID_TypeID')
BEGIN
    ALTER TABLE NotificationSettings
    ADD CONSTRAINT UQ_NotificationSettings_UserID_TypeID UNIQUE(UserID, TypeID);
    PRINT 'Added unique constraint: NotificationSettings (UserID, TypeID)';
END

-- Step 2: Create indexes for better performance
PRINT 'Creating indexes for better performance...';

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Notifications_TypeID')
BEGIN
    CREATE INDEX IX_Notifications_TypeID ON Notifications(TypeID);
    PRINT 'Created index: IX_Notifications_TypeID';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Notifications_CreatedAt')
BEGIN
    CREATE INDEX IX_Notifications_CreatedAt ON Notifications(CreatedAt DESC);
    PRINT 'Created index: IX_Notifications_CreatedAt';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Notifications_ReferenceID')
BEGIN
    CREATE INDEX IX_Notifications_ReferenceID ON Notifications(ReferenceID);
    PRINT 'Created index: IX_Notifications_ReferenceID';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_NotificationRecipients_NotificationID')
BEGIN
    CREATE INDEX IX_NotificationRecipients_NotificationID ON NotificationRecipients(NotificationID);
    PRINT 'Created index: IX_NotificationRecipients_NotificationID';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_NotificationRecipients_RecipientUserID')
BEGIN
    CREATE INDEX IX_NotificationRecipients_RecipientUserID ON NotificationRecipients(RecipientUserID);
    PRINT 'Created index: IX_NotificationRecipients_RecipientUserID';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_NotificationRecipients_RecipientRoleID')
BEGIN
    CREATE INDEX IX_NotificationRecipients_RecipientRoleID ON NotificationRecipients(RecipientRoleID);
    PRINT 'Created index: IX_NotificationRecipients_RecipientRoleID';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_NotificationRecipients_IsRead')
BEGIN
    CREATE INDEX IX_NotificationRecipients_IsRead ON NotificationRecipients(IsRead);
    PRINT 'Created index: IX_NotificationRecipients_IsRead';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_NotificationSettings_UserID')
BEGIN
    CREATE INDEX IX_NotificationSettings_UserID ON NotificationSettings(UserID);
    PRINT 'Created index: IX_NotificationSettings_UserID';
END

PRINT 'All foreign key constraints and indexes have been created successfully!';
PRINT 'Notification system database setup is now complete.';
