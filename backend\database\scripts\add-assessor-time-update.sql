-- Add assessor time update support
-- This script adds the missing assessorTimeUpdateReason column and notification template

PRINT 'Adding assessor time update support...';

-- Step 1: Add assessorTimeUpdateReason column to ClientPurpose table
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'ClientPurpose')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ClientPurpose') AND name = 'assessorTimeUpdateReason')
    BEGIN
        ALTER TABLE ClientPurpose ADD assessorTimeUpdateReason NVARCHAR(500);
        PRINT 'Added assessorTimeUpdateReason column to ClientPurpose table.';
    END
    ELSE
    BEGIN
        PRINT 'assessorTimeUpdateReason column already exists in ClientPurpose table.';
    END
END
ELSE
BEGIN
    PRINT 'WARNING: ClientPurpose table not found. assessorTimeUpdateReason column not added.';
END

-- Step 2: Add notification template for assessor time updates
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'NotificationTemplates')
BEGIN
    IF NOT EXISTS (SELECT * FROM NotificationTemplates WHERE TemplateName = 'ASSESSOR_TIME_UPDATE')
    BEGIN
        INSERT INTO NotificationTemplates (TypeID, TemplateName, TitleTemplate, MessageTemplate, ActionButtonText, ActionUrl, Priority) VALUES
        (
            (SELECT ID FROM NotificationTypes WHERE TypeName = 'TIME_UPDATE'),
            'ASSESSOR_TIME_UPDATE',
            'Assessment Time Updated - {quotationNo}',
            '{employeeName} has updated the assessment time for quotation {quotationNo} (Client: {clientName}). New time: {newTime}. Reason: {reason}',
            'View Details',
            '/forms/business-leads/{quotationNo}',
            2
        );
        PRINT 'Added ASSESSOR_TIME_UPDATE notification template.';
    END
    ELSE
    BEGIN
        PRINT 'ASSESSOR_TIME_UPDATE notification template already exists.';
    END
END
ELSE
BEGIN
    PRINT 'WARNING: NotificationTemplates table not found. Notification template not added.';
END

PRINT 'Assessor time update support setup complete!';
