const { sql, getPool } = require('../db');
const { employeeColumns } = require('../routes/utils/constant');

function removeFirstLeadingZero(str) {
    if (str.startsWith('0') && str.length > 1) {
        return str.slice(1);
    }
    return str;
}

const employeeController = {
    createEmployee: async (req, res) => {
        try {
            const pool = await getPool();
            const request = new sql.Request(pool);

            const userQuery = `
                INSERT INTO users (id, pwd, user_name, RoleID, Emp_ID, EmailAddress, CreationDate)
                VALUES (@username, @password, @title, @roleId, @employeeId, @email, GETDATE());
            `;
            const employeeQuery = `
                INSERT INTO EmployeeDetails (ID, CardId, SrNo, Title, Male, Female, Mobile, LicenseNo, IBANNumber)
                VALUES (@employeeId, @employeeId, @srNo, @title, @male, @female, @phoneNo, @licenseNo, @abn);
            `;

            request.input('username', sql.Var<PERSON>har(8), req.body.username);
            request.input('password', sql.Var<PERSON>har(100), req.body.password);
            request.input('title', sql.VarChar(50), req.body.firstName + ' ' + req.body.lastName);
            request.input('email', sql.VarChar(100), req.body.email);
            request.input('employeeId', sql.VarChar(4), req.body.employeeId);
            request.input('srNo', sql.VarChar(3), removeFirstLeadingZero(req.body.employeeId));
            request.input('male', sql.TinyInt, req.body.gender);
            request.input('female', sql.TinyInt, req.body.gender);
            request.input('abn', sql.VarChar(120), req.body.abn);
            request.input('licenseNo', sql.VarChar(40), req.body.licenseNo);
            request.input('phoneNo', sql.VarChar(120), req.body.phoneNo);
            request.input('roleId', sql.Int, req.body.roleId);

            await request.query(employeeQuery);
            await request.query(userQuery);

            res.status(201).json({
                message: 'Employee and user created successfully'
            });
        } catch (error) {
            res.status(500).json({
                message: 'Error creating employee and user',
                error: error.message
            });
        }
    },

    getNextEmployeeId: async (req, res) => {
        try {
            const query = `
                SELECT MAX(CAST(ID AS INT)) AS maxId
                FROM EmployeeDetails;
            `;

            const pool = await getPool();
            const request = new sql.Request(pool);
            const result = await request.query(query);

            const maxId = result.recordset[0].maxId || 0;
            const nextId = (maxId + 1).toString().padStart(4, '0');

            res.status(200).json({
                message: 'Next employee ID generated successfully',
                nextId: nextId
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    getEmployeeById: async (req, res) => {
        try {
            const { id } = req.params;

            const query = `
                SELECT 
                    E.*, 
                    Dsg.Title AS DesignationName,
                    Dept.Title AS DepartmentName,
                    RD.ID AS RestDayName,
                    RDO.ID RestDayOtherName,
                    Sh.Title AS ShiftName,
                    Coa32E.Title AS ExpenseAccountName,
                    Coa32C.Title AS ControlAccountName,
                    Coa32L.Title AS LiabilityAccountName,
                    Coa32O.Title AS OTExpenseAccountName,
                    Coa32OL.Title AS OTLiabilityAccountName,
                    Coa32T.Title AS TaxLiabilityAccountName,
                    CC.Title AS CostCenterName,
                    Proj.Title AS ProjectName,
                    Prp.Title AS PreparedByName,
                    Coa32PFE.Title AS PFEAccountName,
                    Coa32PFR.Title AS PFRAccountName,
                    Coa32EO.Title AS EOBIExpenseAccountName,
                    Coa32PESSI.Title AS PESSIExpenseAccountName,
                    LR.Title AS LateRuleName,
                    Class.Title AS ClassificationName,
                    Skill.Title AS SkillLevelName,
                    Lic.Title AS LicenseTypeName,
                    Maker.Title AS MakerName,
                    Intrv.Title AS InterviewerName,
                    AgeVer.ID AS AgeVerificationName,
                    Marital.ID AS MaritalStatusName,
                    EmpType.ID AS EmployeeTypeName,
                    IH.Title AS ImmediateHeadName,
                    U.Title AS UserName,
                    ReportTo.Title AS ReportingToName,
                    Coa32SS.Title AS SSExpenseAccountName,
                    Coa32PF.Title AS PFExpenseAccountName,
                    PFOB.Title AS OpeningBalanceAccountName,
                    PFSCD.Title AS SalaryContributionsDrAccountName,
                    PFSCC.Title AS SalaryContributionsCrAccountName,
                    PFIDA.Title AS InstallmentDeductionAccountName,
                    PFPA.Title AS PFundPayableAccountName,
                    PFPR.Title AS ProfitReceivableAccountName,
                    PFEP.Title AS ExpensesPayableAccountName,
                    PFTrust.Title AS PFTrustName,
                    HIC.Title AS HealthInsuranceCompanyName,
                    Coa32GE.Title AS GrantExpenseAccountName,
                    SubClass.Title AS SubClassificationName
                FROM EmployeeDetails E
                LEFT JOIN DefDesignations Dsg ON E.Desig_ID = Dsg.ID
                LEFT JOIN DefDepartments Dept ON E.Deptt_ID = Dept.ID
                LEFT JOIN PrintDays RD ON E.RestDay = RD.ID
                LEFT JOIN PrintDays RDO ON E.RestDayOther = RDO.ID
                LEFT JOIN DefShifts Sh ON E.Shift_ID = Sh.ID
                LEFT JOIN Coa32 Coa32E ON E.Exp_ID = Coa32E.id
                LEFT JOIN Coa32 Coa32C ON E.Control_ID = Coa32C.id
                LEFT JOIN Coa32 Coa32L ON E.Liability_ID = Coa32L.id
                LEFT JOIN Coa32 Coa32O ON E.OTExpense_ID = Coa32O.id
                LEFT JOIN Coa32 Coa32OL ON E.OTLiability_ID = Coa32OL.id
                LEFT JOIN Coa32 Coa32T ON E.TaxLiability_ID = Coa32T.id
                LEFT JOIN CostCenters CC ON E.CostCenter_ID = CC.ID
                LEFT JOIN DefProjects Proj ON E.Project_ID = Proj.ID
                LEFT JOIN users Prp ON E.prp_id = Prp.id
                LEFT JOIN Coa32 Coa32PFE ON E.PFE_ID = Coa32PFE.id
                LEFT JOIN Coa32 Coa32PFR ON E.PFR_ID = Coa32PFR.id
                LEFT JOIN Coa32 Coa32EO ON E.EOBIExp_ID = Coa32EO.id
                LEFT JOIN Coa32 Coa32PESSI ON E.PESSIExp_ID = Coa32PESSI.id
                LEFT JOIN SalaryDeductionRules LR ON E.LateRule_ID = LR.ID
                LEFT JOIN DefClassifications Class ON E.Classification = Class.ID
                LEFT JOIN DefSkillLevels Skill ON E.SkillLevel = Skill.ID
                LEFT JOIN DefLicenseTypes Lic ON E.LicenseType = Lic.ID
                LEFT JOIN DefMakers Maker ON E.Maker_ID = Maker.ID
                LEFT JOIN InterViewerRegister Intrv ON E.Interviewer_ID = Intrv.ID
                LEFT JOIN AgeVerifications AgeVer ON E.AgeVerification = AgeVer.ID
                LEFT JOIN MaritalStatuses Marital ON E.MaritalStatus = Marital.ID
                LEFT JOIN EmployeeTypes EmpType ON E.EmployeeType = EmpType.ID
                LEFT JOIN EmployeeDetails IH ON E.ImmediateHead_ID = IH.ID
                LEFT JOIN users U ON E.User_ID = U.id
                LEFT JOIN EmployeeDetails ReportTo ON E.ReportingTO_ID = ReportTo.ID
                LEFT JOIN Coa32 Coa32SS ON E.SSExp_ID = Coa32SS.id
                LEFT JOIN Coa32 Coa32PF ON E.PFExp_ID = Coa32PF.id
                LEFT JOIN PFundULCoa PFOB ON E.OpeningBalances_ID = PFOB.ID
                LEFT JOIN PFundULCoa PFSCD ON E.SalaryContributionsDr_ID = PFSCD.ID
                LEFT JOIN PFundULCoa PFSCC ON E.SalaryContributionsCr_ID = PFSCC.ID
                LEFT JOIN PFundULCoa PFIDA ON E.InstallmentDeductionAccount_ID = PFIDA.ID
                LEFT JOIN PFundULCoa PFPA ON E.PFundPayableAccount_ID = PFPA.ID
                LEFT JOIN PFundULCoa PFPR ON E.ProfitReceivable_ID = PFPR.ID
                LEFT JOIN PFundULCoa PFEP ON E.ExpensesPayable_ID = PFEP.ID
                LEFT JOIN EmpRegTypes PFTrust ON E.PFTrust_ID = PFTrust.ID
                LEFT JOIN Coa32 HIC ON E.HealthInsuranceCompany_ID = HIC.id
                LEFT JOIN Coa32 Coa32GE ON E.GrantExp_ID = Coa32GE.id
                LEFT JOIN DefSubClassifications SubClass ON E.SubClass_ID = SubClass.ID
                WHERE E.ID = @id;
            `;

            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('id', sql.VarChar(32), id);

            const result = await request.query(query);

            if (result.recordset.length === 0) {
                return res.status(404).json({ message: 'Employee not found' });
            }

            res.status(200).json({
                message: 'Employee retrieved successfully',
                employee: result.recordset[0]
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    deleteEmployee: async (req, res) => {
        try {
            const { id } = req.params;

            const query = `
                DELETE FROM EmployeeDetails
                WHERE ID = @id;
            `;

            const pool = await getPool();
            const request = new sql.Request(pool);
            request.input('id', sql.VarChar(32), id);

            const result = await request.query(query);

            if (result.rowsAffected[0] === 0) {
                return res.status(404).json({ message: 'Employee not found' });
            }

            res.status(200).json({
                message: 'Employee deleted successfully'
            });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    }
};

module.exports = employeeController;
