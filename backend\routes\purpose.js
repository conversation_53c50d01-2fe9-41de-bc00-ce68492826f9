const express = require('express');
const router = express.Router();
const { sql, getPool } = require('../db');
const authorization = require('../middleware/authorization');

router.use(authorization);

router.post('/save', async (req, res) => {
    try {
        const { assessor, clientID } = req.body;

        if (!assessor || !clientID) {
            return res.status(400).json({ message: 'Assessor and Client ID are required' });
        }

        const pool = await getPool();

        const insertRequest = new sql.Request(pool);
        let result = await insertRequest
            .input('assessorID', sql.Var<PERSON>har(32), assessor)
            .input('clientID', sql.VarChar(32), clientID)
            .query(`INSERT INTO ClientPurpose (assessorID, clientID)
                VALUES (@assessorID, @clientID)`);

        res.status(201).json({ message: 'Purpose saved successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error saving purpose', error });
    }
});

router.put('/update/:purposeID', async (req, res) => {
    try {
        const { purposeID } = req.params;
        const { RefVoucherNo, RefVTP, RefMnth, RefLocation, RefVNo } = req.body;

        if (!RefVoucherNo || !RefVTP || !RefMnth || !RefLocation || !RefVNo) {
            return res.status(400).json({ message: 'All fields are required' });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .input('purposeID', sql.VarChar(32), purposeID)
            .input('RefVoucherNo', sql.VarChar(70), RefVoucherNo)
            .input('RefVTP', sql.VarChar(20), RefVTP)
            .input('RefMnth', sql.VarChar(8), RefMnth)
            .input('RefLocation', sql.VarChar(8), RefLocation)
            .input('RefVNo', sql.Int, RefVNo)
            .query(`UPDATE ClientPurpose 
                    SET RefVoucherNo = @RefVoucherNo, RefVTP = @RefVTP, RefMnth = @RefMnth, RefLocation = @RefLocation, RefVNo = @RefVNo
                    WHERE ID = @purposeID`);

        res.status(200).json({ message: 'Purpose updated successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error updating purpose', error });
    }
});

router.put('/approval/:purposeID', async (req, res) => {
    try {
        const { purposeID } = req.params;
        const { isApproved, rejectionCause } = req.body;

        if (isApproved === undefined || isApproved === null || !(isApproved == 1 || isApproved == 0)) {
            return res.status(400).json({ message: 'isApproved is required' });
        }

        if (isApproved == 0 && !rejectionCause) {
            return res.status(400).json({ message: 'Rejection cause is required when rejecting' });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .input('purposeID', sql.VarChar(32), purposeID)
            .input('isApproved', sql.Bit, isApproved)
            .input('rejectionCause', sql.VarChar(500), rejectionCause || null)
            .query(`UPDATE ClientPurpose 
                    SET IsApproved = @isApproved,
                        RejectionCause = @rejectionCause
                    WHERE ID = @purposeID`);

        res.status(200).json({ message: 'Purpose updated successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error updating purpose', error });
    }
});

router.put('/mature/:purposeID', async (req, res) => {
    try {
        const { purposeID } = req.params;
        const { assessmentTime, contactPerson, remarks } = req.body;

        if (!assessmentTime || !contactPerson || !remarks) {
            return res.status(400).json({ message: 'All fields are required' });
        }

        const pool = await getPool();

        // First, get the assessor ID for this purpose
        const getAssessorRequest = new sql.Request(pool);
        const assessorResult = await getAssessorRequest
            .input('purposeID', sql.VarChar(32), purposeID)
            .query(`SELECT assessorID FROM ClientPurpose WHERE ID = @purposeID`);

        if (!assessorResult.recordset[0]) {
            return res.status(404).json({ message: 'Purpose not found' });
        }

        const assessorID = assessorResult.recordset[0].assessorID;

        // Parse the assessment time to create a time window (1 hour before and after)
        const assessmentDateTime = new Date(assessmentTime);
        const startTime = new Date(assessmentDateTime);
        const endTime = new Date(assessmentDateTime);

        // Set time window to check (1 hour before and after)
        startTime.setHours(startTime.getHours());
        endTime.setHours(endTime.getHours() + 1);

        // Check if the assessor is already booked during this time
        const checkRequest = new sql.Request(pool);
        const availabilityCheck = await checkRequest
            .input('assessorID', sql.VarChar(32), assessorID)
            .input('startTime', sql.DateTime, startTime)
            .input('endTime', sql.DateTime, endTime)
            .input('purposeID', sql.VarChar(32), purposeID)
            .query(`
                SELECT COUNT(*) AS conflictCount
                FROM ClientPurpose
                WHERE assessorID = @assessorID
                AND assessmentTime BETWEEN @startTime AND @endTime
                AND ID != @purposeID
            `);

        // If there are any existing appointments in the time window, the assessor is not available
        if (availabilityCheck.recordset[0].conflictCount > 0) {
            return res.status(409).json({
                message: 'Assessor not available at the selected time',
                details: 'The assessor already has an appointment scheduled within one hour of the requested time.'
            });
        }

        // If assessor is available, proceed with updating the purpose
        const updateRequest = new sql.Request(pool);
        let result = await updateRequest
            .input('purposeID', sql.VarChar(32), purposeID)
            .input('assessmentTime', sql.DateTime, assessmentTime)
            .input('contactPerson', sql.VarChar(32), contactPerson)
            .input('remarks', sql.VarChar(255), remarks)
            .query(`UPDATE ClientPurpose
                    SET assessmentTime = @assessmentTime,
                        contactPerson = @contactPerson,
                        remarks = @remarks
                    WHERE ID = @purposeID`);

        res.status(200).json({ message: 'Lead matured successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error maturing lead', error });
    }
});

router.put('/assign-delivery-installer/:purposeID', async(req, res) => {
    try {
        const { purposeID } = req.params;
        const { deliveryPersonId, deliveryPersonTime, installerId, installerTime } = req.body;

        if (!deliveryPersonId || !deliveryPersonTime || !installerId || !installerTime) {
            return res.status(400).json({ message: 'All fields are required' });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .input('purposeID', sql.VarChar(32), purposeID)
            .input('deliveryPersonId', sql.VarChar(32), deliveryPersonId)
            .input('deliveryPersonTime', sql.DateTime, deliveryPersonTime)
            .input('installerId', sql.VarChar(32), installerId)
            .input('installerTime', sql.DateTime, installerTime)
            .query(`UPDATE ClientPurpose 
                   SET deliveryPersonId = @deliveryPersonId, 
                       deliveryPersonTime = @deliveryPersonTime,
                       installerId = @installerId,
                       installerTime = @installerTime
                   WHERE ID = @purposeID`);

        res.status(200).json({ message: 'Delivery person and installer assigned successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error assigning delivery person and installer', error });
    }
});

router.get('/client/:clientID', async (req, res) => {
    try {
        const { clientID } = req.params;

        if (!clientID) {
            return res.status(400).json({ message: 'Client ID is required' });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .input('clientID', sql.VarChar(32), clientID)
            .query(`SELECT 
                        P.*,
                        E.Title AS assessorTitle,
                        CASE
                            WHEN P.assessmentTime IS NULL THEN 'pending'
                            WHEN P.RefVoucherNo IS NULL THEN 'in-progress'
                            WHEN P.RefVoucherNo IS NOT NULL THEN 'completed'
                        END AS status
                    FROM 
                        ClientPurpose P
                    LEFT JOIN
                        EmployeeDetails E on E.ID = P.assessorID
                    WHERE 
                        P.clientID =  @clientID`);

        res.status(200).json({ message: 'Purposes retrieved successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving purposes', error });
    }
});

router.get('/assessor', async (req, res) => {
    try {
        const assessorID = req.user.Emp_ID;

        if (!assessorID) {
            return res.status(400).json({ message: 'Assessor ID is required' });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .input('assessorID', sql.VarChar(32), assessorID)
            .query(`SELECT 
                        P.*,
                        E.Title AS assessorTitle,
                        c.title AS clientTitle,
                        CASE
                            WHEN P.assessmentTime IS NULL THEN 'pending'
                            WHEN P.RefVoucherNo IS NULL THEN 'in-progress'
                            WHEN P.RefVoucherNo IS NOT NULL THEN 'completed'
                        END AS status
                    FROM 
                        ClientPurpose P
                    LEFT JOIN
                        EmployeeDetails E on E.ID = P.assessorID
                    LEFT JOIN 
                        Coa32 c ON P.clientID = c.id
                    WHERE 
                        P.assessorID =  @assessorID AND P.assessorID IS NOT NULL`);

        res.status(200).json({ message: 'Purposes retrieved successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving purposes', error });
    }
});

router.get('/assessor/all', async (req, res) => {
    try {
        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .query(`SELECT 
                        P.*,
                        E.Title AS assessorTitle,
                        CASE
                            WHEN P.assessmentTime IS NULL THEN 'pending'
                            WHEN P.RefVoucherNo IS NULL THEN 'in-progress'
                            WHEN P.RefVoucherNo IS NOT NULL THEN 'completed'
                        END AS status
                    FROM 
                        ClientPurpose P
                    LEFT JOIN
                        EmployeeDetails E on E.ID = P.assessorID`);

        res.status(200).json({ message: 'Purposes retrieved successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving purposes', error });
    }
});

router.get('/assessor/completed', async (req, res) => {
    try {
        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .query(`SELECT 
                        P.*,
                        E.Title AS assessorTitle,
                        CASE
                            WHEN P.IsApproved IS NULL THEN 'pending'
                            WHEN P.IsApproved = 0 THEN 'rejected'
                            WHEN P.IsApproved = 1 THEN 'approved'
                        END AS status
                    FROM 
                        ClientPurpose P
                    LEFT JOIN
                        EmployeeDetails E on E.ID = P.assessorID
                    Where P.RefVoucherNo IS NOT NULL`);

        res.status(200).json({ message: 'Purposes retrieved successfully', data: result.recordset });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving purposes', error });
    }
});

router.get('/:purposeID', async (req, res) => {
    try {
        const { purposeID } = req.params;
        if (!purposeID) {
            return res.status(400).json({ message: 'purposeID is required' });
        }

        const pool = await getPool();
        const request = new sql.Request(pool);

        let result = await request
            .input('purposeID', sql.VarChar(32), purposeID)
            .query(`SELECT 
                        P.*,
                        E.Title AS assessorTitle,
                        c.title AS clientTitle,
                        c1.add1 AS clientAddress, 
                        c1.email AS clientEmail, 
                        c1.Mobile AS clientMobileNo, 
                        c1.tel AS clientTelephone,
                        CASE
                            WHEN P.assessmentTime IS NULL THEN 'pending'
                            WHEN P.RefVoucherNo IS NULL THEN 'in-progress'
                            WHEN P.RefVoucherNo IS NOT NULL THEN 'completed'
                        END AS status
                    FROM 
                        ClientPurpose P
                    LEFT JOIN
                        EmployeeDetails E on E.ID = P.assessorID
                    LEFT JOIN 
                        Coa32 c ON P.clientID = c.id
                    LEFT JOIN 
                        Coa321 c1 ON P.clientID = c1.id
                    WHERE 
                        P.ID =  @purposeID`);

        res.status(200).json({ message: 'Purposes retrieved successfully', data: result.recordset[0] });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving purposes', error });
    }
});

module.exports = router;
