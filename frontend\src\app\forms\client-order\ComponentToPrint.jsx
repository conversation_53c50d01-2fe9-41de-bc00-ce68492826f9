import { Box, Table, Thead, Tbody, Tr, Th, Td, Text, Divider, Flex, Spacer } from "@chakra-ui/react";
import dayjs from "dayjs";

const ComponentToPrint = ({ innerRef, data }) => {
    return (
        <Box ref={innerRef} p={8} maxW="800px" mx="auto">
            <Text><strong>Client Name:</strong> {data?.client_id} / {data?.clientTitle}</Text>
            <Text><strong>Dated:</strong> {dayjs(data?.Dated).format('DD MMM, YYYY')}</Text>
            <Text><strong>Purchase Order No:</strong> {data?.Voucher_No}</Text>
            <Text><strong>Purchase Order Date:</strong> {dayjs(data?.po_date).format('DD MMM, YYYY')}</Text>
            <Text><strong>Tender No:</strong> {data?.TenderNo || "N/A"}</Text>
            <Text><strong>Contract No:</strong> {data?.ContractNo || "N/A"}</Text>


            <Table variant="simple" size="sm" marginTop={'40px !important'} marginBottom={'30px !important'}>
                <Thead>
                    <Tr>
                        <Th>ITEM ID</Th>
                        <Th>DESCRIPTION</Th>
                        <Th>Qty</Th>
                        <Th>Rate</Th>
                        <Th>Total Amount</Th>
                    </Tr>
                </Thead>
                <Tbody>
                    {data?.items?.map((item, index) => (
                        <Tr key={index}>
                            <Td>{item?.Item_ID}</Td>
                            <Td>{item?.itemTitle}</Td>
                            <Td>{item?.Qty}</Td>
                            <Td>{item?.Rate}</Td>
                            <Td>{item?.Total}</Td>
                        </Tr>
                    ))}
                </Tbody>
            </Table>

            <Box>
                <Text><strong>Discount:</strong> {data?.Discount}</Text>
                <Text><strong>Sales Tax:</strong> {data?.SalesTaxAmount}</Text>
                <Text><strong>Balance:</strong> {data?.Balance}</Text>
            </Box>


            <Flex marginTop={'100px !important'}>
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Prepared by</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Approved by</Text>
                </Flex>
                <Spacer />
                <Flex flexDirection={'column'} alignItems={'center'}>
                    <Text>_____________________</Text>
                    <Text>Received by</Text>
                </Flex>
            </Flex>
        </Box>
    );
};

export default ComponentToPrint;
