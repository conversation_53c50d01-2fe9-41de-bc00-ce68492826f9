// Functions Here-----------------------------------------------------------------------------

import axiosInstance from "../axios";

export const formatDate = (date, onlyDate = false) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    const hours = String(d.getHours()).padStart(2, "0");
    const minutes = String(d.getMinutes()).padStart(2, "0");
    const seconds = String(d.getSeconds()).padStart(2, "0");
    const milliseconds = String(d.getMilliseconds()).padStart(3, "0");

    if (onlyDate) {
        return `${year}-${month}-${day}`;
    } else {
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    }
};

export const validateObjectFields = (order, requiredFields) => {
    const missingFields = requiredFields.filter(field => !order[field]);

    if (missingFields.length > 0) {
        return {
            isValid: false,
            error: `The following required fields are missing or empty: ${missingFields.join(", ")}`
        };
    } else {
        return {
            isValid: true
        };
    }
};

export const validateArrayFields = (items, requiredFields) => {
    const errors = [];

    items.forEach((item, index) => {
        const missingFields = requiredFields.filter(field =>
            item[field] === null || item[field] === undefined || item[field] === "" || item[field] === 0
        );

        if (missingFields.length > 0) {
            errors.push(`Item number ${index + 1} is missing or has empty fields: ${missingFields.join(", ")}`);
        }
    });

    if (errors.length > 0) {
        return {
            isValid: false,
            error: errors.join("\n")
        };
    } else {
        return {
            isValid: true
        };
    }
};

export const capitalizeWords = (str) => {
    return str
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
}


const seededRandom = (seed) => {
    let x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
}

export const generateRandomColorRGBA = (seed, alpha = 1) => {
    const red = Math.floor(seededRandom(seed) * 256);
    const green = Math.floor(seededRandom(seed + 1) * 256);
    const blue = Math.floor(seededRandom(seed + 2) * 256);

    return `rgba(${red}, ${green}, ${blue}, ${alpha})`;
}

export const getData = async (url) => {
    try {
        const response = await axiosInstance.get(url);
        return response.data;
    } catch (error) {
        console.error(error);
        return null;
    }
};