"use client";
import Sidebar from "@src/components/sidebar/sidebar";
import React, { Suspense, useEffect, useState } from "react";
import ComboBox from "@components/Custom/ComboBox/ComboBox";
import TableComboBox from "@components/Custom/TableComboBox/TableComboBox";
import AanzaDataTable from "@components/Custom/AanzaDataTable/AanzaDataTable";
import axiosInstance from "../axios";
import Toolbar from "@components/Toolbar/Toolbar";
import { Button, Select, Textarea, useToast } from "@chakra-ui/react";
import { Box, FormControl, FormLabel, Input } from "@chakra-ui/react";
import {
  formatDate,
  getData,
  validateArrayFields,
  validateObjectFields,
} from "@utils/functions";
import { RecoveryFollowUpSectionFormFields } from "@utils/constant";
import { useSearchParams } from "next/navigation";
import Loader from "@components/Loader/Loader";
import { useUser } from "@src/app/provider/UserContext";
import dayjs from "dayjs";
import SignaturePad from "@components/Custom/SignaturePad/SignaturePad";
import MultipleImageUploader from "@components/Custom/MultipleImageUploader/MultipleImageUploader";
import MultipleAudioUploader from "@components/Custom/MultipleAudioUploader/MultipleAudioUploader";
import NumberInput  from "@components/Custom/NumberInput/NumberInput";
import AssessorPDFGenerator from "@components/AssessorPDFGenerator";

const QuotationFollowupRequiredFields = [
  // "Dated",
  // "VTP",
  // "Mnth",
  // "Location",
  // "vno",
  // "Currency_ID",
  // "ExchRate",
  // "Party_ref",
  // "AttentionPerson",
  // "AttentionPerson_Desig",
  // "Subject",
  // "GrdTotalPSTAmt",
  // "Freight",
  // "NetAmount",
  // "Terms",
  // "StartingComments",
  // "client_id",
];

const QuotationFollowupItemsRequiredFields = [
  // "Dated",
  // "VTP",
  // "Mnth",
  // "Location",
  // "vno",
  // "srno",
  // "item_id",
  // "Rate",
  // "Discount",
  // "Total",
  // "VisitDate",
  // "details",
  // "Qty",
];

const QuotationFollowupHeaders = [
  {
    label: "Item ID",
    key: "Item_ID",
    width: "200px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Item Description",
    key: "Item_Title",
    width: "350px",
    isReadOnly: false,
    type: "text",
  },
  {
    label: "Qty",
    key: "Qty",
    width: "100px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Rate",
    key: "Rate",
    width: "100px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Disc %",
    key: "Disc",
    width: "100px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Installation Charges",
    key: "InstallationCharges",
    width: "200px",
    isReadOnly: false,
    type: "number",
  },
  {
    label: "Total",
    key: "Total",
    width: "100px",
    isReadOnly: true,
    type: "number",
  },
  {
    label: "Delivery Date",
    key: "Date",
    width: "200px",
    isReadOnly: false,
    type: "date",
  },
  {
    label: "Details",
    key: "Details",
    width: "350px",
    isReadOnly: false,
    type: "text",
  },
];
const createQuotationFollowupEmptyTableRow = () => [
  {
    Item_ID: "",
    Item_Title: "",
    Qty: 0,
    Rate: 0,
    InstallationCharges: 0,
    Disc: 0,
    Total: 0,
    Date: "",
    Details: "",
  },
];

const createQuotationFollowupInitialFormData = () => ({
  location: "",
  vno: "",
  vtp: "",
  voucherNo: "",
  date: "",
  mnth: "",
  tenderNo: "",
  exchangeRate: 0,
  partyRef: "",
  attentionPerson: "",
  designation: "",
  contactPerson: "",
  email: "",
  phoneNumber: "",
  landline: "",
  address: "",
  lead: "",
  clientId: "",
  clientTitle: "",
  currency: "",
  subject: "Quotation for Requested Items",
  quotation: "Please find below the quotation for the items you inquired about. The prices mentioned are inclusive of all applicable charges. Let us know if you need any adjustments or have further queries.",
  totalAmount: 0,
  freight: 0,
  netAmount: 0,
  validityDays: 0,
  paymentTerms: "",
  narration: "",
  salesTaxR: "",
  salesTaxA: "",
  discountPercent: "",
  discountAmount: 0,
  netPayableAmt: 0,
  sTaxAmount: 0,
  assignerId: "",
  assignerTitle: "",
  assessmentTime: "",
  assignerLocation: "",
});

const QuotationFollowupCalculationFields = [
  "salesTaxR",
  "salesTaxA",
  "netAmount",
  "discountPercent",
  "discountAmount",
  "netPayableAmt",
  "sTaxAmount",
  "totalAmount",
  "freight",
];

const AssesserFollowUp = () => {
  const toast = useToast();
  const { user } = useUser();
  const searchParams = useSearchParams();
  const [isDisabled, setIsDisabled] = useState(false);
  const [items, setItems] = useState([]);
  const [tableData, setTableData] = useState(
    createQuotationFollowupEmptyTableRow()
  );
  const [formData, setFormData] = useState(
    createQuotationFollowupInitialFormData()
  );
  const [loading, setLoading] = useState(false);
  const [printData, setPrintData] = useState({});
  const [signature, setSignature] = useState(null);
  const [images, setImages] = useState([]);
  const [audios, setAudios] = useState([]);
  const [loadedImages, setLoadedImages] = useState([]);
  const [loadedAudios, setLoadedAudios] = useState([]);
  const [isPDFGeneratorOpen, setPDFGeneratorOpen] = useState(false);

  const handlePrint = async () => {
    if (!signature) {
      toast({
        title: "Please sign the document first.",
        status: "warning",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      return false;
    }
    const data = {
      ...formData,
      items: tableData,
      signature
    };
    setPrintData(data);
    setPDFGeneratorOpen(true);
  };

  const handleSendPdf = async (pdfBlob) => {
    try {
      if (
        formData.email &&
        formData.subject &&
        formData.clientTitle &&
        formData.quotation
      ) {
        const newFormData = new FormData();
        newFormData.append("pdf", pdfBlob);
        newFormData.append("to", formData.email);
        newFormData.append("subject", formData.subject);
        newFormData.append("clientName", formData.clientTitle);
        newFormData.append("quotation", formData.quotation);
        await axiosInstance.post(
          "email/quotation-email-with-file",
          newFormData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
      }
    } catch (error) {
      console.error("Error sending PDF:", error);
      toast({
        title: "Error sending PDF.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const handleInputChange = (singleInput, bulkInput) => {
    if (singleInput) {
      let { name, value } = singleInput.target || singleInput;

      if (QuotationFollowupCalculationFields.includes(name)) {
        setFormData((prev) => {
          const salesTaxR =
            name === "salesTaxR" ? Number(value) : Number(prev.salesTaxR);
          const salesTaxA =
            name === "salesTaxA" ? Number(value) : Number(prev.salesTaxA);
          const freight =
            name === "freight" ? Number(value) : Number(prev.freight);
          const discountPercent =
            name === "discountPercent" ? value : prev.discountPercent;
          let discountAmount =
            name === "discountAmount" ? value : prev.discountAmount;
          const totalAmount = prev.totalAmount;
          let sTaxAmount = prev.sTaxAmount;
          let netAmount = prev.netAmount;

          if (salesTaxR + salesTaxA > 100) {
            sTaxAmount = 0;
          } else {
            const totalPercentage = (salesTaxR + salesTaxA) / 100;
            sTaxAmount = totalAmount * totalPercentage;
          }
          if (name !== "netAmount") {
            netAmount = totalAmount + sTaxAmount;
          }

          discountAmount = (discountPercent / 100) * netAmount;
          const netPayableAmt = netAmount + freight - discountAmount;

          return {
            ...prev,
            [name]: value,
            salesTaxR,
            salesTaxA,
            sTaxAmount,
            discountAmount,
            totalAmount,
            netAmount,
            netPayableAmt,
          };
        });
      } else {
        setFormData((prev) => ({ ...prev, [name]: value }));
      }
    } else if (bulkInput) {
      setFormData((prev) => ({ ...prev, ...bulkInput }));
    }
  };

  const transformData = (
    orderData = {},
    itemsArray,
    isNavigationdata = false
  ) => {
    if (isNavigationdata) {
      return itemsArray.map((item) => {
        return {
          Item_ID: item?.item_id,
          Item_Title: item?.itemTitle,
          Rate: Number(item?.Rate),
          InstallationCharges: Number(item?.InstallationCharges),
          Disc: Number(item?.Discount),
          Total: Number(item?.Total),
          Date: formatDate(new Date(item?.VisitDate), true),
          Details: item?.details,
          Qty: Number(item?.Qty),
        };
      });
    } else {
      return itemsArray.map((item, index) => {
        return {
          Dated: orderData.Dated,
          VTP: orderData.VTP,
          Mnth: orderData.Mnth,
          Location: orderData.Location,
          vno: orderData.vno,
          srno: index + 1,
          item_id: item.Item_ID,
          Rate: Number(item.Rate),
          InstallationCharges: Number(item.InstallationCharges),
          Discount: Number(item.Disc),
          Total: Number(item.Total),
          VisitDate: item.Date,
          details: item.Details,
          Qty: Number(item.Qty),
        };
      });
    }
  };

  const rowClickHandler = (data, rowIndex, colIndex) => {
    const isExist = tableData.find((modal) => modal.Item_ID === data.Item_ID);
    if (isExist) {
      setTableData((prev) => {
        const updatedTableData = prev.map((item) => {
          if (item.Item_ID === isExist.Item_ID) {
            return {
              ...item,
              qty: item.qty ? Number(item.qty) + 1 : 1,
            };
          }
          return item;
        });
        return updatedTableData;
      });
    } else {
      setTableData((prev) => {
        const updatedTableData = [...prev];
        updatedTableData[rowIndex] = {
          ...updatedTableData[rowIndex],
          Item_ID: data.Item_ID ? data.Item_ID : "",
          Item_Title: data.Item_Title ? data.Item_Title : "",
          Rate: data.Item_Sale_Rate ? data.Item_Sale_Rate : 0,
          Details: data.Item_Details ? data.Item_Details : "",
        };
        return updatedTableData;
      });
    }
  };

  const handleImagesChange = (newImages) => {
    setImages(newImages);
  };

  const handleAudiosChange = (newAudios) => {
    setAudios(newAudios);
  };

  const cellRender = (
    value,
    key,
    rowIndex,
    colIndex,
    cellData,
    handleInputChange
  ) => {
    if (["Item_ID", "Item_Title", ].includes(key)) {
      return (
        <TableComboBox
          rowIndex={rowIndex}
          colIndex={colIndex}
          inputWidth={cellData.width}
          value={value}
          onChange={(val) => handleInputChange(val)}
          modalData={items}
          modalHeaders={["ID", "Title", "Details", "Sale_Rate"]}
          isDisabled={isDisabled}
          rowClickHandler={rowClickHandler}
        />
      );
    }
    if (cellData.type === "number") {
      return (
        <NumberInput
          width={cellData.width}
          value={value}
          onChange={(e) => handleInputChange(e.target.value)}
          size="sm"
          isReadOnly={cellData.isReadOnly}
          isDisabled={isDisabled}
        />
      );
    }
    
    return (
      <Input
        width={cellData.width}
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        size="sm"
        type={cellData.type}
        isReadOnly={cellData.isReadOnly}
        isDisabled={isDisabled}
      />
    );
  };

  const calculation = (header, value, rowIndex) => {
    setTableData((prevData) => {
      return prevData.map((r, i) => {
        if (i === rowIndex) {
          const updatedRow = { ...r, [header.key]: value };
          const qty = header.key === "Qty" ? value : r.Qty;
          const rate = header.key === "Rate" ? value : r.Rate;
          const installationCharges =
            header.key === "InstallationCharges"
              ? value
              : r.InstallationCharges;
          const discountPercent = header.key === "Disc" ? value : r.Disc;
          const total = (Number(qty) || 0) * (Number(rate) || 0);
          const discountAmount = (discountPercent / 100) * total;
          updatedRow.Total =
            total - discountAmount + (Number(installationCharges) || 0);
          return updatedRow;
        }
        return r;
      });
    });
  };

  useEffect(() => {
    let totalQty = 0;
    let totalAmount = 0;

    tableData.forEach((data) => {
      totalAmount += Number(data.Total) || 0;
    });

    const salesTaxR = formData.salesTaxR;
    const salesTaxA = formData.salesTaxA;
    const freight = formData.freight;
    const discountPercent = formData.discountPercent;
    let discountAmount = formData.discountAmount;
    let sTaxAmount = formData.sTaxAmount;
    let netAmount = formData.netAmount;

    if (salesTaxR + salesTaxA > 100) {
      sTaxAmount = 0;
    } else {
      const totalPercentage = (salesTaxR + salesTaxA) / 100;
      sTaxAmount = totalAmount * totalPercentage;
    }


    discountAmount = (discountPercent / 100) * netAmount;
    const netPayableAmt = netAmount + freight - discountAmount;

    setFormData((prev) => ({
      ...prev,
      totalQty,
      totalAmount,
      netAmount,
      netPayableAmt,
      sTaxAmount,
      discountAmount,
    }));
  }, [tableData]);

  const getVoucherNo = async (date) => {
    if (date) {
      setLoading(true);
      const year = new Date(date).getFullYear();
      try {
        const response = await axiosInstance.post("offer/getVoucherNo", {
          Mnth: year.toString(),
        });
        const { location, vno, vtp, Mnth, voucherNo } = response.data || {};
        if ((location, vno, vtp, Mnth, voucherNo)) {
          setFormData((prevFormData) => ({
            ...prevFormData,
            location,
            vno,
            vtp,
            Mnth,
            voucherNo,
            mnth: Mnth,
          }));
          setLoading(false);
        }
      } catch (error) {
        console.error("Error fetching voucher number:", error);
        setLoading(false);
      }
    } else {
      toast({
        title: "Please Select a Date First.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const navigateVoucherForm = async (voucherNo) => {
    setLoading(true);
    setIsDisabled(true);
    try {
      const response = await axiosInstance.post("offer/navigate", {
        goto: true,
        voucher_no: voucherNo,
      });
      const fileResponse = await axiosInstance.get("offer/get-files", {
        params: {
          refVoucherNo: voucherNo,
        }
      });

      // Process files by type
      const allFiles = fileResponse.data;
      const images = allFiles.filter(file => file.Type === 'image' && file.FileName !== 'signature.png');
      const audios = allFiles.filter(file => file.Type === 'audio');
      
      // Find signature if it exists
      const signatureFile = allFiles.find(file => file.FileName === 'signature.png');
      if (signatureFile) {
        // Convert buffer to base64 string for signature pad
        const buffer = Buffer.from(signatureFile.FileData.data);
        const base64String = `data:image/png;base64,${buffer.toString('base64')}`;
        setSignature(base64String);
      }
      setLoadedAudios(audios);
      setLoadedImages(images);
      const resData = response.data;
      const { items } = response.data;
      const form = {
        date: resData?.Dated
          ? formatDate(new Date(resData?.Dated), true)
          : null,
        vtp: resData?.VTP,
        mnth: resData?.Mnth,
        location: resData?.Location,
        vno: resData?.vno,
        voucherNo: resData?.Voucher_No,
        clientId: resData?.client_id ?? "",
        clientTitle: resData?.ClientName ?? "",
        assignerId: resData?.EmployeeID ?? "",
        assignerTitle: resData?.EmployeeName ?? "",
        assessmentTime: resData?.AssessmentTime
          ? dayjs(resData?.AssessmentTime).format("YYYY-MM-DDTHH:mm")
          : "",
        assignerLocation: resData?.AssessmentLocation ?? "",
        salesTaxA: resData?.SalesTaxA ?? 0,
        salesTaxR: resData?.SalesTaxR ?? 0,
        discountAmount: resData?.Discount ?? 0,
        discountPercent: resData?.DiscountPercent ?? 0,
        netPayableAmt: resData?.GrossAmount ?? 0,
        lead: resData?.Lead ?? "",
        tenderNo: resData?.TenderNo ?? "",
        // exchangeRate: resData?.ExchRate ?? 0,
        // partyRef: resData?.Party_ref ?? "",
        attentionPerson: resData?.AttentionPerson ?? "",
        designation: resData?.AttentionPerson_Desig ?? "",
        contactPerson: resData?.ContactPerson ?? "",
        currency: resData?.Currency_ID ?? "",
        subject: resData?.Subject ?? "",
        quotation: resData?.Quotation ?? "",
        totalAmount: resData?.GrdTotalPSTAmt ?? 0,
        freight: resData?.Freight ?? 0,
        validityDays: resData?.Validity ?? 0,
        paymentTerms: resData?.Terms ?? "",
        netAmount: resData?.NetAmount ?? 0,
        narration: resData?.StartingComments ?? "",
      };
      setTableData(() => transformData([], items, true));
      setFormData(form);
      setLoading(false);
    } catch (error) {
      console.error(`Error fetching goto voucher:`, error);
      setLoading(false);
      toast({
        title: `goto Voucher Fetching Failed !`,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const loadInitialData = async () => {
    const purpose_no = searchParams.get("purpose_no");
    const { data: purposeData } = await getData("purpose/" + purpose_no);
    if (purposeData.RefVoucherNo) {
      navigateVoucherForm(purposeData.RefVoucherNo);
    } else {
      const newDate = formatDate(dayjs(), true);
      setFormData((prev) => ({
        ...prev,
        date: newDate,
      }));
      getVoucherNo(newDate);
    }
    const { data: clientData } = await getData("client/" + purposeData?.clientID);
    const items = await getData("getRecords/items");
    setFormData((prev) => ({
      ...prev,
      assignerId: purposeData?.assessorID,
      assessmentTime: purposeData?.assessmentTime,
      assignerLocation: purposeData?.clientAddress,
      address: purposeData?.clientAddress,
      email: purposeData?.clientEmail,
      phoneNo: purposeData?.clientMobileNo,
      landlineNo: purposeData?.clientTelephone,
      clientId: purposeData?.clientID,
      clientTitle: purposeData?.clientTitle,
      contact: purposeData?.contactPerson,
      comments: purposeData?.remarks,
      ...clientData
    }));

    const itemData = [];
    items.map((item) => {
      itemData.push({
        Item_ID: item.id,
        Item_Title: item.Title,
        // Item_Unit: item.Unit,
        Item_Details: item.Details,
        Item_Sale_Rate: item.Sale_Rate,
      });
    });
    setItems(itemData);
  };

  // Toolbar funtions starts here

  const handleSave = async () => {
    if (!signature) {
      toast({
        title: "Please sign the document first.",
        status: "warning",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      return false;
    }
    const purpose_no = searchParams.get("purpose_no");
    const data = {
      Dated: formData.date ? formatDate(formData.date) : null,
      VTP: formData.vtp,
      Mnth: formData.mnth,
      Location: formData.location,
      vno: formData.vno,
      Voucher_No: formData.voucherNo,
      Currency_ID: formData.currency,
      Validity: formData.validityDays,
      Lead: formData.lead,
      EmployeeID: formData.assignerId,
      AssessmentTime: formData.assessmentTime
        ? formatDate(formData.assessmentTime)
        : null,
      AssessmentLocation: formData.assignerLocation,
      TenderNo: formData.tenderNo,
      SalesTaxA: formData.salesTaxA,
      SalesTaxR: formData.salesTaxR,
      DiscountPercent: formData.discountPercent,
      Discount: formData.discountAmount,
      GrossAmount: formData.netPayableAmt,
      ContactPerson: formData.contactPerson,
      Subject: formData.subject,
      Quotation: formData.quotation,
      GrdTotalPSTAmt: formData.totalAmount,
      prp_id: user.id,
      Freight: formData.freight,
      NetAmount: formData.netAmount,
      Terms: formData.paymentTerms,
      StartingComments: formData.narration,
      client_id: formData.clientId,
      CreationDate: formatDate(new Date()),
      items: transformData(
        {
          Dated: formatDate(formData.date),
          VTP: formData.vtp,
          Mnth: formData.mnth,
          Location: formData.location,
          vno: formData.vno,
        },
        tableData
      ),
    };
    const isValidateObjectFields = validateObjectFields(
      data,
      QuotationFollowupRequiredFields
    );
    const isValidateArrayFields = validateArrayFields(
      data.items,
      QuotationFollowupItemsRequiredFields
    );
    if (isValidateObjectFields.error) {
      toast({
        title: isValidateObjectFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateArrayFields.error) {
      toast({
        title: isValidateArrayFields.error,
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
    if (isValidateObjectFields.isValid && isValidateArrayFields.isValid) {
      setLoading(true);
      try {
        await axiosInstance({
          method: "post",
          url: "offer/create",
          data: data,
        });
        await axiosInstance({
          method: "put",
          url: "purpose/update/" + purpose_no,
          data: {
            RefVoucherNo: formData.voucherNo,
            RefVTP: formData.vtp,
            RefMnth: formData.mnth,
            RefLocation: formData.location,
            RefVNo: formData.vno,
          },
        });
        handleStoreImages();
        handleStoreAudios();
        handleStoreSignature();
        setIsDisabled(true);
        toast({
          title: "Quotation Followup Form saved successfully :)",
          status: "success",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        setLoading(false);
      } catch (error) {
        console.error("Error saving Quotation Followup Form:", error);
        toast({
          title: "Error saving Quotation Followup Form :(",
          status: "error",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        setLoading(false);
      }
    } else {
      toast({
        title:
          "Please fill out all required fields and ensure all items have valid fields",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    }
  };

  const handleStoreImages = async () => {
    if (!images || images.length === 0) {
      toast({
        title: "No files to upload.",
        status: "warning",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      return;
    }

    try {
      const refVoucherNo = formData.voucherNo;
      const refVTP = formData.vtp;
      const refMnth = formData.mnth;
      const refLocation = formData.location;
      const refVNo = formData.vno;

      if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {
        toast({
          title: "Voucher details are missing. Please generate a voucher first.",
          status: "error",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        return;
      }

      setLoading(true);

      const uploadPromises = images.map(async (image) => {
        const formData = new FormData();
        formData.append("file", image.file); // Attach the file
        formData.append("timestamp", image.timestamp);
        formData.append("location", JSON.stringify(image.location));
        formData.append("googleMapsLink", image.googleMapsLink);
        formData.append("type", "image"); // Adjust type if needed (e.g., 'video', 'audio')
        formData.append("refVoucherNo", refVoucherNo);
        formData.append("refVTP", refVTP);
        formData.append("refMnth", refMnth);
        formData.append("refLocation", refLocation);
        formData.append("refVNo", refVNo);

        return axiosInstance.post("offer/store-file", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      });

      const results = await Promise.allSettled(uploadPromises);

      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          console.log(`Image ${index + 1} uploaded successfully`);
        } else {
          console.error(`Image ${index + 1} upload failed:`, result.reason);
        }
      });
    } catch (error) {
      console.error("Error uploading files:", error);
      toast({
        title: "Error uploading files.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStoreAudios = async () => {
    if (!audios || audios.length === 0) {
      toast({
        title: "No audio files to upload.",
        status: "warning",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      return;
    }

    try {
      const refVoucherNo = formData.voucherNo;
      const refVTP = formData.vtp;
      const refMnth = formData.mnth;
      const refLocation = formData.location;
      const refVNo = formData.vno;

      if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {
        toast({
          title: "Voucher details are missing. Please generate a voucher first.",
          status: "error",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        return;
      }

      setLoading(true);

      const uploadPromises = audios.map(async (audio) => {
        const formData = new FormData();
        formData.append("file", audio.file); // Attach the file
        formData.append("timestamp", audio.timestamp);
        formData.append("location", JSON.stringify(audio.location));
        formData.append("googleMapsLink", audio.googleMapsLink);
        formData.append("type", "audio"); // Specify type as 'audio'
        formData.append("refVoucherNo", refVoucherNo);
        formData.append("refVTP", refVTP);
        formData.append("refMnth", refMnth);
        formData.append("refLocation", refLocation);
        formData.append("refVNo", refVNo);

        return axiosInstance.post("offer/store-file", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      });

      const results = await Promise.allSettled(uploadPromises);

      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          console.log(`Audio ${index + 1} uploaded successfully`);
        } else {
          console.error(`Audio ${index + 1} upload failed:`, result.reason);
        }
      });
    } catch (error) {
      console.error("Error uploading audio files:", error);
      toast({
        title: "Error uploading audio files.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStoreSignature = async () => {
    if (!signature) {
      toast({
        title: "No signature to upload",
        status: "info",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
      return;
    }

    try {
      const refVoucherNo = formData.voucherNo;
      const refVTP = formData.vtp;
      const refMnth = formData.mnth;
      const refLocation = formData.location;
      const refVNo = formData.vno;

      if (!refVoucherNo || !refVTP || !refMnth || !refLocation || !refVNo) {
        toast({
          title: "Voucher details are missing. Please generate a voucher first.",
          status: "error",
          variant: "left-accent",
          position: "top-right",
          isClosable: true,
        });
        return;
      }

      setLoading(true);

      // Convert data URL to a Blob
      const fetchResponse = await fetch(signature);
      const signatureBlob = await fetchResponse.blob();
      
      // Create a File object
      const signatureFile = new File([signatureBlob], "signature.png", { type: "image/png" });
      
      const signatureFormData = new FormData();
      signatureFormData.append("file", signatureFile);
      signatureFormData.append("timestamp", new Date().toISOString());
      signatureFormData.append("location", JSON.stringify({}));
      signatureFormData.append("googleMapsLink", "");
      signatureFormData.append("refVoucherNo", refVoucherNo);
      signatureFormData.append("refVTP", refVTP);
      signatureFormData.append("refMnth", refMnth);
      signatureFormData.append("refLocation", refLocation);
      signatureFormData.append("refVNo", refVNo);
      
      await axiosInstance.post("offer/store-signature", signatureFormData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    } catch (error) {
      console.error("Error uploading signature:", error);
      toast({
        title: "Error uploading signature.",
        status: "error",
        variant: "left-accent",
        position: "top-right",
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Toolbar funtions ends here

  useEffect(() => {
    loadInitialData();
  }, []);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="wrapper">
            <div className="">
              <div className="">
                <div className="page-inner">
                  <div className="row">
                    <div className="bgWhite">
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "20px",
                          fontWeight: "bold",
                          padding: "10px",
                        }}
                      >
                        Site Assessment Follow-Up
                      </h1>
                    </div>
                  </div>
                  <div
                    className="row"
                    style={{ gap: "10px", paddingTop: "8px" }}
                  >
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <h1
                        style={{
                          margin: "0",
                          textAlign: "center",
                          color: "#2B6CB0",
                          fontSize: "20px",
                          fontWeight: "bold",
                        }}
                      >
                        SOP&apos;s
                      </h1>
                      <div className="question">
                        <p>1. Is the property more than 2 years old ?</p>
                        <Select name={"question1"} placeholder="Select">
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                        </Select>
                      </div>
                      <div className="question">
                        <p>
                          2. Is the combined yearly income of the household
                          equal to or less than 180,000 ?
                        </p>
                        <Select name={"question1"} placeholder="Select">
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                        </Select>
                      </div>
                      <div className="question">
                        <p>
                          3. Are you ready to decommission the ducted gas heated
                          system ?
                        </p>
                        <Select name={"question1"} placeholder="Select">
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                        </Select>
                      </div>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="bgWhite col-md-5 col-sm-12"
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexWrap: {
                            base: "wrap",
                            sm: "wrap",
                            md: "wrap",
                            lg: "nowrap",
                          },
                          gap: "10px",
                        }}
                      >
                        <Box
                          sx={{
                            width: {
                              base: "100%",
                              sm: "100%",
                              md: "100%",
                              lg: "80%",
                            },
                          }}
                        >
                          <Box
                            className=""
                            sx={{
                              display: "grid",
                              gridTemplateColumns: {
                                base: "repeat(auto-fit,minmax(200px,1fr)) !important",
                                sm: "repeat(auto-fit,minmax(200px,1fr)) !important",
                                md: "repeat(auto-fit,minmax(200px,1fr)) !important",
                                lg: "repeat(auto-fit,minmax(500px,1fr))",
                              },
                              gap: "5px",
                            }}
                          >
                            {RecoveryFollowUpSectionFormFields[0].map(
                              (field) => (
                                <FormControl
                                  key={field.id}
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    flexWrap: {
                                      base: "wrap",
                                      sm: "wrap",
                                      md: "wrap",
                                      lg: "nowrap",
                                    },
                                    flexDirection: {
                                      base: "column",
                                      sm: "column",
                                      md: "row",
                                    },
                                    marginTop: "10px",
                                  }}
                                  isRequired={field.isRequired}
                                >
                                  <FormLabel
                                    htmlFor={field.id}
                                    sx={{
                                      marginBottom: "0",
                                      width: {
                                        base: "100%",
                                        sm: "100%",
                                        md: "100%",
                                        lg: "27%",
                                      },
                                    }}
                                  >
                                    {field.label}
                                  </FormLabel>
                                  {field.type === "date" ? (
                                    <Input
                                      id={field.id}
                                      name={field.name}
                                      type={field.type}
                                      value={formData[field.value]}
                                      onChange={handleInputChange}
                                      placeholder={field.placeholder}
                                      _placeholder={{ color: "gray.500" }}
                                      readOnly={field.isReadOnly}
                                      // min={field.minDate}
                                      // max={field.maxDate}
                                      disabled={isDisabled}
                                      sx={{
                                        marginLeft: {
                                          base: "0",
                                          sm: "0",
                                          lg: "4px",
                                        },
                                        width: {
                                          base: "100%",
                                          sm: "100%",
                                          md: "100%",
                                          lg: "80%",
                                        },
                                      }}
                                    />
                                  ) : (
                                    <Input
                                      id={field.id}
                                      name={field.name}
                                      type={field.type}
                                      value={formData[field.value]}
                                      onChange={handleInputChange}
                                      placeholder={field.placeholder}
                                      _placeholder={{ color: "gray.500" }}
                                      readOnly={field.isReadOnly}
                                      disabled={
                                        field.name === "voucherNo"
                                          ? isDisabled
                                          : isDisabled
                                      }
                                      sx={{
                                        marginLeft: {
                                          base: "0",
                                          sm: "0",
                                          lg: "4px",
                                        },
                                        width: {
                                          base: "100%",
                                          sm: "100%",
                                          md: "100%",
                                          lg: "80%",
                                        },
                                      }}
                                    />
                                  )}
                                </FormControl>
                              )
                            )}
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            width: {
                              base: "100%",
                              sm: "100%",
                              md: "100%",
                              lg: "20%",
                            },
                          }}
                        >
                          <FormControl sx={{ height: "100%" }}>
                            <Button
                              sx={{
                                width: "100%",
                                height: {
                                  base: "inherit",
                                  md: "inherit",
                                  lg: "calc(100% - 10px)",
                                },
                                margin: "10px 0",
                              }}
                              colorScheme="blue"
                              onClick={() => getVoucherNo(formData.date)}
                              isDisabled={isDisabled}
                            >
                              Generate Voucher No
                            </Button>
                          </FormControl>
                        </Box>
                      </Box>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <Box
                        className="pt-4 pb-4"
                        sx={{
                          display: "grid",
                          gridTemplateColumns: {
                            base: "repeat(auto-fit,minmax(200,1fr))",
                            sm: "repeat(auto-fit,minmax(200,1fr))",
                            md: "repeat(auto-fit,minmax(200,1fr))",
                            lg: "repeat(auto-fit,minmax(500px,1fr))",
                          },
                          gap: "5px",
                        }}
                      >
                        {RecoveryFollowUpSectionFormFields[1].map(
                          (control, index) => (
                            <FormControl
                              key={index}
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                flexDirection: {
                                  base: "column",
                                  sm: "column",
                                  lg: "row",
                                },
                                marginTop: "10px",
                                flexWrap: "nowrap",
                              }}
                              isRequired={control.isRequired}
                            >
                              <FormLabel
                                htmlFor={control.fields[0].name}
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "20%",
                                  },
                                }}
                              >
                                {control.label}
                              </FormLabel>
                              <Box
                                sx={{
                                  width: {
                                    base: "100%",
                                    sm: "100%",
                                    lg: "80%",
                                  },
                                  display: "flex",
                                  gap: control.fields.length > 1 ? "10px" : "0",
                                }}
                              >
                                {control.fields.map((field, fieldIndex) =>
                                  field.component === "ComboBox" ? (
                                    <ComboBox
                                      key={fieldIndex}
                                      target={true}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      inputWidths={field.inputWidths}
                                      buttonWidth={field.buttonWidth}
                                      styleButton={{
                                        padding: "3px !important",
                                      }}
                                      tableData={[]}
                                      tableHeaders={field.tableHeaders}
                                      nameFields={field.nameFields}
                                      placeholders={field.placeholders}
                                      keys={field.keys}
                                      form={formData}
                                      isDisabled={true}
                                    />
                                  ) : (
                                    <Input
                                      key={fieldIndex}
                                      onChange={handleInputChange}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      value={formData[field.value]}
                                      _placeholder={field._placeholder}
                                      type={field.type}
                                      style={{ width: field.inputWidth }}
                                      disabled={true}
                                    />
                                  )
                                )}
                              </Box>
                            </FormControl>
                          )
                        )}
                      </Box>
                    </Box>

                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="ClientDIVVV bgWhite"
                    >
                      <AanzaDataTable
                        tableData={tableData}
                        setTableData={setTableData}
                        headers={QuotationFollowupHeaders}
                        tableWidth="100%"
                        tableHeight="400px"
                        fontSize="lg"
                        cellRender={cellRender}
                        styleHead={{
                          background: "#3275bb",
                          color: "white !important",
                        }}
                        styleBody={{ background: "white !important" }}
                        calculation={calculation}
                        isDisabled={isDisabled}
                      />
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(50% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                          height: "100%",
                        }}
                        isRequired="true"
                      >
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "100%" },
                            display: "flex",
                            height: "100%",
                          }}
                        >
                          <MultipleImageUploader
                            initial={loadedImages}
                            onChange={handleImagesChange}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(50% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite col-md-7 col-sm-12"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                            lg: "row",
                          },
                          marginTop: "10px",
                          flexWrap: "nowrap",
                          height: "100%",
                        }}
                        isRequired="true"
                      >
                        <Box
                          sx={{
                            width: { base: "100%", sm: "100%", lg: "100%" },
                            display: "flex",
                            height: "100%",
                          }}
                        >
                          <MultipleAudioUploader
                            initial={loadedAudios}
                            onChange={handleAudiosChange}
                            disabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                        },
                      }}
                      className="ClientDIVVV bgWhite"
                    >
                      {/* <div className="row">
                      <div className="bgWhite mt-2"> */}
                      <div
                        className="pt-4 pb-2"
                        style={{
                          display: "grid",
                          gridTemplateColumns:
                            "repeat(auto-fit,minmax(300px,1fr))",
                          gap: "5px",
                        }}
                      >
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Total Amount
                            </FormLabel>
                            <NumberInput
                              onChange={handleInputChange}
                              name={"totalAmount"}
                              value={formData.totalAmount}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              S.Tax%
                            </FormLabel>
                            <Box
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                                display: "flex",
                                gap: 1.5,
                              }}
                            >
                              <NumberInput
                                onChange={handleInputChange}
                                name={"salesTaxR"}
                                value={formData.salesTaxR}
                                placeholder="R"
                                _placeholder={{ color: "gray.500" }}
                                sx={{
                                  width: {
                                    base: "30%",
                                    sm: "30%",
                                    lg: "30%",
                                  },
                                }}
                                isReadOnly={false}
                                disabled={isDisabled}
                              />
                              <NumberInput
                                onChange={handleInputChange}
                                name={"salesTaxA"}
                                value={formData.salesTaxA}
                                placeholder="A"
                                _placeholder={{ color: "gray.500" }}
                                sx={{
                                  width: {
                                    base: "70%",
                                    sm: "70%",
                                    lg: "70%",
                                  },
                                }}
                                isReadOnly={false}
                                disabled={isDisabled}
                              />
                            </Box>
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              S.Tax Amt.
                            </FormLabel>
                            <NumberInput
                              onChange={handleInputChange}
                              name={"sTaxAmount"}
                              value={formData.sTaxAmount}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Net Amount
                            </FormLabel>
                            <NumberInput
                              onChange={handleInputChange}
                              name={"netAmount"}
                              value={formData.netAmount}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Discount
                            </FormLabel>
                            <Box
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                                display: "flex",
                                gap: 1.5,
                              }}
                            >
                              <NumberInput
                                onChange={handleInputChange}
                                name={"discountPercent"}
                                value={formData.discountPercent}
                                placeholder="%"
                                _placeholder={{ color: "gray.500" }}
                                sx={{
                                  width: {
                                    base: "30%",
                                    sm: "30%",
                                    lg: "30%",
                                  },
                                }}
                                isReadOnly={false}
                                disabled={isDisabled}
                              />
                              <NumberInput
                                onChange={handleInputChange}
                                name={"discountAmount"}
                                value={formData.discountAmount}
                                placeholder="A"
                                _placeholder={{ color: "gray.500" }}
                                sx={{
                                  width: {
                                    base: "70%",
                                    sm: "70%",
                                    lg: "70%",
                                  },
                                }}
                                isReadOnly={true}
                                disabled={isDisabled}
                              />
                            </Box>
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Freight
                            </FormLabel>
                            <NumberInput
                              onChange={handleInputChange}
                              name={"freight"}
                              value={formData.freight}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                              }}
                              isReadOnly={false}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Net Payable Amt
                            </FormLabel>
                            <NumberInput
                              onChange={handleInputChange}
                              name={"netPayableAmt"}
                              value={formData.netPayableAmt}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                              }}
                              isReadOnly={true}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl style={{ display: "flex", gap: "4px" }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "30%",
                                },
                              }}
                            >
                              Validity Days
                            </FormLabel>
                            <NumberInput
                              onChange={handleInputChange}
                              name={"validityDays"}
                              value={formData.validityDays}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "70%",
                                },
                              }}
                              isReadOnly={false}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                        <FormControl
                          gridColumn="1 / -1"
                          style={{ display: "flex", gap: "4px" }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "flex-start",
                              width: "100%",
                              flexDirection: {
                                base: "column",
                                sm: "column",
                                lg: "row",
                              },
                            }}
                          >
                            <FormLabel
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "10%",
                                },
                              }}
                            >
                              Payment Terms
                            </FormLabel>
                            <Input
                              onChange={handleInputChange}
                              name={"paymentTerms"}
                              value={formData.paymentTerms}
                              placeholder=""
                              _placeholder={{ color: "gray.500" }}
                              type="text"
                              sx={{
                                width: {
                                  base: "100%",
                                  sm: "100%",
                                  lg: "90%",
                                },
                              }}
                              isReadOnly={false}
                              disabled={isDisabled}
                            />
                          </Box>
                        </FormControl>
                      </div>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(75% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          marginTop: "10px",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                          },
                        }}
                      >
                        <FormLabel
                          sx={{
                            width: "100%",
                          }}
                        >
                          Remarks
                        </FormLabel>
                        <Textarea
                          _placeholder={{ color: "gray.500" }}
                          resize="vertical"
                          sx={{
                            width: "100%",
                            height: "100%",
                          }}
                          onChange={handleInputChange}
                          name={"narration"}
                          value={formData.narration}
                          disabled={isDisabled}
                          rows={10}
                        />
                      </FormControl>
                    </Box>
                    <Box
                      sx={{
                        padding: "15px",
                        width: {
                          base: "100% !important",
                          sm: "100%",
                          lg: "calc(25% - 5px) !important",
                        },
                      }}
                      className="ClientDIVVV bgWhite"
                    >
                      <FormControl
                        sx={{
                          display: "flex",
                          marginTop: "10px",
                          flexDirection: {
                            base: "column",
                            sm: "column",
                          },
                        }}
                      >
                        <FormLabel
                          sx={{
                            width: "100%",
                          }}
                        >
                          Digital Signature
                        </FormLabel>
                        <Box
                          sx={{
                            width: "100%",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                          }}
                        >
                          <SignaturePad 
                            onSave={(url) => setSignature(url)} 
                            onClose={() => setSignature(null)} 
                            initialSignature={signature}
                            isDisabled={isDisabled}
                          />
                        </Box>
                      </FormControl>
                    </Box>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {!isDisabled && (
            <Toolbar
              save={handlePrint}
              hide={[
                "First",
                "Edit",
                "Last",
                "Previous",
                "Next",
                "Delete",
                "Goto",
                "Cancel",
                "Clear",
                "Check",
                "Print",
              ]}
            />
          )}
          <AssessorPDFGenerator
            isOpen={isPDFGeneratorOpen}
            onClose={() => setPDFGeneratorOpen(false)}
            data={printData}
            onPDFGenerated={(pdfFile) => {
              setPDFGeneratorOpen(false);
              handleSendPdf(pdfFile);
              handleSave();
            }}
          />
        </>
      )}
    </>
  );
};

const AssesserFollowUpPage = () => (
  <Suspense fallback={<Loader />}>
    <AssesserFollowUp />
  </Suspense>
);

export default AssesserFollowUpPage;
