import React from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON>ontent,
    Modal<PERSON>eader,
    ModalCloseButton,
    ModalBody,
    ModalFooter,
    Text,
} from '@chakra-ui/react';

const ConfirmDialog = ({
    isOpen,
    onClose,
    onConfirm,
    title = "Confirm Action",
    message = "Are you sure you want to proceed?",
    confirmText = "Yes",
    cancelText = "No",
    confirmColorScheme = "red",
}) => {
    return (
        <Modal isOpen={isOpen} onClose={onClose}>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>{title}</ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                    {typeof message === "string" ? <Text>{message}</Text> : message}
                </ModalBody>

                <ModalFooter>
                    <Button variant="ghost" onClick={onClose}>
                        {cancelText}
                    </Button>
                    <Button
                        colorScheme={confirmColorScheme}
                        onClick={() => {
                            onConfirm();
                            onClose();
                        }}
                        ml={3}
                    >
                        {confirmText}
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
};

export default ConfirmDialog;
