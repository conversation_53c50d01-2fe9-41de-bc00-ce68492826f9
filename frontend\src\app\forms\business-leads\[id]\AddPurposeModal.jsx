import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  VStack,
  Select,
  useToast,
} from "@chakra-ui/react";
import axiosInstance from "@src/app/axios";
import React, { useState } from 'react';

const AddPurposeModal = ({ isOpen, onClose, onSave, assessors = [], clientID }) => {
  const toast = useToast();
  const [formData, setFormData] = useState({
    assessor: ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({ ...prevState, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const { assessor } = formData;

    if (!assessor) {
      toast({
        title: "Missing information",
        description: "Please select an assessor.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    const submissionData = {
      assessor,
      clientID,
      isPending: true // Flag to indicate this is a pending lead
    };

    try {
      await axiosInstance.post('/purpose/save', submissionData);
      onSave();
      toast({
        title: "Lead added.",
        description: "The lead has been added successfully and is now in pending status.",
        status: "success",
        duration: 5000,
        isClosable: true,
      });
      handleClose();
    } catch (error) {
      toast({
        title: "An error occurred.",
        description: "Unable to add lead.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };



  const handleClose = () => {
    setFormData({
      assessor: ''
    });
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader style={{ background: "#071533", color: "white" }}>Add Lead</ModalHeader>
        <ModalCloseButton style={{ color: "white" }} />
        <form onSubmit={handleSubmit}>
          <ModalBody width={"100%"}>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>Assessor</FormLabel>
                <Select
                  name="assessor"
                  placeholder="Please Select"
                  value={formData.assessor}
                  onChange={handleChange}
                >
                  {assessors && assessors.map(option =>
                    <option key={option.ID} value={option.ID}>{option.Title}</option>
                  )}
                </Select>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} type="submit">Save</Button>
            <Button onClick={handleClose} colorScheme="red">Cancel</Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default AddPurposeModal;
