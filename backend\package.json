{"name": "ImpexGrace_BackEnd", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "engines": {"node": "20.x"}, "dependencies": {"body-parser": "^1.20.3", "compression": "^1.7.5", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "express": "^4.20.0", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "helmet": "^8.0.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tedious": "^18.6.1"}, "devDependencies": {"nodemon": "^3.1.5"}}