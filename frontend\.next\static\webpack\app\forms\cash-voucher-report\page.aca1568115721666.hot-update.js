"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forms/cash-voucher-report/page",{

/***/ "(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx":
/*!****************************************************!*\
  !*** ./src/app/forms/cash-voucher-report/page.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_app_dashboard_dashboard_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @src/app/dashboard/dashboard.css */ \"(app-pages-browser)/./src/app/dashboard/dashboard.css\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/spinner/spinner.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table-container.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/table.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/thead.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tr.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/th.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/tbody.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/table/td.mjs\");\n/* harmony import */ var _src_app_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @src/app/axios */ \"(app-pages-browser)/./src/app/axios.js\");\n/* harmony import */ var _src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @src/components/Custom/ServerPaginatedTable */ \"(app-pages-browser)/./src/components/Custom/ServerPaginatedTable/index.jsx\");\n/* harmony import */ var _src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @src/components/Loader/Loader */ \"(app-pages-browser)/./src/components/Loader/Loader.jsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CashVoucherReport = ()=>{\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        totalCount: 0,\n        totalPages: 0\n    });\n    const [selectedVoucher, setSelectedVoucher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voucherDetails, setVoucherDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentHistory, setPaymentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalLoading, setModalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const fetchData = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/report?page=\".concat(page, \"&pageSize=\").concat(pageSize));\n            console.log(response.data);\n            if (response.data && response.data.data) {\n                setData(response.data.data);\n                setPagination({\n                    page: response.data.page,\n                    pageSize: response.data.pageSize,\n                    totalCount: response.data.totalCount,\n                    totalPages: response.data.totalPages\n                });\n            } else {\n                setData([]);\n                setPagination({\n                    page: 1,\n                    pageSize: 20,\n                    totalCount: 0,\n                    totalPages: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch cash voucher report data\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchData();\n    }, []);\n    const fetchVoucherDetails = async (voucherNo)=>{\n        try {\n            setModalLoading(true);\n            const response = await _src_app_axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"cashVoucher/voucher-details/\".concat(voucherNo));\n            if (response.data) {\n                setVoucherDetails(response.data.voucher_details);\n                setPaymentHistory(response.data.payment_history || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching voucher details: \", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch voucher details\",\n                status: \"error\",\n                duration: 3000,\n                isClosable: true\n            });\n        } finally{\n            setModalLoading(false);\n        }\n    };\n    const handleOpenModal = async (voucher)=>{\n        setSelectedVoucher(voucher);\n        setIsModalOpen(true);\n        await fetchVoucherDetails(voucher.voucher_no);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedVoucher(null);\n        setVoucherDetails(null);\n        setPaymentHistory([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"green\";\n            case \"pending\":\n                return \"red\";\n            case \"partial\":\n                return \"yellow\";\n            default:\n                return \"gray\";\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2\n        }).format(amount || 0);\n    };\n    const columns = [\n        {\n            header: \"Voucher No\",\n            field: \"voucher_no\"\n        },\n        {\n            header: \"Quotation No (adj)\",\n            field: \"quotation_no\"\n        },\n        {\n            header: \"Client ID\",\n            field: \"client_id\"\n        },\n        {\n            header: \"Client Name\",\n            field: \"client_name\"\n        },\n        {\n            header: \"Gross Amount\",\n            field: \"gross_amount\",\n            render: (item)=>formatCurrency(item.gross_amount)\n        },\n        {\n            header: \"Paid Amount\",\n            field: \"paid_amount\",\n            render: (item)=>formatCurrency(item.paid_amount)\n        },\n        {\n            header: \"Remaining Amount\",\n            field: \"remaining_amount\",\n            render: (item)=>formatCurrency(item.remaining_amount)\n        },\n        {\n            header: \"Status\",\n            field: \"status\",\n            type: \"badge\"\n        },\n        {\n            header: \"Actions\",\n            field: \"actions\",\n            render: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                    colorScheme: \"blue\",\n                    size: \"sm\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleOpenModal(item);\n                    },\n                    children: \"View Details\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 168,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    const getRowCursor = ()=>\"pointer\";\n    const handleRowClick = (item)=>{\n        handleOpenModal(item);\n    };\n    const handlePageChange = (newPage)=>{\n        fetchData(newPage, pagination.pageSize);\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        fetchData(1, newPageSize);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Loader_Loader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n            lineNumber: 199,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-inner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bgWhite\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    margin: \"0\",\n                                                    textAlign: \"center\",\n                                                    color: \"#2B6CB0\",\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    padding: \"10px\"\n                                                },\n                                                children: \"Cash Voucher Report\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"row\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Custom_ServerPaginatedTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            data: data,\n                                            columns: columns,\n                                            pagination: pagination,\n                                            onPageChange: handlePageChange,\n                                            onPageSizeChange: handlePageSizeChange,\n                                            onRowClick: handleRowClick,\n                                            getRowCursor: getRowCursor,\n                                            getBadgeColor: getStatusColor,\n                                            loading: loading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                lineNumber: 205,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 204,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                        lineNumber: 203,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 202,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Modal, {\n                    isOpen: isModalOpen,\n                    onClose: handleCloseModal,\n                    size: \"6xl\",\n                    scrollBehavior: \"inside\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalOverlay, {}, void 0, false, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalContent, {\n                            maxH: \"90vh\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalHeader, {\n                                    bg: \"blue.600\",\n                                    color: \"white\",\n                                    borderTopRadius: \"md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                        fontSize: \"xl\",\n                                        fontWeight: \"bold\",\n                                        children: [\n                                            \"Voucher Details - \",\n                                            selectedVoucher === null || selectedVoucher === void 0 ? void 0 : selectedVoucher.voucher_no\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.ModalCloseButton, {\n                                    color: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.ModalBody, {\n                                    p: 6,\n                                    children: modalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        alignItems: \"center\",\n                                        minH: \"200px\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Spinner, {\n                                            size: \"xl\",\n                                            color: \"blue.500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 37\n                                    }, undefined) : voucherDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                        spacing: 6,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                p: 4,\n                                                bg: \"gray.50\",\n                                                borderRadius: \"md\",\n                                                border: \"1px solid\",\n                                                borderColor: \"gray.200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: \"Voucher Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Voucher No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.voucher_no\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_id\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Client Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.client_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Badge, {\n                                                                        colorScheme: getStatusColor(voucherDetails.status),\n                                                                        fontSize: \"sm\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        children: voucherDetails.status.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Divider, {\n                                                        my: 4\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.HStack, {\n                                                        spacing: 8,\n                                                        wrap: \"wrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Gross Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"blue.600\",\n                                                                        children: formatCurrency(voucherDetails.gross_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Paid Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"green.600\",\n                                                                        children: formatCurrency(voucherDetails.paid_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Remaining Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"bold\",\n                                                                        fontSize: \"lg\",\n                                                                        color: \"red.600\",\n                                                                        children: formatCurrency(voucherDetails.remaining_amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.VStack, {\n                                                                align: \"start\",\n                                                                spacing: 1,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"gray.600\",\n                                                                        children: \"Created Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                        fontWeight: \"semibold\",\n                                                                        children: voucherDetails.created_at ? dayjs__WEBPACK_IMPORTED_MODULE_6___default()(voucherDetails.created_at).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                        fontSize: \"lg\",\n                                                        fontWeight: \"bold\",\n                                                        mb: 3,\n                                                        color: \"blue.600\",\n                                                        children: [\n                                                            \"Payment History (\",\n                                                            paymentHistory.length,\n                                                            \" payments)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    paymentHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.TableContainer, {\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        borderRadius: \"md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Table, {\n                                                            variant: \"simple\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.Thead, {\n                                                                    bg: \"gray.100\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.Tr, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Payment Voucher No\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Payment Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                isNumeric: true,\n                                                                                children: \"Amount\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 65\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Th, {\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 65\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 57\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.Tbody, {\n                                                                    children: paymentHistory.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.Tr, {\n                                                                            _hover: {\n                                                                                bg: \"gray.50\"\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    fontWeight: \"semibold\",\n                                                                                    children: payment.payment_voucher_no\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 361,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Badge, {\n                                                                                        colorScheme: payment.payment_type === \"CR\" ? \"green\" : \"blue\",\n                                                                                        variant: \"subtle\",\n                                                                                        children: payment.payment_type === \"CR\" ? \"Cash Receipt\" : payment.payment_type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                        lineNumber: 365,\n                                                                                        columnNumber: 73\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 364,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    isNumeric: true,\n                                                                                    fontWeight: \"semibold\",\n                                                                                    color: \"green.600\",\n                                                                                    children: formatCurrency(payment.payment_amount)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 372,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    children: payment.payment_date ? dayjs__WEBPACK_IMPORTED_MODULE_6___default()(payment.payment_date).format(\"DD MMM, YYYY hh:mm A\") : \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 375,\n                                                                                    columnNumber: 69\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.Td, {\n                                                                                    children: payment.payment_description || \"N/A\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                                    lineNumber: 381,\n                                                                                    columnNumber: 69\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 65\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 49\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                        p: 8,\n                                                        textAlign: \"center\",\n                                                        bg: \"gray.50\",\n                                                        borderRadius: \"md\",\n                                                        border: \"1px solid\",\n                                                        borderColor: \"gray.200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                color: \"gray.500\",\n                                                                fontSize: \"lg\",\n                                                                children: \"No payments found for this voucher\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                color: \"gray.400\",\n                                                                fontSize: \"sm\",\n                                                                mt: 1,\n                                                                children: \"This voucher is still pending payment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 37\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                        textAlign: \"center\",\n                                        py: 8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            color: \"gray.500\",\n                                            children: \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                            lineNumber: 248,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personel\\\\NexSol Tech\\\\ERP\\\\ImpexGrace\\\\frontend\\\\src\\\\app\\\\forms\\\\cash-voucher-report\\\\page.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n};\n_s(CashVoucherReport, \"to0F4F3T6vvp+5aeff+YHPpyvK4=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = CashVoucherReport;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CashVoucherReport);\nvar _c;\n$RefreshReg$(_c, \"CashVoucherReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/forms/cash-voucher-report/page.jsx\n"));

/***/ })

});