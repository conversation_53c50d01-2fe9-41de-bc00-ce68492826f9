import {
  Avatar,
  Box,
  Flex,
  HStack,
  IconButton,
  Menu,
  <PERSON>u<PERSON>utton,
  MenuDivider,
  MenuItem,
  MenuList,
  Text,
  VStack,
} from "@chakra-ui/react";
import Image from "next/image";
import Link from "next/link";
import React, { useMemo } from "react";
import { FiChevronDown, FiLogOut, FiMenu, FiUser } from "react-icons/fi";
import logoMobile from "@assets/assets/imgs/logo.png";
import { useUser } from "@src/app/provider/UserContext";
import { capitalizeWords } from "@src/app/utils/functions";
import { useRouter } from 'next/navigation';
import NotificationBell from '@src/components/Notifications/NotificationBell';

const MobileNav = ({ onOpen, ...rest }) => {
  const { logout, user, userRole } = useUser();
  const router = useRouter();
  const isAdmin = userRole === "Admin";
  const removeMargin = { marginLeft: "0 !important" };
  const combinedStyle = useMemo(() => {
    if (isAdmin) return { ...rest };
    return { ...rest, ...removeMargin };
  }, [isAdmin]);
  const dummyImage =
    "https://images.unsplash.com/photo-1619946794135-5bc917a27793?ixlib=rb-0.3.5&q=80&fm=jpg&crop=faces&fit=crop&h=200&w=200&s=b616b2c5b373a80ffc9636ba24f7a4a9";
  return (
    <Flex
      ml={{ base: 0, md: 60 }}
      px={{ base: 4, md: 4 }}
      height="20"
      alignItems="center"
      bg="white"
      borderBottomWidth="1px"
      borderBottomColor="gray.200"
      justifyContent={{ base: "space-between", md: "flex-end" }}
      {...combinedStyle}
    >
      <IconButton
        display={{ base: "flex", md: "none" }}
        onClick={onOpen}
        variant="outline"
        aria-label="open menu"
        icon={<FiMenu />}
      />

      <Text
        display={{ base: "flex", md: "none" }}
        fontSize="2xl"
        fontFamily="monospace"
        fontWeight="bold"
        style={{ marginBottom: "0" }}
      >
        <Link href="/dashboard">
          <Image src={logoMobile} alt="ECO Asset Manager" height={50} />
        </Link>
      </Text>

      <HStack spacing={{ base: "0", md: "6" }}>
        {isAdmin && <NotificationBell />}

        <Flex alignItems={"center"}>
          <Menu>
            <MenuButton
              py={2}
              transition="all 0.3s"
              _focus={{ boxShadow: "none" }}
            >
              <HStack>
                <Avatar
                  size={"md"}
                  src={user?.imageUrl ? user.imageUrl : dummyImage}
                />
                <VStack
                  display={{ base: "none", md: "flex" }}
                  alignItems="flex-start"
                  style={{ gap: 0 }}
                  ml="2"
                >
                  <Text style={{ marginBottom: "0" }} fontSize="s">
                    {user?.userName
                      ? capitalizeWords(user.userName)
                      : "Anonymous"}
                  </Text>
                  <Text
                    style={{ marginBottom: "0", fontSize: "13px" }}
                    color="gray.600"
                  >
                    {user?.roleName ? capitalizeWords(user.roleName) : "Role"}
                  </Text>
                </VStack>
                <Box display={{ base: "none", md: "flex" }}>
                  <FiChevronDown />
                </Box>
              </HStack>
            </MenuButton>
            <MenuList bg="white" borderColor="gray.200" padding={0}>
              <MenuItem onClick={() => router.push("/profile")} padding={"10px 20px"} sx={{ color: "#000000", textDecoration: "none", display: "flex", alignItems: "center", gap: "10px" }}>
                <FiUser />
                <span>Profile</span>
              </MenuItem>
              <MenuDivider margin={'0 !important'} />
              <MenuItem onClick={() => logout()} padding={"10px 20px"} sx={{ color: "#000000", textDecoration: "none", display: "flex", alignItems: "center", gap: "10px" }}>
                <FiLogOut />
                <span>Sign out</span>
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </HStack>
    </Flex>
  );
};

export default MobileNav;
