/* width */
::-webkit-scrollbar {
    width: 7px;
}

::-webkit-scrollbar:horizontal {
    height: 10px !important;
}

/* Track */
::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #10679d;
    border-radius: 2px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #0b517c;
}

p {
    line-height: 1.3 !important;
    margin: 0 !important;
}

.bgWhite {
    box-shadow: 0px 0px 3px 1px rgba(193, 193, 193, 0.75);
    -webkit-box-shadow: 0px 0px 3px 1px rgba(193, 193, 193, 0.75);
    -moz-box-shadow: 0px 0px 3px 1px rgba(193, 193, 193, 0.75);
    border-radius: 4px;
    background-color: white;
}

.row {
    padding: 0 10px 0 10px;
}

.page-inner {
    padding-top: 15px;
}

@media screen and (min-width: 767.5px) {

    /* .ClientDIVVV {
        width: 56% !important;
        margin-left: 10px;
    } */

    .main-panel {
        width: calc(100% - 210px) !important
    }
}

label {
    margin-bottom: 0 !important;
}

input {
    padding-left: 5px !important;
    padding-right: 5px !important;
}

.TableContainer th {
    color: white !important;
}


.sidebarHR {
    margin: 5px 0 !important;
    margin-left: 10px !important;
    width: calc(100% - 20px) !important;
    border-bottom-color: rgb(131, 131, 131) !important;
}

.css-1lwmz7c {
    padding-left: 0 !important;
    overflow: hidden;
    background-color: #e2e8f0;
}

a:focus {
    color: black !important;
}

.css-1eyncsv {
    padding: 5px 5px !important;
}

.css-r10se1 {
    padding: 0px 6px !important;
}

.PageHeading {
    margin: 0;
    text-align: center;
    color: #1e7c45;
    font-size: 24px;
    font-weight: bold;
    padding: 10px
}

/* .chakra-button {
    background-color: #10275a !important;
}
.chakra-button:hover {
    background-color: #0b1b3c !important;
}
.chakra-button:focus {
    background-color: #08132c !important;
} */

.bgWhite h1 {
    color: #071533 !important;
}

.css-18gzrxy {
    background-color: #0a1c42 !important
}

.css-1lwmz7c {
    background: #040329 !important;
}

.print-container Table {
    border: 1px solid #000
}

.print-container Table Thead Tr {
    border: 1px solid #000;
    background: #76777a;
}

.print-container Table Thead Tr Th {
    border: 1px solid #000 !important;
    color: #fff !important;
    padding: 10px !important;
}

.print-container Table Tbody Tr,
.print-container Table Tbody Tr Td {
    padding: 10px !important;
    border: 1px solid #000 !important;
}

.toolbar button {
    background-color: transparent;
}
.toolbar button:hover {
    background-color: transparent;
}

.toolbarMenu {
    transition: 0.1s ease-in-out;
}
.toolbarMenu:hover {
    background-color: #071533;
}

.question {
    margin: 0 0 7px 0;
}