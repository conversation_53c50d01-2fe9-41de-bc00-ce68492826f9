"use client";
import { useState, useCallback, lazy, Suspense, memo } from "react";
import PropTypes from "prop-types";
import { Input, But<PERSON>, Box, Spinner } from "@chakra-ui/react";
import "./ComboBox.css";

const Modal = lazy(() => import("../Modal/Modal"));

const ChevronDownIcon = () => (
    <svg
        fill="#ededed"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        stroke="#ededed"
        aria-hidden="true"
    >
        <polygon points="12 20.1 0.1 8.2 2.9 5.3 12 14.4 21.1 5.3 23.9 8.2 12 20.1"></polygon>
    </svg>
);

function ComboBox({
    inputWidths = ["30%", "70%"],
    buttonWidth = "20px",
    inputHeight = "35px",
    buttonHeight = "35px",
    style = {},
    styleInput = {},
    styleButton = {},
    tableData = [],
    tableHeaders = [],
    onChange = () => { },
    nameFields = ["clientId", "clientTitle"],
    keys = ["id", "title"],
    form = {},
    ariaLabel = "Combo Box Input",
    placeholders = ["ID", "Title"],
    target = false,
    isDisabled = false
}) {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const openModal = useCallback(() => setIsModalOpen(true), []);
    const closeModal = useCallback(() => setIsModalOpen(false), []);

    const handleInputChange = useCallback((e) => {
        onChange(target ? e : e.target.value);
    }, [onChange, target]);

    const handleRowClick = useCallback(
        (data) => {
            const updatedValues = {};
            nameFields.map((name, i) => {
                updatedValues[name] = data[keys[i]];
            })
            onChange(null, updatedValues);
            closeModal();
        },
        [onChange, closeModal]
    );

    return (
        <Box position="relative" width="100%" {...style} role="combobox" aria-expanded={isModalOpen}>
            <Box display="flex" gap={2}>
                {nameFields.map((name, index) => (
                    <Input
                        key={name}
                        pr={buttonWidth}
                        height={inputHeight}
                        width={inputWidths[index]}
                        fontSize="sm"
                        {...styleInput}
                        aria-label={`${ariaLabel} - ${placeholders[index]}`}
                        onChange={handleInputChange}
                        name={name}
                        value={form[name]}
                        autoComplete="off"
                        placeholder={placeholders[index]}
                        readOnly
                        disabled={isDisabled}
                    />
                ))}

                <Button
                    colorScheme="blue"
                    className="ComboBox"
                    onClick={openModal}
                    height={buttonHeight}
                    width={buttonWidth}
                    minWidth="auto"
                    p="3px"
                    position="absolute"
                    right="0"
                    top="0"
                    aria-label="Open Combo Box Modal"
                    {...styleButton}
                    isDisabled={isDisabled}
                >
                    <ChevronDownIcon />
                </Button>
            </Box>

            {isModalOpen && (
                <Suspense fallback={''}>
                    <Modal onClose={closeModal} tableData={tableData} tableHeaders={tableHeaders} handleRowClick={handleRowClick} />
                </Suspense>
            )}
        </Box>
    );
}

ComboBox.propTypes = {
    inputWidths: PropTypes.arrayOf(PropTypes.string),
    buttonWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    inputHeight: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    buttonHeight: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    style: PropTypes.object,
    styleInput: PropTypes.object,
    styleButton: PropTypes.object,
    tableData: PropTypes.array,
    tableHeaders: PropTypes.array,
    onChange: PropTypes.func,
    nameFields: PropTypes.arrayOf(PropTypes.string),
    ariaLabel: PropTypes.string,
    placeholders: PropTypes.arrayOf(PropTypes.string),
    keys: PropTypes.arrayOf(PropTypes.string)
};

export default memo(ComboBox);
